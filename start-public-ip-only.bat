@echo off
echo ========================================
echo TechMan Public IP Setup (**************)
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo ✅ Running with administrator privileges
echo.

echo ========================================
echo Step 1: Stop All Existing Services
echo ========================================
echo.

echo Stopping all nginx processes...
taskkill /f /im nginx.exe >nul 2>&1

echo Stopping Python API...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im python3.13.exe >nul 2>&1

echo Stopping Node.js processes...
taskkill /f /im node.exe >nul 2>&1

echo ✅ All services stopped

echo.
echo ========================================
echo Step 2: Configure Firewall for Public IP
echo ========================================
echo.

echo Adding firewall rules for public IP access...

REM Port 80 for nginx
netsh advfirewall firewall delete rule name="TechMan Public Nginx" >nul 2>&1
netsh advfirewall firewall add rule name="TechMan Public Nginx" dir=in action=allow protocol=TCP localport=80
echo ✅ Port 80 configured

REM Port 5001 for Python API
netsh advfirewall firewall delete rule name="TechMan Public Python" >nul 2>&1
netsh advfirewall firewall add rule name="TechMan Public Python" dir=in action=allow protocol=TCP localport=5001
echo ✅ Port 5001 configured

REM Port 5017 for Node.js Backend
netsh advfirewall firewall delete rule name="TechMan Public Backend" >nul 2>&1
netsh advfirewall firewall add rule name="TechMan Public Backend" dir=in action=allow protocol=TCP localport=5017
echo ✅ Port 5017 configured

echo.
echo ========================================
echo Step 3: Start MongoDB
echo ========================================
echo.

echo Starting MongoDB service...
net start MongoDB >nul 2>&1
timeout /t 3 /nobreak >nul
echo ✅ MongoDB started

echo.
echo ========================================
echo Step 4: Start Python API for Public IP
echo ========================================
echo.

echo Starting Python API with public IP configuration...
cd /d "%~dp0python-model"

REM Set environment variables for public IP
set API_HOST=0.0.0.0
set PYTHON_API_PORT=5001
set CORS_ORIGINS=http://**************,http://**************:80,http://**************:5174,http://localhost:5175,http://localhost:5174

call venv\Scripts\activate.bat
start "TechMan Python API - Public" cmd /k "python single_api.py"

echo Waiting for Python API to start...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo Step 5: Start Node.js Backend for Public IP
echo ========================================
echo.

echo Starting Node.js Backend with public IP configuration...
cd /d "%~dp0server"

REM Set environment variables for public IP
set PORT=5017
set HOST=0.0.0.0
set CORS_ORIGIN=http://**************,http://**************:80

start "TechMan Backend - Public" cmd /k "npm run start"

echo Waiting for Backend to start...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo Step 6: Configure Nginx for Public IP
echo ========================================
echo.

echo Configuring nginx for public IP access...
cd /d C:\nginx

REM Create public IP nginx configuration
echo Creating public IP nginx configuration...

echo.
echo Starting nginx for public IP...
start "TechMan Nginx - Public" nginx.exe

echo Waiting for nginx to start...
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo Step 7: Test Public IP Access
echo ========================================
echo.

echo Testing services on public IP...

echo Testing Python API...
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://**************:5001/api/health' -Method Get -TimeoutSec 10; Write-Host '✅ Python API Public:' $result.status } catch { Write-Host '⚠️  Python API Public: May need router configuration' }"

echo.
echo Testing Backend API...
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://**************:5017/api/health' -Method Get -TimeoutSec 10; Write-Host '✅ Backend Public:' $result.status } catch { Write-Host '⚠️  Backend Public: May need router configuration' }"

echo.
echo Testing Nginx...
powershell -Command "try { $result = Invoke-WebRequest -Uri 'http://**************/health' -UseBasicParsing -TimeoutSec 10; Write-Host '✅ Nginx Public: Status' $result.StatusCode } catch { Write-Host '⚠️  Nginx Public: May need router configuration' }"

echo.
echo ========================================
echo ✅ Public IP Setup Complete!
echo ========================================
echo.
echo 🌐 Your TechMan Application Public URLs:
echo.
echo   Main Application:    http://**************
echo   Backend API:         http://**************/api/health
echo   Python API:          http://**************/pyapi/health
echo.
echo   Direct API Access:
echo   Backend Direct:      http://**************:5017/api/health
echo   Python Direct:       http://**************:5001/api/health
echo.
echo 🔥 Services configured for public IP access:
echo   ✅ All services bind to 0.0.0.0 (all interfaces)
echo   ✅ CORS configured for **************
echo   ✅ Firewall rules added for ports 80, 5001, 5017
echo.
echo ⚠️  Note: If services are not accessible from outside your network,
echo     you may need to configure your router to forward these ports.
echo.
pause
