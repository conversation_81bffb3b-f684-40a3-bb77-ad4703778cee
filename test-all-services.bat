@echo off
echo ========================================
echo TechMan Complete Service Test
echo ========================================
echo.

echo Testing all TechMan services...
echo.

echo ========================================
echo 1. Direct Service Tests
echo ========================================
echo.

echo Testing Python API (Direct)...
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://localhost:5001/api/health' -Method Get; Write-Host '✅ Python API Direct:' $result.status 'Port:' $result.port } catch { Write-Host '❌ Python API Direct: Failed' }"

echo.
echo Testing Node.js Backend (Direct)...
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://localhost:5017/api/health' -Method Get; Write-Host '✅ Backend Direct:' $result.status 'Message:' $result.message } catch { Write-Host '❌ Backend Direct: Failed' }"

echo.
echo ========================================
echo 2. Nginx Proxy Tests
echo ========================================
echo.

echo Testing Nginx Health...
powershell -Command "try { $result = Invoke-WebRequest -Uri 'http://localhost/health' -UseBasicParsing; Write-Host '✅ Nginx Health: Status' $result.StatusCode } catch { Write-Host '❌ Nginx Health: Failed' }"

echo.
echo Testing Backend API through Nginx...
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://localhost/api/health' -Method Get; Write-Host '✅ Backend via Nginx:' $result.status } catch { Write-Host '❌ Backend via Nginx: Failed' }"

echo.
echo Testing Python API through Nginx...
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://localhost/pyapi/health' -Method Get; Write-Host '✅ Python via Nginx:' $result.status } catch { Write-Host '❌ Python via Nginx: Failed' }"

echo.
echo ========================================
echo 3. Frontend Test
echo ========================================
echo.

echo Testing Frontend...
powershell -Command "try { $result = Invoke-WebRequest -Uri 'http://localhost/' -UseBasicParsing; Write-Host '✅ Frontend: Status' $result.StatusCode } catch { Write-Host '❌ Frontend: Failed' }"

echo.
echo ========================================
echo 4. Service Status Summary
echo ========================================
echo.

echo Checking running processes...
echo.
echo Python processes:
tasklist | findstr python

echo.
echo Node.js processes:
tasklist | findstr node

echo.
echo Nginx processes:
tasklist | findstr nginx

echo.
echo ========================================
echo 5. Port Status
echo ========================================
echo.

echo Active ports:
netstat -an | findstr ":80 :5001 :5017 :27017" | findstr LISTENING

echo.
echo ========================================
echo ✅ Test Complete!
echo ========================================
echo.
echo 🌐 Your TechMan Application URLs:
echo.
echo   Main Application:    http://localhost
echo   Backend API:         http://localhost/api/health
echo   Python API:          http://localhost/pyapi/health
echo.
echo   Direct Access:
echo   Backend Direct:      http://localhost:5017/api/health
echo   Python Direct:       http://localhost:5001/api/health
echo.
echo 📊 Service Architecture:
echo   Frontend (React) ← Nginx (Port 80) → Backend APIs
echo                                      ├── /api/* → Node.js (Port 5017)
echo                                      └── /pyapi/* → Python (Port 5001)
echo.
pause
