@echo off
echo ========================================
echo Starting TechMan Single Python API
echo ========================================
echo.

REM Set the project directory
set PROJECT_DIR=%~dp0
set PYTHON_DIR=%PROJECT_DIR%python-model

echo ✅ Project Directory: %PROJECT_DIR%
echo ✅ Python Model Directory: %PYTHON_DIR%
echo.

REM Change to python-model directory
cd /d "%PYTHON_DIR%"

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo ❌ Virtual environment not found!
    echo Please run setup-python-environment.bat first
    pause
    exit /b 1
)

echo ✅ Virtual environment found
echo.

echo ========================================
echo Activating Virtual Environment
echo ========================================
echo.

REM Activate virtual environment
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ Failed to activate virtual environment
    pause
    exit /b 1
)

echo ✅ Virtual environment activated
echo.

echo ========================================
echo Checking Dependencies
echo ========================================
echo.

REM Quick dependency check
python -c "import flask, pymongo, pandas; print('✅ Core dependencies available')"
if %errorlevel% neq 0 (
    echo ❌ Missing core dependencies. Please run setup-python-environment.bat
    pause
    exit /b 1
)

echo.
echo ========================================
echo Environment Configuration
echo ========================================
echo.

REM Set environment variables for the API (Public IP Ready)
set FLASK_APP=single_api.py
set FLASK_ENV=production
set PYTHON_API_PORT=5001
set API_HOST=0.0.0.0
set MONGODB_URI=mongodb://localhost:27017/ims
set CORS_ORIGINS=http://localhost:5175,http://localhost:5174,http://localhost:3000,http://localhost:5173,http://**************,http://**************:80,http://**************:5174

echo ✅ FLASK_APP=%FLASK_APP%
echo ✅ PYTHON_API_PORT=%PYTHON_API_PORT%
echo ✅ API_HOST=%API_HOST%
echo ✅ MONGODB_URI=%MONGODB_URI%
echo ✅ CORS_ORIGINS=%CORS_ORIGINS%
echo.

echo ========================================
echo Starting Single API Server
echo ========================================
echo.

echo 🚀 Starting TechMan Single Python API on port %PYTHON_API_PORT%...
echo.
echo 🌐 API will be available at:
echo   Local:  http://localhost:%PYTHON_API_PORT%/api/health
echo   Public: http://**************:%PYTHON_API_PORT%/api/health
echo   Internal: http://********:%PYTHON_API_PORT%/api/health
echo.
echo 📊 Available endpoints:
echo   Health Check:     /api/health
echo   Procurement:      /api/procurement/*
echo   Vendors:          /api/procurement/vendors
echo   Requests:         /api/procurement/requests
echo   Quotations:       /api/procurement/quotations/*
echo   Analysis:         /api/procurement/analyze/*
echo   Email Check:      /api/procurement/email/check-quotations
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start the single API
python single_api.py

REM If we get here, the server stopped
echo.
echo ========================================
echo Server Stopped
echo ========================================
echo.
echo The Python API server has stopped.
echo Check the output above for any error messages.
echo.
pause
