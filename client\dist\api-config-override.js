// Runtime API Configuration Override
// This script dynamically configures API URLs based on the current domain

(function() {
    'use strict';
    
    console.log('🔧 TechMan API Configuration Override Loading...');
    
    // Get current host information
    const currentHost = window.location.host;
    const currentProtocol = window.location.protocol;
    const isPublicIP = currentHost.includes('**************');
    const isPort5174 = currentHost.includes(':5174');
    
    console.log('🌐 Current Host:', currentHost);
    console.log('🔍 Is Public IP:', isPublicIP);
    console.log('🔍 Is Port 5174:', isPort5174);
    
    // Define API configurations
    let apiConfig;
    
    if (isPublicIP && isPort5174) {
        // Public IP access via port 5174 - use relative URLs through nginx proxy
        apiConfig = {
            NODE_API_BASE: '/api',
            PYTHON_API_BASE: '/pyapi',
            NODE_SERVER_URL: '',
            PYTHON_SERVER_URL: '',
            description: 'Public IP via Nginx Proxy (Port 5174)'
        };
    } else if (currentHost.includes('********')) {
        // Internal network access
        apiConfig = {
            NODE_API_BASE: '/api',
            PYTHON_API_BASE: '/pyapi', 
            NODE_SERVER_URL: '',
            PYTHON_SERVER_URL: '',
            description: 'Internal Network via Nginx Proxy'
        };
    } else {
        // Localhost or other - use relative URLs
        apiConfig = {
            NODE_API_BASE: '/api',
            PYTHON_API_BASE: '/pyapi',
            NODE_SERVER_URL: '',
            PYTHON_SERVER_URL: '',
            description: 'Local Development'
        };
    }
    
    console.log('⚙️ API Configuration:', apiConfig.description);
    console.log('📡 Node API Base:', apiConfig.NODE_API_BASE);
    console.log('🐍 Python API Base:', apiConfig.PYTHON_API_BASE);
    
    // Override fetch to automatically use correct API URLs
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
        let modifiedUrl = url;
        
        // Check if URL needs to be modified
        if (typeof url === 'string') {
            // Replace localhost URLs with relative URLs
            if (url.includes('localhost:5017')) {
                modifiedUrl = url.replace(/https?:\/\/localhost:5017/, '');
                if (!modifiedUrl.startsWith('/api')) {
                    modifiedUrl = '/api' + (modifiedUrl.startsWith('/') ? modifiedUrl : '/' + modifiedUrl);
                }
            } else if (url.includes('localhost:5001')) {
                modifiedUrl = url.replace(/https?:\/\/localhost:5001/, '');
                if (!modifiedUrl.startsWith('/pyapi')) {
                    modifiedUrl = '/pyapi' + (modifiedUrl.startsWith('/api') ? modifiedUrl.substring(4) : modifiedUrl);
                }
            } else if (url.includes('********:5017')) {
                modifiedUrl = url.replace(/https?:\/\/10\.0\.5\.4:5017/, '');
                if (!modifiedUrl.startsWith('/api')) {
                    modifiedUrl = '/api' + (modifiedUrl.startsWith('/') ? modifiedUrl : '/' + modifiedUrl);
                }
            } else if (url.includes('********:5001')) {
                modifiedUrl = url.replace(/https?:\/\/10\.0\.5\.4:5001/, '');
                if (!modifiedUrl.startsWith('/pyapi')) {
                    modifiedUrl = '/pyapi' + (modifiedUrl.startsWith('/api') ? modifiedUrl.substring(4) : modifiedUrl);
                }
            } else if (url.includes('**************:5017')) {
                modifiedUrl = url.replace(/https?:\/\/103\.255\.144\.12:5017/, '');
                if (!modifiedUrl.startsWith('/api')) {
                    modifiedUrl = '/api' + (modifiedUrl.startsWith('/') ? modifiedUrl : '/' + modifiedUrl);
                }
            } else if (url.includes('**************:5001')) {
                modifiedUrl = url.replace(/https?:\/\/103\.255\.144\.12:5001/, '');
                if (!modifiedUrl.startsWith('/pyapi')) {
                    modifiedUrl = '/pyapi' + (modifiedUrl.startsWith('/api') ? modifiedUrl.substring(4) : modifiedUrl);
                }
            }
        }
        
        if (modifiedUrl !== url) {
            console.log('🔄 API URL Override:', url, '→', modifiedUrl);
        }
        
        return originalFetch.call(this, modifiedUrl, options);
    };
    
    console.log('✅ TechMan API Configuration Override Loaded Successfully!');
    console.log('🔗 API URLs will be automatically routed through nginx proxy');
    
})();
