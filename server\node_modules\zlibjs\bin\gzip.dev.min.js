/** @license zlib.js 2012 - imaya [ https://github.com/imaya/zlib.js ] The MIT License */(function() {'use strict';var p=void 0,v=!0,da=this;function fa(f,e){var c=f.split("."),a=da;!(c[0]in a)&&a.execScript&&a.execScript("var "+c[0]);for(var b;c.length&&(b=c.shift());)!c.length&&e!==p?a[b]=e:a=a[b]?a[b]:a[b]={}};var C="undefined"!==typeof Uint8Array&&"undefined"!==typeof Uint16Array&&"undefined"!==typeof Uint32Array&&"undefined"!==typeof DataView;function F(f,e){this.index="number"===typeof e?e:0;this.f=0;this.buffer=f instanceof(C?Uint8Array:Array)?f:new (C?Uint8Array:Array)(32768);if(2*this.buffer.length<=this.index)throw Error("invalid index");this.buffer.length<=this.index&&ga(this)}function ga(f){var e=f.buffer,c,a=e.length,b=new (C?Uint8Array:Array)(a<<1);if(C)b.set(e);else for(c=0;c<a;++c)b[c]=e[c];return f.buffer=b}
F.prototype.b=function(f,e,c){var a=this.buffer,b=this.index,g=this.f,l=a[b],m;c&&1<e&&(f=8<e?(H[f&255]<<24|H[f>>>8&255]<<16|H[f>>>16&255]<<8|H[f>>>24&255])>>32-e:H[f]>>8-e);if(8>e+g)l=l<<e|f,g+=e;else for(m=0;m<e;++m)l=l<<1|f>>e-m-1&1,8===++g&&(g=0,a[b++]=H[l],l=0,b===a.length&&(a=ga(this)));a[b]=l;this.buffer=a;this.f=g;this.index=b};F.prototype.finish=function(){var f=this.buffer,e=this.index,c;0<this.f&&(f[e]<<=8-this.f,f[e]=H[f[e]],e++);C?c=f.subarray(0,e):(f.length=e,c=f);return c};
var ia=new (C?Uint8Array:Array)(256),M;for(M=0;256>M;++M){for(var N=M,S=N,ja=7,N=N>>>1;N;N>>>=1)S<<=1,S|=N&1,--ja;ia[M]=(S<<ja&255)>>>0}var H=ia;function ka(f,e,c){var a,b="number"===typeof e?e:e=0,g="number"===typeof c?c:f.length;a=-1;for(b=g&7;b--;++e)a=a>>>8^T[(a^f[e])&255];for(b=g>>3;b--;e+=8)a=a>>>8^T[(a^f[e])&255],a=a>>>8^T[(a^f[e+1])&255],a=a>>>8^T[(a^f[e+2])&255],a=a>>>8^T[(a^f[e+3])&255],a=a>>>8^T[(a^f[e+4])&255],a=a>>>8^T[(a^f[e+5])&255],a=a>>>8^T[(a^f[e+6])&255],a=a>>>8^T[(a^f[e+7])&255];return(a^4294967295)>>>0}
var la=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,
2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,
2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,
2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,
3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,
936918E3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],T=C?new Uint32Array(la):la;function U(f){this.buffer=new (C?Uint16Array:Array)(2*f);this.length=0}U.prototype.getParent=function(f){return 2*((f-2)/4|0)};U.prototype.push=function(f,e){var c,a,b=this.buffer,g;c=this.length;b[this.length++]=e;for(b[this.length++]=f;0<c;)if(a=this.getParent(c),b[c]>b[a])g=b[c],b[c]=b[a],b[a]=g,g=b[c+1],b[c+1]=b[a+1],b[a+1]=g,c=a;else break;return this.length};
U.prototype.pop=function(){var f,e,c=this.buffer,a,b,g;e=c[0];f=c[1];this.length-=2;c[0]=c[this.length];c[1]=c[this.length+1];for(g=0;;){b=2*g+2;if(b>=this.length)break;b+2<this.length&&c[b+2]>c[b]&&(b+=2);if(c[b]>c[g])a=c[g],c[g]=c[b],c[b]=a,a=c[g+1],c[g+1]=c[b+1],c[b+1]=a;else break;g=b}return{index:f,value:e,length:this.length}};function ma(f,e){this.h=na;this.j=0;this.input=C&&f instanceof Array?new Uint8Array(f):f;this.c=0;e&&(e.lazy&&(this.j=e.lazy),"number"===typeof e.compressionType&&(this.h=e.compressionType),e.outputBuffer&&(this.a=C&&e.outputBuffer instanceof Array?new Uint8Array(e.outputBuffer):e.outputBuffer),"number"===typeof e.outputIndex&&(this.c=e.outputIndex));this.a||(this.a=new (C?Uint8Array:Array)(32768))}var na=2,V=[],$;
for($=0;288>$;$++)switch(v){case 143>=$:V.push([$+48,8]);break;case 255>=$:V.push([$-144+400,9]);break;case 279>=$:V.push([$-256+0,7]);break;case 287>=$:V.push([$-280+192,8]);break;default:throw"invalid literal: "+$;}
ma.prototype.g=function(){var f,e,c,a,b=this.input;switch(this.h){case 0:c=0;for(a=b.length;c<a;){e=C?b.subarray(c,c+65535):b.slice(c,c+65535);c+=e.length;var g=e,l=c===a,m=p,d=p,h=p,s=p,x=p,n=this.a,k=this.c;if(C){for(n=new Uint8Array(this.a.buffer);n.length<=k+g.length+5;)n=new Uint8Array(n.length<<1);n.set(this.a)}m=l?1:0;n[k++]=m|0;d=g.length;h=~d+65536&65535;n[k++]=d&255;n[k++]=d>>>8&255;n[k++]=h&255;n[k++]=h>>>8&255;if(C)n.set(g,k),k+=g.length,n=n.subarray(0,k);else{s=0;for(x=g.length;s<x;++s)n[k++]=
g[s];n.length=k}this.c=k;this.a=n}break;case 1:var q=new F(C?new Uint8Array(this.a.buffer):this.a,this.c);q.b(1,1,v);q.b(1,2,v);var u=oa(this,b),w,aa,z;w=0;for(aa=u.length;w<aa;w++)if(z=u[w],F.prototype.b.apply(q,V[z]),256<z)q.b(u[++w],u[++w],v),q.b(u[++w],5),q.b(u[++w],u[++w],v);else if(256===z)break;this.a=q.finish();this.c=this.a.length;break;case na:var B=new F(C?new Uint8Array(this.a.buffer):this.a,this.c),ra,L,O,P,Q,Ha=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],W,sa,X,ta,ba,ea=Array(19),
ua,R,ca,y,va;ra=na;B.b(1,1,v);B.b(ra,2,v);L=oa(this,b);W=pa(this.n,15);sa=qa(W);X=pa(this.m,7);ta=qa(X);for(O=286;257<O&&0===W[O-1];O--);for(P=30;1<P&&0===X[P-1];P--);var wa=O,xa=P,G=new (C?Uint32Array:Array)(wa+xa),r,I,t,Y,E=new (C?Uint32Array:Array)(316),D,A,J=new (C?Uint8Array:Array)(19);for(r=I=0;r<wa;r++)G[I++]=W[r];for(r=0;r<xa;r++)G[I++]=X[r];if(!C){r=0;for(Y=J.length;r<Y;++r)J[r]=0}r=D=0;for(Y=G.length;r<Y;r+=I){for(I=1;r+I<Y&&G[r+I]===G[r];++I);t=I;if(0===G[r])if(3>t)for(;0<t--;)E[D++]=0,
J[0]++;else for(;0<t;)A=138>t?t:138,A>t-3&&A<t&&(A=t-3),10>=A?(E[D++]=17,E[D++]=A-3,J[17]++):(E[D++]=18,E[D++]=A-11,J[18]++),t-=A;else if(E[D++]=G[r],J[G[r]]++,t--,3>t)for(;0<t--;)E[D++]=G[r],J[G[r]]++;else for(;0<t;)A=6>t?t:6,A>t-3&&A<t&&(A=t-3),E[D++]=16,E[D++]=A-3,J[16]++,t-=A}f=C?E.subarray(0,D):E.slice(0,D);ba=pa(J,7);for(y=0;19>y;y++)ea[y]=ba[Ha[y]];for(Q=19;4<Q&&0===ea[Q-1];Q--);ua=qa(ba);B.b(O-257,5,v);B.b(P-1,5,v);B.b(Q-4,4,v);for(y=0;y<Q;y++)B.b(ea[y],3,v);y=0;for(va=f.length;y<va;y++)if(R=
f[y],B.b(ua[R],ba[R],v),16<=R){y++;switch(R){case 16:ca=2;break;case 17:ca=3;break;case 18:ca=7;break;default:throw"invalid code: "+R;}B.b(f[y],ca,v)}var ya=[sa,W],za=[ta,X],K,Aa,Z,ha,Ba,Ca,Da,Ea;Ba=ya[0];Ca=ya[1];Da=za[0];Ea=za[1];K=0;for(Aa=L.length;K<Aa;++K)if(Z=L[K],B.b(Ba[Z],Ca[Z],v),256<Z)B.b(L[++K],L[++K],v),ha=L[++K],B.b(Da[ha],Ea[ha],v),B.b(L[++K],L[++K],v);else if(256===Z)break;this.a=B.finish();this.c=this.a.length;break;default:throw"invalid compression type";}return this.a};
function Fa(f,e){this.length=f;this.k=e}
var Ga=function(){function f(b){switch(v){case 3===b:return[257,b-3,0];case 4===b:return[258,b-4,0];case 5===b:return[259,b-5,0];case 6===b:return[260,b-6,0];case 7===b:return[261,b-7,0];case 8===b:return[262,b-8,0];case 9===b:return[263,b-9,0];case 10===b:return[264,b-10,0];case 12>=b:return[265,b-11,1];case 14>=b:return[266,b-13,1];case 16>=b:return[267,b-15,1];case 18>=b:return[268,b-17,1];case 22>=b:return[269,b-19,2];case 26>=b:return[270,b-23,2];case 30>=b:return[271,b-27,2];case 34>=b:return[272,
b-31,2];case 42>=b:return[273,b-35,3];case 50>=b:return[274,b-43,3];case 58>=b:return[275,b-51,3];case 66>=b:return[276,b-59,3];case 82>=b:return[277,b-67,4];case 98>=b:return[278,b-83,4];case 114>=b:return[279,b-99,4];case 130>=b:return[280,b-115,4];case 162>=b:return[281,b-131,5];case 194>=b:return[282,b-163,5];case 226>=b:return[283,b-195,5];case 257>=b:return[284,b-227,5];case 258===b:return[285,b-258,0];default:throw"invalid length: "+b;}}var e=[],c,a;for(c=3;258>=c;c++)a=f(c),e[c]=a[2]<<24|
a[1]<<16|a[0];return e}(),Ia=C?new Uint32Array(Ga):Ga;
function oa(f,e){function c(b,e){var a=b.k,c=[],g=0,f;f=Ia[b.length];c[g++]=f&65535;c[g++]=f>>16&255;c[g++]=f>>24;var d;switch(v){case 1===a:d=[0,a-1,0];break;case 2===a:d=[1,a-2,0];break;case 3===a:d=[2,a-3,0];break;case 4===a:d=[3,a-4,0];break;case 6>=a:d=[4,a-5,1];break;case 8>=a:d=[5,a-7,1];break;case 12>=a:d=[6,a-9,2];break;case 16>=a:d=[7,a-13,2];break;case 24>=a:d=[8,a-17,3];break;case 32>=a:d=[9,a-25,3];break;case 48>=a:d=[10,a-33,4];break;case 64>=a:d=[11,a-49,4];break;case 96>=a:d=[12,a-
65,5];break;case 128>=a:d=[13,a-97,5];break;case 192>=a:d=[14,a-129,6];break;case 256>=a:d=[15,a-193,6];break;case 384>=a:d=[16,a-257,7];break;case 512>=a:d=[17,a-385,7];break;case 768>=a:d=[18,a-513,8];break;case 1024>=a:d=[19,a-769,8];break;case 1536>=a:d=[20,a-1025,9];break;case 2048>=a:d=[21,a-1537,9];break;case 3072>=a:d=[22,a-2049,10];break;case 4096>=a:d=[23,a-3073,10];break;case 6144>=a:d=[24,a-4097,11];break;case 8192>=a:d=[25,a-6145,11];break;case 12288>=a:d=[26,a-8193,12];break;case 16384>=
a:d=[27,a-12289,12];break;case 24576>=a:d=[28,a-16385,13];break;case 32768>=a:d=[29,a-24577,13];break;default:throw"invalid distance";}f=d;c[g++]=f[0];c[g++]=f[1];c[g++]=f[2];var h,l;h=0;for(l=c.length;h<l;++h)n[k++]=c[h];u[c[0]]++;w[c[3]]++;q=b.length+e-1;x=null}var a,b,g,l,m,d={},h,s,x,n=C?new Uint16Array(2*e.length):[],k=0,q=0,u=new (C?Uint32Array:Array)(286),w=new (C?Uint32Array:Array)(30),aa=f.j,z;if(!C){for(g=0;285>=g;)u[g++]=0;for(g=0;29>=g;)w[g++]=0}u[256]=1;a=0;for(b=e.length;a<b;++a){g=
m=0;for(l=3;g<l&&a+g!==b;++g)m=m<<8|e[a+g];d[m]===p&&(d[m]=[]);h=d[m];if(!(0<q--)){for(;0<h.length&&32768<a-h[0];)h.shift();if(a+3>=b){x&&c(x,-1);g=0;for(l=b-a;g<l;++g)z=e[a+g],n[k++]=z,++u[z];break}0<h.length?(s=Ja(e,a,h),x?x.length<s.length?(z=e[a-1],n[k++]=z,++u[z],c(s,0)):c(x,-1):s.length<aa?x=s:c(s,0)):x?c(x,-1):(z=e[a],n[k++]=z,++u[z])}h.push(a)}n[k++]=256;u[256]++;f.n=u;f.m=w;return C?n.subarray(0,k):n}
function Ja(f,e,c){var a,b,g=0,l,m,d,h,s=f.length;m=0;h=c.length;a:for(;m<h;m++){a=c[h-m-1];l=3;if(3<g){for(d=g;3<d;d--)if(f[a+d-1]!==f[e+d-1])continue a;l=g}for(;258>l&&e+l<s&&f[a+l]===f[e+l];)++l;l>g&&(b=a,g=l);if(258===l)break}return new Fa(g,e-b)}
function pa(f,e){var c=f.length,a=new U(572),b=new (C?Uint8Array:Array)(c),g,l,m,d,h;if(!C)for(d=0;d<c;d++)b[d]=0;for(d=0;d<c;++d)0<f[d]&&a.push(d,f[d]);g=Array(a.length/2);l=new (C?Uint32Array:Array)(a.length/2);if(1===g.length)return b[a.pop().index]=1,b;d=0;for(h=a.length/2;d<h;++d)g[d]=a.pop(),l[d]=g[d].value;m=Ka(l,l.length,e);d=0;for(h=g.length;d<h;++d)b[g[d].index]=m[d];return b}
function Ka(f,e,c){function a(b){var c=d[b][h[b]];c===e?(a(b+1),a(b+1)):--l[c];++h[b]}var b=new (C?Uint16Array:Array)(c),g=new (C?Uint8Array:Array)(c),l=new (C?Uint8Array:Array)(e),m=Array(c),d=Array(c),h=Array(c),s=(1<<c)-e,x=1<<c-1,n,k,q,u,w;b[c-1]=e;for(k=0;k<c;++k)s<x?g[k]=0:(g[k]=1,s-=x),s<<=1,b[c-2-k]=(b[c-1-k]/2|0)+e;b[0]=g[0];m[0]=Array(b[0]);d[0]=Array(b[0]);for(k=1;k<c;++k)b[k]>2*b[k-1]+g[k]&&(b[k]=2*b[k-1]+g[k]),m[k]=Array(b[k]),d[k]=Array(b[k]);for(n=0;n<e;++n)l[n]=c;for(q=0;q<b[c-1];++q)m[c-
1][q]=f[q],d[c-1][q]=q;for(n=0;n<c;++n)h[n]=0;1===g[c-1]&&(--l[0],++h[c-1]);for(k=c-2;0<=k;--k){u=n=0;w=h[k+1];for(q=0;q<b[k];q++)u=m[k+1][w]+m[k+1][w+1],u>f[n]?(m[k][q]=u,d[k][q]=e,w+=2):(m[k][q]=f[n],d[k][q]=n,++n);h[k]=0;1===g[k]&&a(k)}return l}
function qa(f){var e=new (C?Uint16Array:Array)(f.length),c=[],a=[],b=0,g,l,m,d;g=0;for(l=f.length;g<l;g++)c[f[g]]=(c[f[g]]|0)+1;g=1;for(l=16;g<=l;g++)a[g]=b,b+=c[g]|0,b<<=1;g=0;for(l=f.length;g<l;g++){b=a[f[g]];a[f[g]]+=1;m=e[g]=0;for(d=f[g];m<d;m++)e[g]=e[g]<<1|b&1,b>>>=1}return e};function La(f,e){this.input=f;this.c=this.i=0;this.d={};e&&(e.flags&&(this.d=e.flags),"string"===typeof e.filename&&(this.filename=e.filename),"string"===typeof e.comment&&(this.l=e.comment),e.deflateOptions&&(this.e=e.deflateOptions));this.e||(this.e={})}
La.prototype.g=function(){var f,e,c,a,b,g,l,m,d=new (C?Uint8Array:Array)(32768),h=0,s=this.input,x=this.i,n=this.filename,k=this.l;d[h++]=31;d[h++]=139;d[h++]=8;f=0;this.d.fname&&(f|=Ma);this.d.fcomment&&(f|=Na);this.d.fhcrc&&(f|=Oa);d[h++]=f;e=(Date.now?Date.now():+new Date)/1E3|0;d[h++]=e&255;d[h++]=e>>>8&255;d[h++]=e>>>16&255;d[h++]=e>>>24&255;d[h++]=0;d[h++]=Pa;if(this.d.fname!==p){l=0;for(m=n.length;l<m;++l)g=n.charCodeAt(l),255<g&&(d[h++]=g>>>8&255),d[h++]=g&255;d[h++]=0}if(this.d.comment){l=
0;for(m=k.length;l<m;++l)g=k.charCodeAt(l),255<g&&(d[h++]=g>>>8&255),d[h++]=g&255;d[h++]=0}this.d.fhcrc&&(c=ka(d,0,h)&65535,d[h++]=c&255,d[h++]=c>>>8&255);this.e.outputBuffer=d;this.e.outputIndex=h;b=new ma(s,this.e);d=b.g();h=b.c;C&&(h+8>d.buffer.byteLength?(this.a=new Uint8Array(h+8),this.a.set(new Uint8Array(d.buffer)),d=this.a):d=new Uint8Array(d.buffer));a=ka(s,p,p);d[h++]=a&255;d[h++]=a>>>8&255;d[h++]=a>>>16&255;d[h++]=a>>>24&255;m=s.length;d[h++]=m&255;d[h++]=m>>>8&255;d[h++]=m>>>16&255;d[h++]=
m>>>24&255;this.i=x;C&&h<d.length&&(this.a=d=d.subarray(0,h));return d};var Pa=255,Oa=2,Ma=8,Na=16;fa("Zlib.Gzip",La);fa("Zlib.Gzip.prototype.compress",La.prototype.g);}).call(this);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYmluL2d6aXAubWluLmpzIiwibGluZUNvdW50IjozMiwibWFwcGluZ3MiOiJBLG1IQUFBLGtCQTRDQUEsR0FBYyxJQTBIS0MsU0FBUSxHQUFBLENBQUNDLENBQUQsQ0FBT0MsQ0FBUCxDQUF5QyxDQUNsRSxJQUFJQyxFQUFRRixDQUFBRyxNQUFBLENBQVcsR0FBWCxDQUFaLENBQ0lDLEVBQThCTixFQUs5QixHQUFFSSxDQUFBLENBQU0sQ0FBTixDQUFGLEVBQWNFLEVBQWQsQ0FBSixFQUEwQkEsQ0FBQUMsV0FBMUIsRUFDRUQsQ0FBQUMsV0FBQSxDQUFlLE1BQWYsQ0FBd0JILENBQUEsQ0FBTSxDQUFOLENBQXhCLENBU0YsS0FBSyxJQUFJSSxDQUFULENBQWVKLENBQUFLLE9BQWYsR0FBZ0NELENBQWhDLENBQXVDSixDQUFBTSxNQUFBLEVBQXZDLEVBQUEsQ0FDTSxDQUFDTixDQUFBSyxPQUFMLEVBQWdDTixDQUFoQyxHQXlqQmFRLENBempCYixDQUVFTCxDQUFBLENBQUlFLENBQUosQ0FGRixDQUVjTCxDQUZkLENBSUVHLENBSkYsQ0FHV0EsQ0FBQSxDQUFJRSxDQUFKLENBQUosQ0FDQ0YsQ0FBQSxDQUFJRSxDQUFKLENBREQsQ0FHQ0YsQ0FBQSxDQUFJRSxDQUFKLENBSEQsQ0FHYSxFQXhCNEMsQyxDQzVKcEUsSUFBSUksRUFDcUIsV0FEckJBLEdBQ0QsTUFBT0MsV0FETkQsRUFFc0IsV0FGdEJBLEdBRUQsTUFBT0UsWUFGTkYsRUFHc0IsV0FIdEJBLEdBR0QsTUFBT0csWUFITkgsRUFJbUIsV0FKbkJBLEdBSUQsTUFBT0ksUyxDQ0NPQyxRQUFRLEVBQUEsQ0FBQ0MsQ0FBRCxDQUFTQyxDQUFULENBQXlCLENBRWhELElBQUFDLE1BQUEsQ0FBdUMsUUFBMUIsR0FBQSxNQUFPRCxFQUFQLENBQXFDQSxDQUFyQyxDQUFzRCxDQUVuRSxLQUFBRSxFQUFBLENBQWdCLENBRWhCLEtBQUFILE9BQUEsQ0FBY0EsQ0FBQSxZQUFtQk4sQ0FBQSxDQUFpQkMsVUFBakIsQ0FBOEJTLEtBQWpELEVBQ1pKLENBRFksQ0FFWixLQUFLTixDQUFBLENBQWlCQyxVQUFqQixDQUE4QlMsS0FBbkMsRUFlOEJDLEtBZjlCLENBR0YsSUFBeUIsQ0FBekIsQ0FBSSxJQUFBTCxPQUFBVCxPQUFKLEVBQThCLElBQUFXLE1BQTlCLENBQ0UsS0FBVUksTUFBSixDQUFVLGVBQVYsQ0FBTixDQUNTLElBQUFOLE9BQUFULE9BQUosRUFBMEIsSUFBQVcsTUFBMUIsRUFDTEssRUFBQSxDQUFBQSxJQUFBLENBZDhDLENBNkJWQyxRQUFRLEdBQUEsQ0FBUkEsQ0FBUSxDQUFHLENBRWpELElBQUlDLEVBQVMsQ0FBQVQsT0FBYixDQUVJVSxDQUZKLENBSUlDLEVBQUtGLENBQUFsQixPQUpULENBTUlTLEVBQ0YsS0FBS04sQ0FBQSxDQUFpQkMsVUFBakIsQ0FBOEJTLEtBQW5DLEVBQTBDTyxDQUExQyxFQUFnRCxDQUFoRCxDQUdGLElBQUlqQixDQUFKLENBQ0VNLENBQUFZLElBQUEsQ0FBV0gsQ0FBWCxDQURGLEtBSUUsS0FBS0MsQ0FBTCxDQUFTLENBQVQsQ0FBWUEsQ0FBWixDQUFnQkMsQ0FBaEIsQ0FBb0IsRUFBRUQsQ0FBdEIsQ0FDRVYsQ0FBQSxDQUFPVSxDQUFQLENBQUEsQ0FBWUQsQ0FBQSxDQUFPQyxDQUFQLENBSWhCLE9BQVEsRUFBQVYsT0FBUixDQUFzQkEsQ0FyQjJCO0FBK0JuREQsQ0FBQWMsVUFBQUMsRUFBQSxDQUFxQ0MsUUFBUSxDQUFDQyxDQUFELENBQVNDLENBQVQsQ0FBWUMsQ0FBWixDQUFxQixDQUNoRSxJQUFJbEIsRUFBUyxJQUFBQSxPQUFiLENBQ0lFLEVBQVEsSUFBQUEsTUFEWixDQUVJQyxFQUFXLElBQUFBLEVBRmYsQ0FLSWdCLEVBQVVuQixDQUFBLENBQU9FLENBQVAsQ0FMZCxDQU9JUSxDQWVBUSxFQUFKLEVBQW1CLENBQW5CLENBQWVELENBQWYsR0FDRUQsQ0FERixDQUNlLENBQUosQ0FBQUMsQ0FBQSxFQVBERyxDQUFBLENBUUNKLENBUkQsQ0FBZ0MsR0FBaEMsQ0FPQyxFQVB3QyxFQU94QyxDQU5OSSxDQUFBLENBT01KLENBUE4sR0FBa0MsQ0FBbEMsQ0FBc0MsR0FBdEMsQ0FNTSxFQU55QyxFQU16QyxDQUxOSSxDQUFBLENBTU1KLENBTk4sR0FBa0MsRUFBbEMsQ0FBdUMsR0FBdkMsQ0FLTSxFQUwwQyxDQUsxQyxDQUpQSSxDQUFBLENBS09KLENBTFAsR0FBa0MsRUFBbEMsQ0FBdUMsR0FBdkMsQ0FJTyxHQUNZLEVBRFosQ0FDaUJDLENBRGpCLENBRVBHLENBQUEsQ0FBNEJKLENBQTVCLENBRk8sRUFFaUMsQ0FGakMsQ0FFcUNDLENBSGhELENBT0EsSUFBbUIsQ0FBbkIsQ0FBSUEsQ0FBSixDQUFRZCxDQUFSLENBQ0VnQixDQUNBLENBRFdBLENBQ1gsRUFEc0JGLENBQ3RCLENBRDJCRCxDQUMzQixDQUFBYixDQUFBLEVBQVljLENBRmQsS0FLRSxLQUFLUCxDQUFMLENBQVMsQ0FBVCxDQUFZQSxDQUFaLENBQWdCTyxDQUFoQixDQUFtQixFQUFFUCxDQUFyQixDQUNFUyxDQUdBLENBSFdBLENBR1gsRUFIc0IsQ0FHdEIsQ0FINkJILENBRzdCLEVBSHVDQyxDQUd2QyxDQUgyQ1AsQ0FHM0MsQ0FIK0MsQ0FHL0MsQ0FIb0QsQ0FHcEQsQ0FBbUIsQ0FBbkIsR0FBSSxFQUFFUCxDQUFOLEdBQ0VBLENBS0EsQ0FMVyxDQUtYLENBSkFILENBQUEsQ0FBT0UsQ0FBQSxFQUFQLENBSUEsQ0FKa0JrQixDQUFBLENBQTRCRCxDQUE1QixDQUlsQixDQUhBQSxDQUdBLENBSFUsQ0FHVixDQUFJakIsQ0FBSixHQUFjRixDQUFBVCxPQUFkLEdBQ0VTLENBREYsQ0FDV08sRUFBQSxDQUFBQSxJQUFBLENBRFgsQ0FORixDQVlKUCxFQUFBLENBQU9FLENBQVAsQ0FBQSxDQUFnQmlCLENBRWhCLEtBQUFuQixPQUFBLENBQWNBLENBQ2QsS0FBQUcsRUFBQSxDQUFnQkEsQ0FDaEIsS0FBQUQsTUFBQSxDQUFhQSxDQXZEbUQsQ0ErRGxFSCxFQUFBYyxVQUFBUSxPQUFBLENBQWtDQyxRQUFRLEVBQUcsQ0FDM0MsSUFBSXRCLEVBQVMsSUFBQUEsT0FBYixDQUNJRSxFQUFRLElBQUFBLE1BRFosQ0FJSXFCLENBR2dCLEVBQXBCLENBQUksSUFBQXBCLEVBQUosR0FDRUgsQ0FBQSxDQUFPRSxDQUFQLENBRUEsR0FGa0IsQ0FFbEIsQ0FGc0IsSUFBQUMsRUFFdEIsQ0FEQUgsQ0FBQSxDQUFPRSxDQUFQLENBQ0EsQ0FEZ0JrQixDQUFBLENBQTRCcEIsQ0FBQSxDQUFPRSxDQUFQLENBQTVCLENBQ2hCLENBQUFBLENBQUEsRUFIRixDQU9JUixFQUFKLENBQ0U2QixDQURGLENBQ1d2QixDQUFBd0IsU0FBQSxDQUFnQixDQUFoQixDQUFtQnRCLENBQW5CLENBRFgsRUFHRUYsQ0FBQVQsT0FDQSxDQURnQlcsQ0FDaEIsQ0FBQXFCLENBQUEsQ0FBU3ZCLENBSlgsQ0FPQSxPQUFPdUIsRUF0Qm9DLENBa0MzQztJQUFJRSxHQUFRLEtBQUsvQixDQUFBLENBQWlCQyxVQUFqQixDQUE4QlMsS0FBbkMsRUFBMEMsR0FBMUMsQ0FBWixDQUVJTSxDQUdKLEtBQUtBLENBQUwsQ0FBUyxDQUFULENBQWdCLEdBQWhCLENBQVlBLENBQVosQ0FBcUIsRUFBRUEsQ0FBdkIsQ0FBMEIsQ0FLdEIsSUFPQ0EsSUFBQUEsRUFBQUEsQ0FBQUEsQ0FWR2dCLEVBQUlULENBVVBQLENBVEdpQixHQUFJLENBU1BqQixDQVBJTyxFQUFBQSxDQUFBQSxHQUFPLENBQVosQ0FBZUEsQ0FBZixDQUFrQkEsQ0FBbEIsSUFBeUIsQ0FBekIsQ0FDRVMsQ0FFQSxHQUZNLENBRU4sQ0FEQUEsQ0FDQSxFQURLVCxDQUNMLENBRFMsQ0FDVCxDQUFBLEVBQUVVLEVBUE5GLEdBQUEsQ0FBTWYsQ0FBTixDQUFBLEVBVVVnQixDQVZWLEVBVWVDLEVBVmYsQ0FVbUIsR0FWbkIsSUFVNkIsQ0FYTCxDQVQ1QixJQUFBUCxFQXdCU0ssRSxDQ2pLV0csUUFBUSxHQUFBLENBQUNDLENBQUQsQ0FBWUMsQ0FBWixDQUFpQnZDLENBQWpCLENBQXlCLENBWHBCLElBQUEsQ0FBQSxDQWEzQm1CLEVBQW9CLFFBQWYsR0FBQSxNQUFPb0IsRUFBUCxDQUEyQkEsQ0FBM0IsQ0FBa0NBLENBQWxDLENBQXdDLENBYmxCLENBYzNCbkIsRUFBd0IsUUFBbEIsR0FBQSxNQUFPcEIsRUFBUCxDQUE4QkEsQ0FBOUIsQ0FBdUNzQyxDQUFBdEMsT0FFakR3QyxFQUFBLENBQUEsRUFHQSxLQUFLckIsQ0FBTCxDQUFTQyxDQUFULENBQWMsQ0FBZCxDQUFpQkQsQ0FBQSxFQUFqQixDQUFzQixFQUFFb0IsQ0FBeEIsQ0FDRUMsQ0FBQSxDQUFPQSxDQUFQLEdBQWUsQ0FBZixDQVJVQyxDQVFVLEVBQU9ELENBQVAsQ0FBYUYsQ0FBQSxDQUFLQyxDQUFMLENBQWIsRUFBMEIsR0FBMUIsQ0FFdEIsS0FBS3BCLENBQUwsQ0FBU0MsQ0FBVCxFQUFlLENBQWYsQ0FBa0JELENBQUEsRUFBbEIsQ0FBdUJvQixDQUF2QixFQUE4QixDQUE5QixDQUNFQyxDQU9BLENBUE9BLENBT1AsR0FQZSxDQU9mLENBbEJVQyxDQVdVLEVBQU9ELENBQVAsQ0FBYUYsQ0FBQSxDQUFLQyxDQUFMLENBQWIsRUFBOEIsR0FBOUIsQ0FPcEIsQ0FOQUMsQ0FNQSxDQU5PQSxDQU1QLEdBTmUsQ0FNZixDQWxCVUMsQ0FZVSxFQUFPRCxDQUFQLENBQWFGLENBQUEsQ0FBS0MsQ0FBTCxDQUFXLENBQVgsQ0FBYixFQUE4QixHQUE5QixDQU1wQixDQUxBQyxDQUtBLENBTE9BLENBS1AsR0FMZSxDQUtmLENBbEJVQyxDQWFVLEVBQU9ELENBQVAsQ0FBYUYsQ0FBQSxDQUFLQyxDQUFMLENBQVcsQ0FBWCxDQUFiLEVBQThCLEdBQTlCLENBS3BCLENBSkFDLENBSUEsQ0FKT0EsQ0FJUCxHQUplLENBSWYsQ0FsQlVDLENBY1UsRUFBT0QsQ0FBUCxDQUFhRixDQUFBLENBQUtDLENBQUwsQ0FBVyxDQUFYLENBQWIsRUFBOEIsR0FBOUIsQ0FJcEIsQ0FIQUMsQ0FHQSxDQUhPQSxDQUdQLEdBSGUsQ0FHZixDQWxCVUMsQ0FlVSxFQUFPRCxDQUFQLENBQWFGLENBQUEsQ0FBS0MsQ0FBTCxDQUFXLENBQVgsQ0FBYixFQUE4QixHQUE5QixDQUdwQixDQUZBQyxDQUVBLENBRk9BLENBRVAsR0FGZSxDQUVmLENBbEJVQyxDQWdCVSxFQUFPRCxDQUFQLENBQWFGLENBQUEsQ0FBS0MsQ0FBTCxDQUFXLENBQVgsQ0FBYixFQUE4QixHQUE5QixDQUVwQixDQURBQyxDQUNBLENBRE9BLENBQ1AsR0FEZSxDQUNmLENBbEJVQyxDQWlCVSxFQUFPRCxDQUFQLENBQWFGLENBQUEsQ0FBS0MsQ0FBTCxDQUFXLENBQVgsQ0FBYixFQUE4QixHQUE5QixDQUNwQixDQUFBQyxDQUFBLENBQU9BLENBQVAsR0FBZSxDQUFmLENBbEJVQyxDQWtCVSxFQUFPRCxDQUFQLENBQWFGLENBQUEsQ0FBS0MsQ0FBTCxDQUFXLENBQVgsQ0FBYixFQUE4QixHQUE5QixDQUd0QixRQUFRQyxDQUFSLENBQWMsVUFBZCxJQUE4QixDQXRCcUI7QUF1Q3JELElBQUFFLEdBQW9CLENBQ2xCLENBRGtCLENBQ04sVUFETSxDQUNNLFVBRE4sQ0FDa0IsVUFEbEIsQ0FDOEIsU0FEOUIsQ0FDMEMsVUFEMUMsQ0FFbEIsVUFGa0IsQ0FFTixVQUZNLENBRU0sU0FGTixDQUVrQixVQUZsQixDQUU4QixVQUY5QixDQUUwQyxVQUYxQyxDQUdsQixTQUhrQixDQUdOLFVBSE0sQ0FHTSxVQUhOLENBR2tCLFVBSGxCLENBRzhCLFNBSDlCLENBRzBDLFVBSDFDLENBSWxCLFVBSmtCLENBSU4sVUFKTSxDQUlNLFNBSk4sQ0FJa0IsVUFKbEIsQ0FJOEIsVUFKOUIsQ0FJMEMsVUFKMUMsQ0FLbEIsU0FMa0IsQ0FLTixVQUxNLENBS00sVUFMTixDQUtrQixVQUxsQixDQUs4QixTQUw5QixDQUswQyxVQUwxQyxDQU1sQixVQU5rQixDQU1OLFVBTk0sQ0FNTSxTQU5OLENBTWtCLFVBTmxCLENBTThCLFVBTjlCLENBTTBDLFVBTjFDLENBT2xCLFVBUGtCLENBT04sVUFQTSxDQU9NLFVBUE4sQ0FPa0IsVUFQbEIsQ0FPOEIsU0FQOUIsQ0FPMEMsVUFQMUMsQ0FRbEIsVUFSa0IsQ0FRTixVQVJNLENBUU0sU0FSTixDQVFrQixVQVJsQixDQVE4QixVQVI5QjtBQVEwQyxVQVIxQyxDQVNsQixTQVRrQixDQVNOLFVBVE0sQ0FTTSxVQVROLENBU2tCLFVBVGxCLENBUzhCLFNBVDlCLENBUzBDLFVBVDFDLENBVWxCLFVBVmtCLENBVU4sVUFWTSxDQVVNLFNBVk4sQ0FVa0IsVUFWbEIsQ0FVOEIsVUFWOUIsQ0FVMEMsVUFWMUMsQ0FXbEIsU0FYa0IsQ0FXTixVQVhNLENBV00sVUFYTixDQVdrQixVQVhsQixDQVc4QixVQVg5QixDQVcwQyxRQVgxQyxDQVlsQixVQVprQixDQVlOLFVBWk0sQ0FZTSxVQVpOLENBWWtCLFNBWmxCLENBWThCLFVBWjlCLENBWTBDLFVBWjFDLENBYWxCLFVBYmtCLENBYU4sU0FiTSxDQWFNLFVBYk4sQ0Fha0IsVUFibEIsQ0FhOEIsVUFiOUIsQ0FhMEMsU0FiMUMsQ0FjbEIsVUFka0IsQ0FjTixVQWRNLENBY00sVUFkTixDQWNrQixTQWRsQixDQWM4QixVQWQ5QixDQWMwQyxVQWQxQyxDQWVsQixVQWZrQixDQWVOLFNBZk0sQ0FlTSxVQWZOLENBZWtCLFVBZmxCLENBZThCLFVBZjlCLENBZTBDLFNBZjFDLENBZ0JsQixVQWhCa0IsQ0FnQk4sVUFoQk0sQ0FnQk0sVUFoQk4sQ0FnQmtCLFNBaEJsQjtBQWdCOEIsVUFoQjlCLENBZ0IwQyxVQWhCMUMsQ0FpQmxCLFVBakJrQixDQWlCTixTQWpCTSxDQWlCTSxVQWpCTixDQWlCa0IsVUFqQmxCLENBaUI4QixVQWpCOUIsQ0FpQjBDLFVBakIxQyxDQWtCbEIsVUFsQmtCLENBa0JOLFVBbEJNLENBa0JNLFVBbEJOLENBa0JrQixTQWxCbEIsQ0FrQjhCLFVBbEI5QixDQWtCMEMsVUFsQjFDLENBbUJsQixVQW5Ca0IsQ0FtQk4sU0FuQk0sQ0FtQk0sVUFuQk4sQ0FtQmtCLFVBbkJsQixDQW1COEIsVUFuQjlCLENBbUIwQyxTQW5CMUMsQ0FvQmxCLFVBcEJrQixDQW9CTixVQXBCTSxDQW9CTSxVQXBCTixDQW9Ca0IsU0FwQmxCLENBb0I4QixVQXBCOUIsQ0FvQjBDLFVBcEIxQyxDQXFCbEIsVUFyQmtCLENBcUJOLFNBckJNLENBcUJNLFVBckJOLENBcUJrQixVQXJCbEIsQ0FxQjhCLFVBckI5QixDQXFCMEMsU0FyQjFDLENBc0JsQixVQXRCa0IsQ0FzQk4sVUF0Qk0sQ0FzQk0sVUF0Qk4sQ0FzQmtCLFVBdEJsQixDQXNCOEIsUUF0QjlCLENBc0IwQyxVQXRCMUMsQ0F1QmxCLFVBdkJrQixDQXVCTixVQXZCTSxDQXVCTSxRQXZCTixDQXVCa0IsVUF2QmxCLENBdUI4QixVQXZCOUIsQ0F1QjBDLFVBdkIxQyxDQXdCbEIsU0F4QmtCLENBd0JOLFVBeEJNLENBd0JNLFVBeEJOO0FBd0JrQixVQXhCbEIsQ0F3QjhCLFNBeEI5QixDQXdCMEMsVUF4QjFDLENBeUJsQixVQXpCa0IsQ0F5Qk4sVUF6Qk0sQ0F5Qk0sU0F6Qk4sQ0F5QmtCLFVBekJsQixDQXlCOEIsVUF6QjlCLENBeUIwQyxVQXpCMUMsQ0EwQmxCLFNBMUJrQixDQTBCTixVQTFCTSxDQTBCTSxVQTFCTixDQTBCa0IsVUExQmxCLENBMEI4QixTQTFCOUIsQ0EwQjBDLFVBMUIxQyxDQTJCbEIsVUEzQmtCLENBMkJOLFVBM0JNLENBMkJNLFNBM0JOLENBMkJrQixVQTNCbEIsQ0EyQjhCLFVBM0I5QixDQTJCMEMsVUEzQjFDLENBNEJsQixTQTVCa0IsQ0E0Qk4sVUE1Qk0sQ0E0Qk0sVUE1Qk4sQ0E0QmtCLFVBNUJsQixDQTRCOEIsVUE1QjlCLENBNEIwQyxVQTVCMUMsQ0E2QmxCLFVBN0JrQixDQTZCTixVQTdCTSxDQTZCTSxTQTdCTixDQTZCa0IsVUE3QmxCLENBNkI4QixVQTdCOUIsQ0E2QjBDLFVBN0IxQyxDQThCbEIsU0E5QmtCLENBOEJOLFVBOUJNLENBOEJNLFVBOUJOLENBOEJrQixVQTlCbEIsQ0E4QjhCLFNBOUI5QixDQThCMEMsVUE5QjFDLENBK0JsQixVQS9Ca0IsQ0ErQk4sVUEvQk0sQ0ErQk0sU0EvQk4sQ0ErQmtCLFVBL0JsQixDQStCOEIsVUEvQjlCLENBK0IwQyxVQS9CMUMsQ0FnQ2xCLFNBaENrQixDQWdDTixVQWhDTTtBQWdDTSxVQWhDTixDQWdDa0IsVUFoQ2xCLENBZ0M4QixTQWhDOUIsQ0FnQzBDLFVBaEMxQyxDQWlDbEIsVUFqQ2tCLENBaUNOLFVBakNNLENBaUNNLFVBakNOLENBaUNrQixRQWpDbEIsQ0FpQzhCLFVBakM5QixDQWlDMEMsVUFqQzFDLENBa0NsQixVQWxDa0IsQ0FrQ04sUUFsQ00sQ0FrQ00sVUFsQ04sQ0FrQ2tCLFVBbENsQixDQWtDOEIsVUFsQzlCLENBa0MwQyxTQWxDMUMsQ0FtQ2xCLFVBbkNrQixDQW1DTixVQW5DTSxDQW1DTSxVQW5DTixDQW1Da0IsU0FuQ2xCLENBbUM4QixVQW5DOUIsQ0FtQzBDLFVBbkMxQyxDQW9DbEIsVUFwQ2tCLENBb0NOLFNBcENNLENBb0NNLFVBcENOLENBb0NrQixVQXBDbEIsQ0FvQzhCLFVBcEM5QixDQW9DMEMsU0FwQzFDLENBcUNsQixVQXJDa0IsQ0FxQ04sVUFyQ00sQ0FxQ00sVUFyQ04sQ0FxQ2tCLFNBckNsQixDQXFDOEIsVUFyQzlCLENBcUMwQyxVQXJDMUMsQ0FzQ2xCLFVBdENrQixDQXNDTixTQXRDTSxDQXNDTSxVQXRDTixDQXNDa0IsVUF0Q2xCLENBc0M4QixVQXRDOUIsQ0FzQzBDLFNBdEMxQyxDQXVDbEIsVUF2Q2tCLENBdUNOLFVBdkNNLENBdUNNLFVBdkNOLENBdUNrQixVQXZDbEIsQ0F1QzhCLFVBdkM5QixDQXVDMEMsVUF2QzFDLENBd0NsQixVQXhDa0I7QUF3Q04sUUF4Q00sQ0F3Q00sVUF4Q04sQ0F3Q2tCLFVBeENsQixDQXdDOEIsVUF4QzlCLENBd0MwQyxTQXhDMUMsQ0F5Q2xCLFVBekNrQixDQXlDTixVQXpDTSxDQXlDTSxVQXpDTixDQXlDa0IsU0F6Q2xCLENBeUM4QixVQXpDOUIsQ0F5QzBDLFVBekMxQyxDQTBDbEIsVUExQ2tCLENBMENOLFNBMUNNLENBMENNLFVBMUNOLENBMENrQixVQTFDbEIsQ0EwQzhCLFVBMUM5QixDQTBDMEMsU0ExQzFDLENBMkNsQixVQTNDa0IsQ0EyQ04sVUEzQ00sQ0EyQ00sVUEzQ04sQ0EyQ2tCLFNBM0NsQixDQUFwQixDQWtEQUQsRUFtQk90QyxDQUFBLENBQWlCLElBQUlHLFdBQUosQ0FBZ0JvQyxFQUFoQixDQUFqQixDQUFzREEsRSxDQzVIakRDLFFBQVEsRUFBQSxDQUFDM0MsQ0FBRCxDQUFTLENBQzNCLElBQUFTLE9BQUEsQ0FBYyxLQUFLTixDQUFBLENBQWlCRSxXQUFqQixDQUErQlEsS0FBcEMsRUFBb0QsQ0FBcEQsQ0FBMkNiLENBQTNDLENBQ2QsS0FBQUEsT0FBQSxDQUFjLENBRmEsQ0FXN0IyQyxDQUFBckIsVUFBQXNCLFVBQUEsQ0FBZ0NDLFFBQVEsQ0FBQ2xDLENBQUQsQ0FBUSxDQUM5QyxNQUErQixFQUEvQixHQUFTQSxDQUFULENBQWlCLENBQWpCLEVBQXNCLENBQXRCLENBQTBCLENBQTFCLENBRDhDLENBbUJoRGdDLEVBQUFyQixVQUFBd0IsS0FBQSxDQUEyQkMsUUFBUSxDQUFDcEMsQ0FBRCxDQUFRcUMsQ0FBUixDQUFlLENBQUEsSUFDNUNwQixDQUQ0QyxDQUNuQ3FCLENBRG1DLENBRTVDQyxFQUFPLElBQUF6QyxPQUZxQyxDQUc1QzBDLENBRUp2QixFQUFBLENBQVUsSUFBQTVCLE9BQ1ZrRCxFQUFBLENBQUssSUFBQWxELE9BQUEsRUFBTCxDQUFBLENBQXNCZ0QsQ0FJdEIsS0FIQUUsQ0FBQSxDQUFLLElBQUFsRCxPQUFBLEVBQUwsQ0FHQSxDQUhzQlcsQ0FHdEIsQ0FBaUIsQ0FBakIsQ0FBT2lCLENBQVAsQ0FBQSxDQUlFLEdBSEFxQixDQUdJLENBSEssSUFBQUwsVUFBQSxDQUFlaEIsQ0FBZixDQUdMLENBQUFzQixDQUFBLENBQUt0QixDQUFMLENBQUEsQ0FBZ0JzQixDQUFBLENBQUtELENBQUwsQ0FBcEIsQ0FDRUUsQ0FRQSxDQVJPRCxDQUFBLENBQUt0QixDQUFMLENBUVAsQ0FQQXNCLENBQUEsQ0FBS3RCLENBQUwsQ0FPQSxDQVBnQnNCLENBQUEsQ0FBS0QsQ0FBTCxDQU9oQixDQU5BQyxDQUFBLENBQUtELENBQUwsQ0FNQSxDQU5lRSxDQU1mLENBSkFBLENBSUEsQ0FKT0QsQ0FBQSxDQUFLdEIsQ0FBTCxDQUFlLENBQWYsQ0FJUCxDQUhBc0IsQ0FBQSxDQUFLdEIsQ0FBTCxDQUFlLENBQWYsQ0FHQSxDQUhvQnNCLENBQUEsQ0FBS0QsQ0FBTCxDQUFjLENBQWQsQ0FHcEIsQ0FGQUMsQ0FBQSxDQUFLRCxDQUFMLENBQWMsQ0FBZCxDQUVBLENBRm1CRSxDQUVuQixDQUFBdkIsQ0FBQSxDQUFVcUIsQ0FUWixLQVlFLE1BSUosT0FBTyxLQUFBakQsT0E5QnlDLENBc0NsRDJDO0NBQUFyQixVQUFBOEIsSUFBQSxDQUEwQkMsUUFBUSxFQUFHLENBQUEsSUFDL0IxQyxDQUQrQixDQUN4QnFDLENBRHdCLENBRS9CRSxFQUFPLElBQUF6QyxPQUZ3QixDQUVYMEMsQ0FGVyxDQUcvQnZCLENBSCtCLENBR3RCcUIsQ0FFYkQsRUFBQSxDQUFRRSxDQUFBLENBQUssQ0FBTCxDQUNSdkMsRUFBQSxDQUFRdUMsQ0FBQSxDQUFLLENBQUwsQ0FHUixLQUFBbEQsT0FBQSxFQUFlLENBQ2ZrRCxFQUFBLENBQUssQ0FBTCxDQUFBLENBQVVBLENBQUEsQ0FBSyxJQUFBbEQsT0FBTCxDQUNWa0QsRUFBQSxDQUFLLENBQUwsQ0FBQSxDQUFVQSxDQUFBLENBQUssSUFBQWxELE9BQUwsQ0FBbUIsQ0FBbkIsQ0FJVixLQUZBaUQsQ0FFQSxDQUZTLENBRVQsQ0FBQSxDQUFBLENBQWEsQ0FDWHJCLENBQUEsQ0EvREssQ0ErREwsQ0FBd0JxQixDQUF4QixDQS9EaUIsQ0FrRWpCLElBQUlyQixDQUFKLEVBQWUsSUFBQTVCLE9BQWYsQ0FDRSxLQUlFNEIsRUFBSixDQUFjLENBQWQsQ0FBa0IsSUFBQTVCLE9BQWxCLEVBQWlDa0QsQ0FBQSxDQUFLdEIsQ0FBTCxDQUFlLENBQWYsQ0FBakMsQ0FBcURzQixDQUFBLENBQUt0QixDQUFMLENBQXJELEdBQ0VBLENBREYsRUFDYSxDQURiLENBS0EsSUFBSXNCLENBQUEsQ0FBS3RCLENBQUwsQ0FBSixDQUFvQnNCLENBQUEsQ0FBS0QsQ0FBTCxDQUFwQixDQUNFRSxDQU1BLENBTk9ELENBQUEsQ0FBS0QsQ0FBTCxDQU1QLENBTEFDLENBQUEsQ0FBS0QsQ0FBTCxDQUtBLENBTGVDLENBQUEsQ0FBS3RCLENBQUwsQ0FLZixDQUpBc0IsQ0FBQSxDQUFLdEIsQ0FBTCxDQUlBLENBSmdCdUIsQ0FJaEIsQ0FGQUEsQ0FFQSxDQUZPRCxDQUFBLENBQUtELENBQUwsQ0FBYyxDQUFkLENBRVAsQ0FEQUMsQ0FBQSxDQUFLRCxDQUFMLENBQWMsQ0FBZCxDQUNBLENBRG1CQyxDQUFBLENBQUt0QixDQUFMLENBQWUsQ0FBZixDQUNuQixDQUFBc0IsQ0FBQSxDQUFLdEIsQ0FBTCxDQUFlLENBQWYsQ0FBQSxDQUFvQnVCLENBUHRCLEtBU0UsTUFHRkYsRUFBQSxDQUFTckIsQ0ExQkUsQ0E2QmIsTUFBTyxPQUFRakIsQ0FBUixPQUFzQnFDLENBQXRCLFFBQXFDLElBQUFoRCxPQUFyQyxDQTVDNEIsQyxDQzNEbkJzRCxRQUFRLEdBQUEsQ0FBQ0MsQ0FBRCxDQUFRQyxDQUFSLENBQW9CLENBRTVDLElBQUFDLEVBQUEsQ0FBdUJDLEVBRXZCLEtBQUFDLEVBQUEsQ0FBWSxDQU1aLEtBQUFKLE1BQUEsQ0FDR3BELENBQUEsRUFBa0JvRCxDQUFsQixXQUFtQzFDLE1BQW5DLENBQTRDLElBQUlULFVBQUosQ0FBZW1ELENBQWYsQ0FBNUMsQ0FBb0VBLENBSXZFLEtBQUFLLEVBQUEsQ0FBVSxDQUdOSixFQUFKLEdBQ01BLENBQUEsS0FXSixHQVZFLElBQUFHLEVBVUYsQ0FWY0gsQ0FBQSxLQVVkLEVBUjZDLFFBUTdDLEdBUkksTUFBT0EsRUFBQSxnQkFRWCxHQVBFLElBQUFDLEVBT0YsQ0FQeUJELENBQUEsZ0JBT3pCLEVBTElBLENBQUEsYUFLSixHQUpFLElBQUF4QixFQUlGLENBSEs3QixDQUFBLEVBQWtCcUQsQ0FBQSxhQUFsQixXQUF3RDNDLE1BQXhELENBQ0QsSUFBSVQsVUFBSixDQUFlb0QsQ0FBQSxhQUFmLENBREMsQ0FDNENBLENBQUEsYUFFakQsRUFBeUMsUUFBekMsR0FBSSxNQUFPQSxFQUFBLFlBQVgsR0FDRSxJQUFBSSxFQURGLENBQ1lKLENBQUEsWUFEWixDQVpGLENBaUJLLEtBQUF4QixFQUFMLEdBQ0UsSUFBQUEsRUFERixDQUNnQixLQUFLN0IsQ0FBQSxDQUFpQkMsVUFBakIsQ0FBOEJTLEtBQW5DLEVBQTBDLEtBQTFDLENBRGhCLENBbkM0QyxDQThDNUNnRCxJQUFBQSxHQUFTQSxDQUFUQSxDQThDSTNCLEVBQVEsRUE5Q1oyQixDQThDZ0IxQyxDQUVoQjtJQUFLQSxDQUFMLENBQVMsQ0FBVCxDQUFnQixHQUFoQixDQUFZQSxDQUFaLENBQXFCQSxDQUFBLEVBQXJCLENBQ0UsT0FBUTJDLENBQVIsRUFDRSxLQUFXLEdBQVgsRUFBTTNDLENBQU4sQ0FBaUJlLENBQUFZLEtBQUEsQ0FBVyxDQUFDM0IsQ0FBRCxDQUFXLEVBQVgsQ0FBa0IsQ0FBbEIsQ0FBWCxDQUFrQyxNQUNuRCxNQUFXLEdBQVgsRUFBTUEsQ0FBTixDQUFpQmUsQ0FBQVksS0FBQSxDQUFXLENBQUMzQixDQUFELENBQUssR0FBTCxDQUFXLEdBQVgsQ0FBa0IsQ0FBbEIsQ0FBWCxDQUFrQyxNQUNuRCxNQUFXLEdBQVgsRUFBTUEsQ0FBTixDQUFpQmUsQ0FBQVksS0FBQSxDQUFXLENBQUMzQixDQUFELENBQUssR0FBTCxDQUFXLENBQVgsQ0FBa0IsQ0FBbEIsQ0FBWCxDQUFrQyxNQUNuRCxNQUFXLEdBQVgsRUFBTUEsQ0FBTixDQUFpQmUsQ0FBQVksS0FBQSxDQUFXLENBQUMzQixDQUFELENBQUssR0FBTCxDQUFXLEdBQVgsQ0FBa0IsQ0FBbEIsQ0FBWCxDQUFrQyxNQUNuRCxTQUNFLEtBQU0sbUJBQU4sQ0FBNEJBLENBQTVCLENBTko7QUFpQkptQyxFQUFBaEMsVUFBQXlDLEVBQUEsQ0FBcUNDLFFBQVEsRUFBRyxDQUU5QyxJQUFBLENBQUEsQ0FBSUMsQ0FBSixDQUVJQyxDQUZKLENBSUlsRSxDQUpKLENBTUl1RCxFQUFRLElBQUFBLE1BR1osUUFBUSxJQUFBRSxFQUFSLEVBQ0UsS0FoRklVLENBZ0ZKLENBRU9ELENBQUEsQ0FBVyxDQUFoQixLQUFtQmxFLENBQW5CLENBQTRCdUQsQ0FBQXZELE9BQTVCLENBQTBDa0UsQ0FBMUMsQ0FBcURsRSxDQUFyRCxDQUFBLENBQThELENBQzVEaUUsQ0FBQSxDQUFhOUQsQ0FBQSxDQUNYb0QsQ0FBQXRCLFNBQUEsQ0FBZWlDLENBQWYsQ0FBeUJBLENBQXpCLENBQW9DLEtBQXBDLENBRFcsQ0FFWFgsQ0FBQWEsTUFBQSxDQUFZRixDQUFaLENBQXNCQSxDQUF0QixDQUFpQyxLQUFqQyxDQUNGQSxFQUFBLEVBQVlELENBQUFqRSxPQUNhaUUsS0FBQUEsRUFBQUEsQ0FBQUEsQ0FBYSxFQUFBQyxDQUFBLEdBQWFsRSxDQUExQmlFLENBMkIzQkksRUFBQW5FLENBM0IyQitELENBK0IzQkssRUFBQXBFLENBL0IyQitELENBaUMzQk0sRUFBQXJFLENBakMyQitELENBbUMzQjlDLEVBQUFqQixDQW5DMkIrRCxDQXFDM0I3QyxFQUFBbEIsQ0FyQzJCK0QsQ0F1QzNCakMsRUF2Q0V3QyxJQXVDT3hDLEVBdkNrQmlDLENBd0MzQkwsRUF4Q0VZLElBd0NHWixFQUdULElBQUl6RCxDQUFKLENBQW9CLENBRWxCLElBREE2QixDQUNBLENBRFMsSUFBSTVCLFVBQUosQ0E1Q0xvRSxJQTRDb0J4QyxFQUFBdkIsT0FBZixDQUNULENBQU91QixDQUFBaEMsT0FBUCxFQUF3QjRELENBQXhCLENBQTZCSyxDQUFBakUsT0FBN0IsQ0FBaUQsQ0FBakQsQ0FBQSxDQUNFZ0MsQ0FBQSxDQUFTLElBQUk1QixVQUFKLENBQWU0QixDQUFBaEMsT0FBZixFQUFnQyxDQUFoQyxDQUVYZ0MsRUFBQVgsSUFBQSxDQWhESW1ELElBZ0RPeEMsRUFBWCxDQUxrQixDQVNwQnFDLENBQUEsQ0FBU0ksQ0FBQSxDQUFlLENBQWYsQ0FBbUIsQ0FFNUJ6QyxFQUFBLENBQU80QixDQUFBLEVBQVAsQ0FBQSxDQUFnQlMsQ0FBaEIsQ0FBMkIsQ0FHM0JDLEVBQUEsQ0FBTUwsQ0FBQWpFLE9BQ051RSxFQUFBLENBQVEsQ0FBQ0QsQ0FBVCxDQUFlLEtBQWYsQ0FBMEIsS0FDMUJ0QyxFQUFBLENBQU80QixDQUFBLEVBQVAsQ0FBQSxDQUF3QlUsQ0FBeEIsQ0FBOEIsR0FDOUJ0QyxFQUFBLENBQU80QixDQUFBLEVBQVAsQ0FBQSxDQUFpQlUsQ0FBakIsR0FBeUIsQ0FBekIsQ0FBOEIsR0FDOUJ0QyxFQUFBLENBQU80QixDQUFBLEVBQVAsQ0FBQSxDQUF1QlcsQ0FBdkIsQ0FBOEIsR0FDOUJ2QyxFQUFBLENBQU80QixDQUFBLEVBQVAsQ0FBQSxDQUFnQlcsQ0FBaEIsR0FBeUIsQ0FBekIsQ0FBOEIsR0FHOUIsSUFBSXBFLENBQUosQ0FDRzZCLENBQUFYLElBQUEsQ0FBVzRDLENBQVgsQ0FBdUJMLENBQXZCLENBRUEsQ0FEQUEsQ0FDQSxFQURNSyxDQUFBakUsT0FDTixDQUFBZ0MsQ0FBQSxDQUFTQSxDQUFBQyxTQUFBLENBQWdCLENBQWhCLENBQW1CMkIsQ0FBbkIsQ0FIWixLQUlPLENBQ0F6QyxDQUFBLENBQUksQ0FBVCxLQUFZQyxDQUFaLENBQWlCNkMsQ0FBQWpFLE9BQWpCLENBQW9DbUIsQ0FBcEMsQ0FBd0NDLENBQXhDLENBQTRDLEVBQUVELENBQTlDLENBQ0VhLENBQUEsQ0FBTzRCLENBQUEsRUFBUCxDQUFBO0FBQWVLLENBQUEsQ0FBVzlDLENBQVgsQ0FFakJhLEVBQUFoQyxPQUFBLENBQWdCNEQsQ0FKWCxDQXJFRFksSUE0RU5aLEVBQUEsQ0FBVUEsQ0E1RUpZLEtBNkVOeEMsRUFBQSxDQUFjQSxDQWxGb0QsQ0FPOUQsS0FDRixNQXpGSzBDLENBeUZMLENBd0ZGLElBQUlDLEVBQVMsSUFBSW5FLENBQUosQ0FBbUJMLENBQUEsQ0FDOUIsSUFBSUMsVUFBSixDQXhGZ0J3RSxJQXdGRDVDLEVBQUF2QixPQUFmLENBRDhCLENBdkZkbUUsSUF3RnFCNUMsRUFEMUIsQ0F2Rks0QyxJQXdGa0NoQixFQUR2QyxDQWFiZSxFQUFBcEQsRUFBQSxDQUh3QjhDLENBR3hCLENBQXlCLENBQXpCLENBQTRCUCxDQUE1QixDQUNBYSxFQUFBcEQsRUFBQSxDQS9MT21ELENBK0xQLENBQXdCLENBQXhCLENBQTJCWixDQUEzQixDQUdrQnhCLEtBQUFBLEVBRFh1QyxFQUFBdkMsQ0F2R1dzQyxJQXVHWHRDLENBdkdzQ2lCLENBdUd0Q2pCLENBQ1dBLENBZ01kM0IsQ0FoTWMyQixDQWtNZHRDLEVBbE1jc0MsQ0FvTWR3QyxDQUdDbkUsRUFBQSxDQUFRLENBQWIsS0FBZ0JYLEVBQWhCLENBQXlCK0UsQ0FBQS9FLE9BQXpCLENBQTJDVyxDQUEzQyxDQUFtRFgsRUFBbkQsQ0FBMkRXLENBQUEsRUFBM0QsQ0FVRSxHQVRBbUUsQ0FTSSxDQVRNQyxDQUFBLENBQVVwRSxDQUFWLENBU04sQ0FOSkgsQ0FBQWMsVUFBQUMsRUFBQXlELE1BQUEsQ0EzTXNCTCxDQTJNdEIsQ0FqVkt6QyxDQW1WSCxDQUFrQzRDLENBQWxDLENBRkYsQ0FNSSxDQUFVLEdBQVYsQ0FBQUEsQ0FBSixDQWpOc0JILENBbU5wQnBELEVBQUEsQ0FBaUJ3RCxDQUFBLENBQVUsRUFBRXBFLENBQVosQ0FBakIsQ0FBcUNvRSxDQUFBLENBQVUsRUFBRXBFLENBQVosQ0FBckMsQ0FBeURtRCxDQUF6RCxDQUlBLENBdk5vQmEsQ0FxTnBCcEQsRUFBQSxDQUFpQndELENBQUEsQ0FBVSxFQUFFcEUsQ0FBWixDQUFqQixDQUFxQyxDQUFyQyxDQUVBLENBdk5vQmdFLENBdU5wQnBELEVBQUEsQ0FBaUJ3RCxDQUFBLENBQVUsRUFBRXBFLENBQVosQ0FBakIsQ0FBcUNvRSxDQUFBLENBQVUsRUFBRXBFLENBQVosQ0FBckMsQ0FBeURtRCxDQUF6RCxDQU5GLEtBUU8sSUFBZ0IsR0FBaEIsR0FBSWdCLENBQUosQ0FDTCxLQWxVQSxLQUFBOUMsRUFBQSxDQTBHRzJDLENBQUE3QyxPQUFBLEVBekdILEtBQUE4QixFQUFBLENBQVUsSUFBQTVCLEVBQUFoQyxPQUNWLE1BQ0YsTUFBSzBELEVBQUwsQ0FtSEYsSUFBSWlCLEVBQVMsSUFBSW5FLENBQUosQ0FBbUJMLENBQUEsQ0FDOUIsSUFBSUMsVUFBSixDQW5IZ0I2RSxJQW1IRGpELEVBQUF2QixPQUFmLENBRDhCLENBbEhkd0UsSUFtSHFCakQsRUFEMUIsQ0FsSEtpRCxJQW1Ia0NyQixFQUR2QyxDQUFiLENBS0lzQixFQUxKLENBT0k1QyxDQVBKLENBU0k2QyxDQVRKLENBV0lDLENBWEosQ0FhSUMsQ0FiSixDQWVJQyxHQUNFLENBQUMsRUFBRCxDQUFLLEVBQUwsQ0FBUyxFQUFULENBQWEsQ0FBYixDQUFnQixDQUFoQixDQUFtQixDQUFuQixDQUFzQixDQUF0QixDQUF5QixDQUF6QixDQUE0QixFQUE1QixDQUFnQyxDQUFoQyxDQUFtQyxFQUFuQyxDQUF1QyxDQUF2QyxDQUEwQyxFQUExQyxDQUE4QyxDQUE5QyxDQUFpRCxFQUFqRCxDQUFxRCxDQUFyRCxDQUF3RCxFQUF4RCxDQUE0RCxDQUE1RCxDQUErRCxFQUEvRCxDQWhCTixDQWtCSUMsQ0FsQkosQ0FvQklDLEVBcEJKLENBc0JJQyxDQXRCSixDQXdCSUMsRUF4QkosQ0ErQklDLEVBL0JKLENBaUNJQyxHQUFtQi9FLEtBQUosQ0FBVSxFQUFWLENBakNuQjtBQW1DSWdGLEVBbkNKLENBcUNJQyxDQXJDSixDQXVDSUMsRUF2Q0osQ0F5Q0k1RSxDQXpDSixDQTJDSUMsRUFJSjhELEdBQUEsQ0FBUXhCLEVBRVJpQixFQUFBcEQsRUFBQSxDQUh3QjhDLENBR3hCLENBQXlCLENBQXpCLENBQTRCUCxDQUE1QixDQUNBYSxFQUFBcEQsRUFBQSxDQUFpQjJELEVBQWpCLENBQXdCLENBQXhCLENBQTJCcEIsQ0FBM0IsQ0FFQXhCLEVBQUEsQ0FBT3VDLEVBQUEsQ0F0S1dJLElBc0tYLENBdEt3QzFCLENBc0t4QyxDQUdQZ0MsRUFBQSxDQUFnQlMsRUFBQSxDQXpLRWYsSUF5S2VnQixFQUFqQixDQUFtQyxFQUFuQyxDQUNoQlQsR0FBQSxDQUFjVSxFQUFBLENBQTBCWCxDQUExQixDQUNkRSxFQUFBLENBQWNPLEVBQUEsQ0EzS0lmLElBMktha0IsRUFBakIsQ0FBaUMsQ0FBakMsQ0FDZFQsR0FBQSxDQUFZUSxFQUFBLENBQTBCVCxDQUExQixDQUdaLEtBQUtOLENBQUwsQ0FBWSxHQUFaLENBQXdCLEdBQXhCLENBQWlCQSxDQUFqQixFQUEyRCxDQUEzRCxHQUErQkksQ0FBQSxDQUFjSixDQUFkLENBQXFCLENBQXJCLENBQS9CLENBQThEQSxDQUFBLEVBQTlELEVBQ0EsSUFBS0MsQ0FBTCxDQUFhLEVBQWIsQ0FBeUIsQ0FBekIsQ0FBaUJBLENBQWpCLEVBQXlELENBQXpELEdBQThCSyxDQUFBLENBQVlMLENBQVosQ0FBb0IsQ0FBcEIsQ0FBOUIsQ0FBNERBLENBQUEsRUFBNUQsRUFJdUJELElBQUFBLEdBQUFBLENBQUFBLENBQXFCQyxHQUFBQSxDQUFyQkQsQ0E2Z0JuQmlCLEVBQU0sS0FBS2pHLENBQUEsQ0FBaUJHLFdBQWpCLENBQStCTyxLQUFwQyxFQUEyQ3NFLEVBQTNDLENBQWtEQyxFQUFsRCxDQTdnQmFELENBOGdCbkJoRSxDQTlnQm1CZ0UsQ0E4Z0JoQmtCLENBOWdCZ0JsQixDQThnQmJtQixDQTlnQmFuQixDQThnQkZvQixDQTlnQkVwQixDQStnQm5CcUIsRUFBUyxLQUFLckcsQ0FBQSxDQUFpQkcsV0FBakIsQ0FBK0JPLEtBQXBDLEVBQTJDLEdBQTNDLENBL2dCVXNFLENBZ2hCbkJzQixDQWhoQm1CdEIsQ0FpaEJuQnVCLENBamhCbUJ2QixDQWtoQm5Cd0IsRUFBUSxLQUFLeEcsQ0FBQSxDQUFpQkMsVUFBakIsQ0FBOEJTLEtBQW5DLEVBQTBDLEVBQTFDLENBR1osS0FBS00sQ0FBTCxDQURBa0YsQ0FDQSxDQURJLENBQ0osQ0FBWWxGLENBQVosQ0FBZ0JnRSxFQUFoQixDQUFzQmhFLENBQUEsRUFBdEIsQ0FDRWlGLENBQUEsQ0FBSUMsQ0FBQSxFQUFKLENBQUEsQ0F0aEIyQmQsQ0FzaEJoQixDQUFjcEUsQ0FBZCxDQUViLEtBQUtBLENBQUwsQ0FBUyxDQUFULENBQVlBLENBQVosQ0FBZ0JpRSxFQUFoQixDQUF1QmpFLENBQUEsRUFBdkIsQ0FDRWlGLENBQUEsQ0FBSUMsQ0FBQSxFQUFKLENBQUEsQ0F6aEJpRFosQ0F5aEJ0QyxDQUFZdEUsQ0FBWixDQUliLElBQUksQ0FBQ2hCLENBQUwsQ0FBcUIsQ0FDZGdCLENBQUEsQ0FBSSxDQUFULEtBQVlvRixDQUFaLENBQWdCSSxDQUFBM0csT0FBaEIsQ0FBOEJtQixDQUE5QixDQUFrQ29GLENBQWxDLENBQXFDLEVBQUVwRixDQUF2QyxDQUNFd0YsQ0FBQSxDQUFNeEYsQ0FBTixDQUFBLENBQVcsQ0FGTSxDQVFoQkEsQ0FBQSxDQURMc0YsQ0FDSyxDQURLLENBQ1YsS0FBWUYsQ0FBWixDQUFnQkgsQ0FBQXBHLE9BQWhCLENBQTRCbUIsQ0FBNUIsQ0FBZ0NvRixDQUFoQyxDQUFtQ3BGLENBQW5DLEVBQXdDa0YsQ0FBeEMsQ0FBMkMsQ0FFekMsSUFBS0EsQ0FBTCxDQUFTLENBQVQsQ0FBWWxGLENBQVosQ0FBZ0JrRixDQUFoQixDQUFvQkUsQ0FBcEIsRUFBeUJILENBQUEsQ0FBSWpGLENBQUosQ0FBUWtGLENBQVIsQ0FBekIsR0FBd0NELENBQUEsQ0FBSWpGLENBQUosQ0FBeEMsQ0FBZ0QsRUFBRWtGLENBQWxELEVBRUFDLENBQUEsQ0FBWUQsQ0FFWixJQUFlLENBQWYsR0FBSUQsQ0FBQSxDQUFJakYsQ0FBSixDQUFKLENBRUUsR0FBZ0IsQ0FBaEIsQ0FBSW1GLENBQUosQ0FDRSxJQUFBLENBQXFCLENBQXJCLENBQU9BLENBQUEsRUFBUCxDQUFBLENBQ0VFLENBQUEsQ0FBT0MsQ0FBQSxFQUFQLENBQ0EsQ0FEb0IsQ0FDcEI7QUFBQUUsQ0FBQSxDQUFNLENBQU4sQ0FBQSxFQUhKLEtBTUUsS0FBQSxDQUFtQixDQUFuQixDQUFPTCxDQUFQLENBQUEsQ0FFRUksQ0FrQkEsQ0FsQm1CLEdBQVosQ0FBQUosQ0FBQSxDQUFrQkEsQ0FBbEIsQ0FBOEIsR0FrQnJDLENBaEJJSSxDQWdCSixDQWhCVUosQ0FnQlYsQ0FoQnNCLENBZ0J0QixFQWhCMkJJLENBZ0IzQixDQWhCaUNKLENBZ0JqQyxHQWZFSSxDQWVGLENBZlFKLENBZVIsQ0Fmb0IsQ0FlcEIsRUFYVyxFQUFYLEVBQUlJLENBQUosRUFDRUYsQ0FBQSxDQUFPQyxDQUFBLEVBQVAsQ0FFQSxDQUZvQixFQUVwQixDQURBRCxDQUFBLENBQU9DLENBQUEsRUFBUCxDQUNBLENBRG9CQyxDQUNwQixDQUQwQixDQUMxQixDQUFBQyxDQUFBLENBQU0sRUFBTixDQUFBLEVBSEYsR0FNRUgsQ0FBQSxDQUFPQyxDQUFBLEVBQVAsQ0FFQSxDQUZvQixFQUVwQixDQURBRCxDQUFBLENBQU9DLENBQUEsRUFBUCxDQUNBLENBRG9CQyxDQUNwQixDQUQwQixFQUMxQixDQUFBQyxDQUFBLENBQU0sRUFBTixDQUFBLEVBUkYsQ0FXQSxDQUFBTCxDQUFBLEVBQWFJLENBNUJuQixLQXFDRSxJQUxBRixDQUFBLENBQU9DLENBQUEsRUFBUCxDQUtJLENBTGdCTCxDQUFBLENBQUlqRixDQUFKLENBS2hCLENBSkp3RixDQUFBLENBQU1QLENBQUEsQ0FBSWpGLENBQUosQ0FBTixDQUFBLEVBSUksQ0FISm1GLENBQUEsRUFHSSxDQUFZLENBQVosQ0FBQUEsQ0FBSixDQUNFLElBQUEsQ0FBcUIsQ0FBckIsQ0FBT0EsQ0FBQSxFQUFQLENBQUEsQ0FDRUUsQ0FBQSxDQUFPQyxDQUFBLEVBQVAsQ0FDQSxDQURvQkwsQ0FBQSxDQUFJakYsQ0FBSixDQUNwQixDQUFBd0YsQ0FBQSxDQUFNUCxDQUFBLENBQUlqRixDQUFKLENBQU4sQ0FBQSxFQUhKLEtBT0UsS0FBQSxDQUFtQixDQUFuQixDQUFPbUYsQ0FBUCxDQUFBLENBRUVJLENBVUEsQ0FWbUIsQ0FBWixDQUFBSixDQUFBLENBQWdCQSxDQUFoQixDQUE0QixDQVVuQyxDQVJJSSxDQVFKLENBUlVKLENBUVYsQ0FSc0IsQ0FRdEIsRUFSMkJJLENBUTNCLENBUmlDSixDQVFqQyxHQVBFSSxDQU9GLENBUFFKLENBT1IsQ0FQb0IsQ0FPcEIsRUFKQUUsQ0FBQSxDQUFPQyxDQUFBLEVBQVAsQ0FJQSxDQUpvQixFQUlwQixDQUhBRCxDQUFBLENBQU9DLENBQUEsRUFBUCxDQUdBLENBSG9CQyxDQUdwQixDQUgwQixDQUcxQixDQUZBQyxDQUFBLENBQU0sRUFBTixDQUFBLEVBRUEsQ0FBQUwsQ0FBQSxFQUFhSSxDQTlEc0IsQ0FvRTNDLENBQUEsQ0FFSXZHLENBQUEsQ0FBaUJxRyxDQUFBdkUsU0FBQSxDQUFnQixDQUFoQixDQUFtQndFLENBQW5CLENBQWpCLENBQStDRCxDQUFBcEMsTUFBQSxDQUFhLENBQWIsQ0FBZ0JxQyxDQUFoQixDQTFtQm5EZCxHQUFBLENBQWNLLEVBQUEsQ0EybUJMVyxDQTNtQkssQ0FBb0MsQ0FBcEMsQ0FDZCxLQUFLeEYsQ0FBTCxDQUFTLENBQVQsQ0FBZ0IsRUFBaEIsQ0FBWUEsQ0FBWixDQUFvQkEsQ0FBQSxFQUFwQixDQUNFeUUsRUFBQSxDQUFhekUsQ0FBYixDQUFBLENBQWtCd0UsRUFBQSxDQUFZTCxFQUFBLENBQVduRSxDQUFYLENBQVosQ0FFcEIsS0FBS2tFLENBQUwsQ0FBYSxFQUFiLENBQXlCLENBQXpCLENBQWlCQSxDQUFqQixFQUEwRCxDQUExRCxHQUE4Qk8sRUFBQSxDQUFhUCxDQUFiLENBQXFCLENBQXJCLENBQTlCLENBQTZEQSxDQUFBLEVBQTdELEVBRUFRLEVBQUEsQ0FBWUssRUFBQSxDQUEwQlAsRUFBMUIsQ0FHWmhCLEVBQUFwRCxFQUFBLENBQWlCNEQsQ0FBakIsQ0FBd0IsR0FBeEIsQ0FBNkIsQ0FBN0IsQ0FBZ0NyQixDQUFoQyxDQUNBYSxFQUFBcEQsRUFBQSxDQUFpQjZELENBQWpCLENBQXlCLENBQXpCLENBQTRCLENBQTVCLENBQStCdEIsQ0FBL0IsQ0FDQWEsRUFBQXBELEVBQUEsQ0FBaUI4RCxDQUFqQixDQUF5QixDQUF6QixDQUE0QixDQUE1QixDQUErQnZCLENBQS9CLENBQ0EsS0FBSzNDLENBQUwsQ0FBUyxDQUFULENBQVlBLENBQVosQ0FBZ0JrRSxDQUFoQixDQUF1QmxFLENBQUEsRUFBdkIsQ0FDRXdELENBQUFwRCxFQUFBLENBQWlCcUUsRUFBQSxDQUFhekUsQ0FBYixDQUFqQixDQUFrQyxDQUFsQyxDQUFxQzJDLENBQXJDLENBSUczQyxFQUFBLENBQUksQ0FBVCxLQUFZQyxFQUFaLENBQWlCd0YsQ0FBQTVHLE9BQWpCLENBQTJDbUIsQ0FBM0MsQ0FBK0NDLEVBQS9DLENBQW1ERCxDQUFBLEVBQW5ELENBTUUsR0FMQTJFLENBS0k7QUFMR2MsQ0FBQSxDQUFrQnpGLENBQWxCLENBS0gsQ0FISndELENBQUFwRCxFQUFBLENBQWlCc0UsRUFBQSxDQUFVQyxDQUFWLENBQWpCLENBQWtDSCxFQUFBLENBQVlHLENBQVosQ0FBbEMsQ0FBcURoQyxDQUFyRCxDQUdJLENBQVEsRUFBUixFQUFBZ0MsQ0FBSixDQUFnQixDQUNkM0UsQ0FBQSxFQUNBLFFBQVEyRSxDQUFSLEVBQ0UsS0FBSyxFQUFMLENBQVNDLEVBQUEsQ0FBUyxDQUFHLE1BQ3JCLE1BQUssRUFBTCxDQUFTQSxFQUFBLENBQVMsQ0FBRyxNQUNyQixNQUFLLEVBQUwsQ0FBU0EsRUFBQSxDQUFTLENBQUcsTUFDckIsU0FDRSxLQUFNLGdCQUFOLENBQXlCRCxDQUF6QixDQUxKLENBUUFuQixDQUFBcEQsRUFBQSxDQUFpQnFGLENBQUEsQ0FBa0J6RixDQUFsQixDQUFqQixDQUF1QzRFLEVBQXZDLENBQStDakMsQ0FBL0MsQ0FWYyxDQWdCaEIsSUFBQSxHQUFBLENBQUMwQixFQUFELENBQWNELENBQWQsQ0FBQSxDQUNBLEdBQUEsQ0FBQ0csRUFBRCxDQUFZRCxDQUFaLENBREEsQ0FrQkU5RSxDQWxCRixDQW9CRVgsRUFwQkYsQ0FzQkU4RSxDQXRCRixDQXdCRWdCLEVBeEJGLENBMEJFTixFQTFCRixDQTRCRUQsRUE1QkYsQ0E4QkVHLEVBOUJGLENBZ0NFRCxFQUVKRCxHQUFBLENBQWNxQixFQUFBLENBQU8sQ0FBUCxDQUNkdEIsR0FBQSxDQUFnQnNCLEVBQUEsQ0FBTyxDQUFQLENBQ2hCbkIsR0FBQSxDQUFZb0IsRUFBQSxDQUFLLENBQUwsQ0FDWnJCLEdBQUEsQ0FBY3FCLEVBQUEsQ0FBSyxDQUFMLENBR1RuRyxFQUFBLENBQVEsQ0FBYixLQUFnQlgsRUFBaEIsQ0F6Q0VzQyxDQXlDdUJ0QyxPQUF6QixDQUEyQ1csQ0FBM0MsQ0FBbURYLEVBQW5ELENBQTJELEVBQUVXLENBQTdELENBT0UsR0FOQW1FLENBTUksQ0FoREp4QyxDQTBDVSxDQUFVM0IsQ0FBVixDQU1OLENBN0NKZ0UsQ0EwQ0FwRCxFQUFBLENBQWlCaUUsRUFBQSxDQUFZVixDQUFaLENBQWpCLENBQXVDUyxFQUFBLENBQWNULENBQWQsQ0FBdkMsQ0FBK0RoQixDQUEvRCxDQUdJLENBQVUsR0FBVixDQUFBZ0IsQ0FBSixDQTdDQUgsQ0ErQ0VwRCxFQUFBLENBbERGZSxDQWtEbUIsQ0FBVSxFQUFFM0IsQ0FBWixDQUFqQixDQWxERjJCLENBa0R1QyxDQUFVLEVBQUUzQixDQUFaLENBQXJDLENBQXlEbUQsQ0FBekQsQ0FLQSxDQUhBZ0MsRUFHQSxDQXZERnhELENBb0RTLENBQVUsRUFBRTNCLENBQVosQ0FHUCxDQXBERmdFLENBa0RFcEQsRUFBQSxDQUFpQm1FLEVBQUEsQ0FBVUksRUFBVixDQUFqQixDQUFrQ0wsRUFBQSxDQUFZSyxFQUFaLENBQWxDLENBQXFEaEMsQ0FBckQsQ0FFQSxDQXBERmEsQ0FvREVwRCxFQUFBLENBdkRGZSxDQXVEbUIsQ0FBVSxFQUFFM0IsQ0FBWixDQUFqQixDQXZERjJCLENBdUR1QyxDQUFVLEVBQUUzQixDQUFaLENBQXJDLENBQXlEbUQsQ0FBekQsQ0FQRixLQVNPLElBQWdCLEdBQWhCLEdBQUlnQixDQUFKLENBQ0wsS0FyUkEsS0FBQTlDLEVBQUEsQ0FpT0cyQyxDQUFBN0MsT0FBQSxFQWhPSCxLQUFBOEIsRUFBQSxDQUFVLElBQUE1QixFQUFBaEMsT0FDVixNQUNGLFNBQ0UsS0FBTSwwQkFBTixDQXBCSixDQXVCQSxNQUFPLEtBQUFnQyxFQWxDdUMsQ0FzV3BCK0U7UUFBUSxHQUFBLENBQUMvRyxDQUFELENBQVNnSCxDQUFULENBQTJCLENBRTdELElBQUFoSCxPQUFBLENBQWNBLENBRWQsS0FBQWdILEVBQUEsQ0FBd0JBLENBSnFDO0FBZTNELElBQUEsR0FBQSxRQUFRLEVBQUcsQ0FpQmJsQixRQUFTQSxFQUFJLENBQUM5RixDQUFELENBQVMsQ0FDcEIsT0FBUThELENBQVIsRUFDRSxLQUFpQixDQUFqQixHQUFNOUQsQ0FBTixDQUFxQixNQUFPLENBQUMsR0FBRCxDQUFNQSxDQUFOLENBQWUsQ0FBZixDQUFrQixDQUFsQixDQUM1QixNQUFpQixDQUFqQixHQUFNQSxDQUFOLENBQXFCLE1BQU8sQ0FBQyxHQUFELENBQU1BLENBQU4sQ0FBZSxDQUFmLENBQWtCLENBQWxCLENBQzVCLE1BQWlCLENBQWpCLEdBQU1BLENBQU4sQ0FBcUIsTUFBTyxDQUFDLEdBQUQsQ0FBTUEsQ0FBTixDQUFlLENBQWYsQ0FBa0IsQ0FBbEIsQ0FDNUIsTUFBaUIsQ0FBakIsR0FBTUEsQ0FBTixDQUFxQixNQUFPLENBQUMsR0FBRCxDQUFNQSxDQUFOLENBQWUsQ0FBZixDQUFrQixDQUFsQixDQUM1QixNQUFpQixDQUFqQixHQUFNQSxDQUFOLENBQXFCLE1BQU8sQ0FBQyxHQUFELENBQU1BLENBQU4sQ0FBZSxDQUFmLENBQWtCLENBQWxCLENBQzVCLE1BQWlCLENBQWpCLEdBQU1BLENBQU4sQ0FBcUIsTUFBTyxDQUFDLEdBQUQsQ0FBTUEsQ0FBTixDQUFlLENBQWYsQ0FBa0IsQ0FBbEIsQ0FDNUIsTUFBaUIsQ0FBakIsR0FBTUEsQ0FBTixDQUFxQixNQUFPLENBQUMsR0FBRCxDQUFNQSxDQUFOLENBQWUsQ0FBZixDQUFrQixDQUFsQixDQUM1QixNQUFpQixFQUFqQixHQUFNQSxDQUFOLENBQXNCLE1BQU8sQ0FBQyxHQUFELENBQU1BLENBQU4sQ0FBZSxFQUFmLENBQW1CLENBQW5CLENBQzdCLE1BQWdCLEVBQWhCLEVBQU1BLENBQU4sQ0FBcUIsTUFBTyxDQUFDLEdBQUQsQ0FBTUEsQ0FBTixDQUFlLEVBQWYsQ0FBbUIsQ0FBbkIsQ0FDNUIsTUFBZ0IsRUFBaEIsRUFBTUEsQ0FBTixDQUFxQixNQUFPLENBQUMsR0FBRCxDQUFNQSxDQUFOLENBQWUsRUFBZixDQUFtQixDQUFuQixDQUM1QixNQUFnQixFQUFoQixFQUFNQSxDQUFOLENBQXFCLE1BQU8sQ0FBQyxHQUFELENBQU1BLENBQU4sQ0FBZSxFQUFmLENBQW1CLENBQW5CLENBQzVCLE1BQWdCLEVBQWhCLEVBQU1BLENBQU4sQ0FBcUIsTUFBTyxDQUFDLEdBQUQsQ0FBTUEsQ0FBTixDQUFlLEVBQWYsQ0FBbUIsQ0FBbkIsQ0FDNUIsTUFBZ0IsRUFBaEIsRUFBTUEsQ0FBTixDQUFxQixNQUFPLENBQUMsR0FBRCxDQUFNQSxDQUFOLENBQWUsRUFBZixDQUFtQixDQUFuQixDQUM1QixNQUFnQixFQUFoQixFQUFNQSxDQUFOLENBQXFCLE1BQU8sQ0FBQyxHQUFELENBQU1BLENBQU4sQ0FBZSxFQUFmLENBQW1CLENBQW5CLENBQzVCLE1BQWdCLEVBQWhCLEVBQU1BLENBQU4sQ0FBcUIsTUFBTyxDQUFDLEdBQUQsQ0FBTUEsQ0FBTixDQUFlLEVBQWYsQ0FBbUIsQ0FBbkIsQ0FDNUIsTUFBZ0IsRUFBaEIsRUFBTUEsQ0FBTixDQUFxQixNQUFPLENBQUMsR0FBRDtBQUFNQSxDQUFOLENBQWUsRUFBZixDQUFtQixDQUFuQixDQUM1QixNQUFnQixFQUFoQixFQUFNQSxDQUFOLENBQXFCLE1BQU8sQ0FBQyxHQUFELENBQU1BLENBQU4sQ0FBZSxFQUFmLENBQW1CLENBQW5CLENBQzVCLE1BQWdCLEVBQWhCLEVBQU1BLENBQU4sQ0FBcUIsTUFBTyxDQUFDLEdBQUQsQ0FBTUEsQ0FBTixDQUFlLEVBQWYsQ0FBbUIsQ0FBbkIsQ0FDNUIsTUFBZ0IsRUFBaEIsRUFBTUEsQ0FBTixDQUFxQixNQUFPLENBQUMsR0FBRCxDQUFNQSxDQUFOLENBQWUsRUFBZixDQUFtQixDQUFuQixDQUM1QixNQUFnQixFQUFoQixFQUFNQSxDQUFOLENBQXFCLE1BQU8sQ0FBQyxHQUFELENBQU1BLENBQU4sQ0FBZSxFQUFmLENBQW1CLENBQW5CLENBQzVCLE1BQWdCLEVBQWhCLEVBQU1BLENBQU4sQ0FBcUIsTUFBTyxDQUFDLEdBQUQsQ0FBTUEsQ0FBTixDQUFlLEVBQWYsQ0FBbUIsQ0FBbkIsQ0FDNUIsTUFBZ0IsRUFBaEIsRUFBTUEsQ0FBTixDQUFxQixNQUFPLENBQUMsR0FBRCxDQUFNQSxDQUFOLENBQWUsRUFBZixDQUFtQixDQUFuQixDQUM1QixNQUFnQixHQUFoQixFQUFNQSxDQUFOLENBQXNCLE1BQU8sQ0FBQyxHQUFELENBQU1BLENBQU4sQ0FBZSxFQUFmLENBQW1CLENBQW5CLENBQzdCLE1BQWdCLEdBQWhCLEVBQU1BLENBQU4sQ0FBc0IsTUFBTyxDQUFDLEdBQUQsQ0FBTUEsQ0FBTixDQUFlLEdBQWYsQ0FBb0IsQ0FBcEIsQ0FDN0IsTUFBZ0IsR0FBaEIsRUFBTUEsQ0FBTixDQUFzQixNQUFPLENBQUMsR0FBRCxDQUFNQSxDQUFOLENBQWUsR0FBZixDQUFvQixDQUFwQixDQUM3QixNQUFnQixHQUFoQixFQUFNQSxDQUFOLENBQXNCLE1BQU8sQ0FBQyxHQUFELENBQU1BLENBQU4sQ0FBZSxHQUFmLENBQW9CLENBQXBCLENBQzdCLE1BQWdCLEdBQWhCLEVBQU1BLENBQU4sQ0FBc0IsTUFBTyxDQUFDLEdBQUQsQ0FBTUEsQ0FBTixDQUFlLEdBQWYsQ0FBb0IsQ0FBcEIsQ0FDN0IsTUFBZ0IsR0FBaEIsRUFBTUEsQ0FBTixDQUFzQixNQUFPLENBQUMsR0FBRCxDQUFNQSxDQUFOLENBQWUsR0FBZixDQUFvQixDQUFwQixDQUM3QixNQUFpQixHQUFqQixHQUFNQSxDQUFOLENBQXVCLE1BQU8sQ0FBQyxHQUFELENBQU1BLENBQU4sQ0FBZSxHQUFmLENBQW9CLENBQXBCLENBQzlCLFNBQVMsS0FBTSxrQkFBTixDQUEyQkEsQ0FBM0IsQ0E5QlgsQ0FEb0IsQ0FmdEIsSUFBSWtDLEVBQVEsRUFBWixDQUVJZixDQUZKLENBSUk4RixDQUVKLEtBQUs5RixDQUFMLENBQVMsQ0FBVCxDQUFpQixHQUFqQixFQUFZQSxDQUFaLENBQXNCQSxDQUFBLEVBQXRCLENBQ0U4RixDQUNBLENBREluQixDQUFBLENBQUszRSxDQUFMLENBQ0osQ0FBQWUsQ0FBQSxDQUFNZixDQUFOLENBQUEsQ0FBWThGLENBQUEsQ0FBRSxDQUFGLENBQVosRUFBb0IsRUFBcEI7QUFBMkJBLENBQUEsQ0FBRSxDQUFGLENBQTNCLEVBQW1DLEVBQW5DLENBQXlDQSxDQUFBLENBQUUsQ0FBRixDQTBDM0MsT0FBTy9FLEVBcERNLENBQVgsRUFBQSxDQUZKZ0YsR0FDUy9HLENBQUEsQ0FBaUIsSUFBSUcsV0FBSixDQUFnQjRCLEVBQWhCLENBQWpCLENBQTBDQSxFQTZJbEJpRjtRQUFRLEdBQUEsQ0FBUkEsQ0FBUSxDQUFDcEMsQ0FBRCxDQUFZLENBa0RuRHFDLFFBQVNBLEVBQVUsQ0FBQ0MsQ0FBRCxDQUFRQyxDQUFSLENBQWdCLENBOUVuQyxJQUFJUixFQWdGY08sQ0FoRlBMLEVBQVgsQ0FFSU8sRUFBWSxFQUZoQixDQUlJaEYsRUFBTSxDQUpWLENBTUl1RCxDQUdKQSxFQUFBLENBQU9vQixFQUFBLENBdUVXRyxDQWxGTHJILE9BV04sQ0FDUHVILEVBQUEsQ0FBVWhGLENBQUEsRUFBVixDQUFBLENBQW1CdUQsQ0FBbkIsQ0FBMEIsS0FDMUJ5QixFQUFBLENBQVVoRixDQUFBLEVBQVYsQ0FBQSxDQUFvQnVELENBQXBCLEVBQTRCLEVBQTVCLENBQWtDLEdBQ2xDeUIsRUFBQSxDQUFVaEYsQ0FBQSxFQUFWLENBQUEsQ0FBbUJ1RCxDQUFuQixFQUEyQixFQTdEM0IsS0FBSTNELENBRUosUUFBUTJCLENBQVIsRUFDRSxLQUFlLENBQWYsR0E2RDJCZ0QsQ0E3RDNCLENBQW1CM0UsQ0FBQSxDQUFJLENBQUMsQ0FBRCxDQTZESTJFLENBN0RKLENBQVcsQ0FBWCxDQUFjLENBQWQsQ0FBa0IsTUFDekMsTUFBZSxDQUFmLEdBNEQyQkEsQ0E1RDNCLENBQW1CM0UsQ0FBQSxDQUFJLENBQUMsQ0FBRCxDQTRESTJFLENBNURKLENBQVcsQ0FBWCxDQUFjLENBQWQsQ0FBa0IsTUFDekMsTUFBZSxDQUFmLEdBMkQyQkEsQ0EzRDNCLENBQW1CM0UsQ0FBQSxDQUFJLENBQUMsQ0FBRCxDQTJESTJFLENBM0RKLENBQVcsQ0FBWCxDQUFjLENBQWQsQ0FBa0IsTUFDekMsTUFBZSxDQUFmLEdBMEQyQkEsQ0ExRDNCLENBQW1CM0UsQ0FBQSxDQUFJLENBQUMsQ0FBRCxDQTBESTJFLENBMURKLENBQVcsQ0FBWCxDQUFjLENBQWQsQ0FBa0IsTUFDekMsTUFBYyxDQUFkLEVBeUQyQkEsQ0F6RDNCLENBQWtCM0UsQ0FBQSxDQUFJLENBQUMsQ0FBRCxDQXlESzJFLENBekRMLENBQVcsQ0FBWCxDQUFjLENBQWQsQ0FBa0IsTUFDeEMsTUFBYyxDQUFkLEVBd0QyQkEsQ0F4RDNCLENBQWtCM0UsQ0FBQSxDQUFJLENBQUMsQ0FBRCxDQXdESzJFLENBeERMLENBQVcsQ0FBWCxDQUFjLENBQWQsQ0FBa0IsTUFDeEMsTUFBYyxFQUFkLEVBdUQyQkEsQ0F2RDNCLENBQW1CM0UsQ0FBQSxDQUFJLENBQUMsQ0FBRCxDQXVESTJFLENBdkRKLENBQVcsQ0FBWCxDQUFjLENBQWQsQ0FBa0IsTUFDekMsTUFBYyxFQUFkLEVBc0QyQkEsQ0F0RDNCLENBQW1CM0UsQ0FBQSxDQUFJLENBQUMsQ0FBRCxDQXNESTJFLENBdERKLENBQVcsRUFBWCxDQUFlLENBQWYsQ0FBbUIsTUFDMUMsTUFBYyxFQUFkLEVBcUQyQkEsQ0FyRDNCLENBQW1CM0UsQ0FBQSxDQUFJLENBQUMsQ0FBRCxDQXFESTJFLENBckRKLENBQVcsRUFBWCxDQUFlLENBQWYsQ0FBbUIsTUFDMUMsTUFBYyxFQUFkLEVBb0QyQkEsQ0FwRDNCLENBQW1CM0UsQ0FBQSxDQUFJLENBQUMsQ0FBRCxDQW9ESTJFLENBcERKLENBQVcsRUFBWCxDQUFlLENBQWYsQ0FBbUIsTUFDMUMsTUFBYyxFQUFkLEVBbUQyQkEsQ0FuRDNCLENBQW1CM0UsQ0FBQSxDQUFJLENBQUMsRUFBRCxDQW1ESTJFLENBbkRKLENBQVksRUFBWixDQUFnQixDQUFoQixDQUFvQixNQUMzQyxNQUFjLEVBQWQsRUFrRDJCQSxDQWxEM0IsQ0FBbUIzRSxDQUFBLENBQUksQ0FBQyxFQUFELENBa0RJMkUsQ0FsREosQ0FBWSxFQUFaLENBQWdCLENBQWhCLENBQW9CLE1BQzNDLE1BQWMsRUFBZCxFQWlEMkJBLENBakQzQixDQUFtQjNFLENBQUEsQ0FBSSxDQUFDLEVBQUQsQ0FpREkyRSxDQWpESjtBQUFZLEVBQVosQ0FBZ0IsQ0FBaEIsQ0FBb0IsTUFDM0MsTUFBYyxHQUFkLEVBZ0QyQkEsQ0FoRDNCLENBQW9CM0UsQ0FBQSxDQUFJLENBQUMsRUFBRCxDQWdERzJFLENBaERILENBQVksRUFBWixDQUFnQixDQUFoQixDQUFvQixNQUM1QyxNQUFjLEdBQWQsRUErQzJCQSxDQS9DM0IsQ0FBb0IzRSxDQUFBLENBQUksQ0FBQyxFQUFELENBK0NHMkUsQ0EvQ0gsQ0FBWSxHQUFaLENBQWlCLENBQWpCLENBQXFCLE1BQzdDLE1BQWMsR0FBZCxFQThDMkJBLENBOUMzQixDQUFvQjNFLENBQUEsQ0FBSSxDQUFDLEVBQUQsQ0E4Q0cyRSxDQTlDSCxDQUFZLEdBQVosQ0FBaUIsQ0FBakIsQ0FBcUIsTUFDN0MsTUFBYyxHQUFkLEVBNkMyQkEsQ0E3QzNCLENBQW9CM0UsQ0FBQSxDQUFJLENBQUMsRUFBRCxDQTZDRzJFLENBN0NILENBQVksR0FBWixDQUFpQixDQUFqQixDQUFxQixNQUM3QyxNQUFjLEdBQWQsRUE0QzJCQSxDQTVDM0IsQ0FBb0IzRSxDQUFBLENBQUksQ0FBQyxFQUFELENBNENHMkUsQ0E1Q0gsQ0FBWSxHQUFaLENBQWlCLENBQWpCLENBQXFCLE1BQzdDLE1BQWMsR0FBZCxFQTJDMkJBLENBM0MzQixDQUFvQjNFLENBQUEsQ0FBSSxDQUFDLEVBQUQsQ0EyQ0cyRSxDQTNDSCxDQUFZLEdBQVosQ0FBaUIsQ0FBakIsQ0FBcUIsTUFDN0MsTUFBYyxJQUFkLEVBMEMyQkEsQ0ExQzNCLENBQXFCM0UsQ0FBQSxDQUFJLENBQUMsRUFBRCxDQTBDRTJFLENBMUNGLENBQVksR0FBWixDQUFpQixDQUFqQixDQUFxQixNQUM5QyxNQUFjLElBQWQsRUF5QzJCQSxDQXpDM0IsQ0FBcUIzRSxDQUFBLENBQUksQ0FBQyxFQUFELENBeUNFMkUsQ0F6Q0YsQ0FBWSxJQUFaLENBQWtCLENBQWxCLENBQXNCLE1BQy9DLE1BQWMsSUFBZCxFQXdDMkJBLENBeEMzQixDQUFxQjNFLENBQUEsQ0FBSSxDQUFDLEVBQUQsQ0F3Q0UyRSxDQXhDRixDQUFZLElBQVosQ0FBa0IsQ0FBbEIsQ0FBc0IsTUFDL0MsTUFBYyxJQUFkLEVBdUMyQkEsQ0F2QzNCLENBQXFCM0UsQ0FBQSxDQUFJLENBQUMsRUFBRCxDQXVDRTJFLENBdkNGLENBQVksSUFBWixDQUFrQixFQUFsQixDQUF1QixNQUNoRCxNQUFjLElBQWQsRUFzQzJCQSxDQXRDM0IsQ0FBcUIzRSxDQUFBLENBQUksQ0FBQyxFQUFELENBc0NFMkUsQ0F0Q0YsQ0FBWSxJQUFaLENBQWtCLEVBQWxCLENBQXVCLE1BQ2hELE1BQWMsSUFBZCxFQXFDMkJBLENBckMzQixDQUFxQjNFLENBQUEsQ0FBSSxDQUFDLEVBQUQsQ0FxQ0UyRSxDQXJDRixDQUFZLElBQVosQ0FBa0IsRUFBbEIsQ0FBdUIsTUFDaEQsTUFBYyxJQUFkLEVBb0MyQkEsQ0FwQzNCLENBQXFCM0UsQ0FBQSxDQUFJLENBQUMsRUFBRCxDQW9DRTJFLENBcENGLENBQVksSUFBWixDQUFrQixFQUFsQixDQUF1QixNQUNoRCxNQUFjLEtBQWQsRUFtQzJCQSxDQW5DM0IsQ0FBc0IzRSxDQUFBLENBQUksQ0FBQyxFQUFELENBbUNDMkUsQ0FuQ0QsQ0FBWSxJQUFaLENBQWtCLEVBQWxCLENBQXVCLE1BQ2pELE1BQWMsS0FBZDtBQWtDMkJBLENBbEMzQixDQUFzQjNFLENBQUEsQ0FBSSxDQUFDLEVBQUQsQ0FrQ0MyRSxDQWxDRCxDQUFZLEtBQVosQ0FBbUIsRUFBbkIsQ0FBd0IsTUFDbEQsTUFBYyxLQUFkLEVBaUMyQkEsQ0FqQzNCLENBQXNCM0UsQ0FBQSxDQUFJLENBQUMsRUFBRCxDQWlDQzJFLENBakNELENBQVksS0FBWixDQUFtQixFQUFuQixDQUF3QixNQUNsRCxNQUFjLEtBQWQsRUFnQzJCQSxDQWhDM0IsQ0FBc0IzRSxDQUFBLENBQUksQ0FBQyxFQUFELENBZ0NDMkUsQ0FoQ0QsQ0FBWSxLQUFaLENBQW1CLEVBQW5CLENBQXdCLE1BQ2xELFNBQVMsS0FBTSxrQkFBTixDQS9CWCxDQWtDQSxDQUFBLENBQU8zRSxDQTZCUG9GLEVBQUEsQ0FBVWhGLENBQUEsRUFBVixDQUFBLENBQW1CdUQsQ0FBQSxDQUFLLENBQUwsQ0FDbkJ5QixFQUFBLENBQVVoRixDQUFBLEVBQVYsQ0FBQSxDQUFtQnVELENBQUEsQ0FBSyxDQUFMLENBQ25CeUIsRUFBQSxDQUFVaEYsQ0FBQSxFQUFWLENBQUEsQ0FBbUJ1RCxDQUFBLENBQUssQ0FBTCxDQWdFakIsS0FBSTNFLENBQUosQ0FFSUMsQ0FFQ0QsRUFBQSxDQUFJLENBQVQsS0FBWUMsQ0FBWixDQWxFS21HLENBa0VZdkgsT0FBakIsQ0FBbUNtQixDQUFuQyxDQUF1Q0MsQ0FBdkMsQ0FBMkMsRUFBRUQsQ0FBN0MsQ0FDRXFHLENBQUEsQ0FBUWpGLENBQUEsRUFBUixDQUFBLENBbkVHZ0YsQ0FtRWMsQ0FBVXBHLENBQVYsQ0FFbkI4RSxFQUFBLENBckVLc0IsQ0FxRU8sQ0FBVSxDQUFWLENBQVosQ0FBQSxFQUNBcEIsRUFBQSxDQXRFS29CLENBc0VLLENBQVUsQ0FBVixDQUFWLENBQUEsRUFDQUUsRUFBQSxDQUFhSixDQUFBckgsT0FBYixDQUE0QnNILENBQTVCLENBQXFDLENBQ3JDSSxFQUFBLENBQVksSUFkcUIsQ0FoRG5DLElBQUl4RCxDQUFKLENBRUlsRSxDQUZKLENBSUltQixDQUpKLENBTUlDLENBTkosQ0FRSXVHLENBUkosQ0FVSXpGLEVBQVEsRUFWWixDQWNJMEYsQ0FkSixDQWdCSUMsQ0FoQkosQ0FrQklILENBbEJKLENBb0JJRixFQUFVckgsQ0FBQSxDQUNaLElBQUlFLFdBQUosQ0FBbUMsQ0FBbkMsQ0FBZ0IwRSxDQUFBL0UsT0FBaEIsQ0FEWSxDQUM0QixFQXJCMUMsQ0F1Qkl1QyxFQUFNLENBdkJWLENBeUJJa0YsRUFBYSxDQXpCakIsQ0EyQkl4QixFQUFjLEtBQUs5RixDQUFBLENBQWlCRyxXQUFqQixDQUErQk8sS0FBcEMsRUFBMkMsR0FBM0MsQ0EzQmxCLENBNkJJc0YsRUFBWSxLQUFLaEcsQ0FBQSxDQUFpQkcsV0FBakIsQ0FBK0JPLEtBQXBDLEVBQTJDLEVBQTNDLENBN0JoQixDQStCSThDLEdBQU8sQ0FBQUEsRUEvQlgsQ0FpQ0ltRSxDQUdKLElBQUksQ0FBQzNILENBQUwsQ0FBcUIsQ0FDbkIsSUFBS2dCLENBQUwsQ0FBUyxDQUFULENBQWlCLEdBQWpCLEVBQVlBLENBQVosQ0FBQSxDQUF5QjhFLENBQUEsQ0FBWTlFLENBQUEsRUFBWixDQUFBLENBQW1CLENBQzVDLEtBQUtBLENBQUwsQ0FBUyxDQUFULENBQWlCLEVBQWpCLEVBQVlBLENBQVosQ0FBQSxDQUF3QmdGLENBQUEsQ0FBVWhGLENBQUEsRUFBVixDQUFBLENBQWlCLENBRnRCLENBSXJCOEUsQ0FBQSxDQUFZLEdBQVosQ0FBQSxDQUFtQixDQTBCZC9CLEVBQUEsQ0FBVyxDQUFoQixLQUFtQmxFLENBQW5CLENBQTRCK0UsQ0FBQS9FLE9BQTVCLENBQThDa0UsQ0FBOUMsQ0FBeURsRSxDQUF6RCxDQUFpRSxFQUFFa0UsQ0FBbkUsQ0FBNkUsQ0FFeEQvQyxDQUFkO0FBQUF3RyxDQUFBLENBQVcsQ0FBaEIsS0FBMEJ2RyxDQUExQixDQS9uQjRCMkcsQ0ErbkI1QixDQUE4RDVHLENBQTlELENBQWtFQyxDQUFsRSxFQUNNOEMsQ0FETixDQUNpQi9DLENBRGpCLEdBQ3VCbkIsQ0FEdkIsQ0FBc0UsRUFBRW1CLENBQXhFLENBSUV3RyxDQUFBLENBQVlBLENBQVosRUFBd0IsQ0FBeEIsQ0FBNkI1QyxDQUFBLENBQVViLENBQVYsQ0FBcUIvQyxDQUFyQixDQUkzQmUsRUFBQSxDQUFNeUYsQ0FBTixDQUFKLEdBQXdCekgsQ0FBeEIsR0FBa0NnQyxDQUFBLENBQU15RixDQUFOLENBQWxDLENBQW9ELEVBQXBELENBQ0FDLEVBQUEsQ0FBWTFGLENBQUEsQ0FBTXlGLENBQU4sQ0FHWixJQUFJLEVBQWUsQ0FBZixDQUFBRixDQUFBLEVBQUEsQ0FBSixDQUFBLENBTUEsSUFBQSxDQUEwQixDQUExQixDQUFPRyxDQUFBNUgsT0FBUCxFQW5vQnlCZ0ksS0Ftb0J6QixDQUErQjlELENBQS9CLENBQTBDMEQsQ0FBQSxDQUFVLENBQVYsQ0FBMUMsQ0FBQSxDQUNFQSxDQUFBM0gsTUFBQSxFQUlGLElBQUlpRSxDQUFKLENBdHBCNEI2RCxDQXNwQjVCLEVBQWdEL0gsQ0FBaEQsQ0FBd0QsQ0FDbEQwSCxDQUFKLEVBQ0VOLENBQUEsQ0FBV00sQ0FBWCxDQUF1QixFQUF2QixDQUdHdkcsRUFBQSxDQUFJLENBQVQsS0FBWUMsQ0FBWixDQUFpQnBCLENBQWpCLENBQTBCa0UsQ0FBMUIsQ0FBb0MvQyxDQUFwQyxDQUF3Q0MsQ0FBeEMsQ0FBNEMsRUFBRUQsQ0FBOUMsQ0FDRTJHLENBRUEsQ0FGTS9DLENBQUEsQ0FBVWIsQ0FBVixDQUFxQi9DLENBQXJCLENBRU4sQ0FEQXFHLENBQUEsQ0FBUWpGLENBQUEsRUFBUixDQUNBLENBRGlCdUYsQ0FDakIsQ0FBQSxFQUFFN0IsQ0FBQSxDQUFZNkIsQ0FBWixDQUVKLE1BVnNELENBY2pDLENBQXZCLENBQUlGLENBQUE1SCxPQUFKLEVBQ0U2SCxDQUVBLENBRmVJLEVBQUEsQ0FBeUJsRCxDQUF6QixDQUFvQ2IsQ0FBcEMsQ0FBOEMwRCxDQUE5QyxDQUVmLENBQUlGLENBQUosQ0FFTUEsQ0FBQTFILE9BQUosQ0FBdUI2SCxDQUFBN0gsT0FBdkIsRUFFRThILENBS0EsQ0FMTS9DLENBQUEsQ0FBVWIsQ0FBVixDQUFxQixDQUFyQixDQUtOLENBSkFzRCxDQUFBLENBQVFqRixDQUFBLEVBQVIsQ0FJQSxDQUppQnVGLENBSWpCLENBSEEsRUFBRTdCLENBQUEsQ0FBWTZCLENBQVosQ0FHRixDQUFBVixDQUFBLENBQVdTLENBQVgsQ0FBeUIsQ0FBekIsQ0FQRixFQVVFVCxDQUFBLENBQVdNLENBQVgsQ0FBdUIsRUFBdkIsQ0FaSixDQWNXRyxDQUFBN0gsT0FBSixDQUEwQjJELEVBQTFCLENBQ0wrRCxDQURLLENBQ09HLENBRFAsQ0FHTFQsQ0FBQSxDQUFXUyxDQUFYLENBQXlCLENBQXpCLENBcEJKLEVBdUJXSCxDQUFKLENBQ0xOLENBQUEsQ0FBV00sQ0FBWCxDQUF1QixFQUF2QixDQURLLEVBR0xJLENBRUEsQ0FGTS9DLENBQUEsQ0FBVWIsQ0FBVixDQUVOLENBREFzRCxDQUFBLENBQVFqRixDQUFBLEVBQVIsQ0FDQSxDQURpQnVGLENBQ2pCLENBQUEsRUFBRTdCLENBQUEsQ0FBWTZCLENBQVosQ0FMRyxDQWhEUCxDQUNFRixDQUFBOUUsS0FBQSxDQUFlb0IsQ0FBZixDQWZ5RSxDQTBFN0VzRCxDQUFBLENBQVFqRixDQUFBLEVBQVIsQ0FBQSxDQUFpQixHQUNqQjBELEVBQUEsQ0FBWSxHQUFaLENBQUEsRUFDQSxFQUFBQSxFQUFBLENBQW1CQSxDQUNuQixFQUFBRSxFQUFBLENBQWlCQSxDQUVqQixPQUNFaEcsRUFBQSxDQUFrQnFILENBQUF2RixTQUFBLENBQWlCLENBQWpCLENBQW9CTSxDQUFwQixDQUFsQixDQUE2Q2lGLENBcEpJO0FBaUtyRFUsUUFBUSxHQUFBLENBQUM1RixDQUFELENBQU80QixDQUFQLENBQWlCMEQsQ0FBakIsQ0FBNEIsQ0FBQSxJQUM5QlAsQ0FEOEIsQ0FFOUJjLENBRjhCLENBRzlCQyxFQUFXLENBSG1CLENBR2hCQyxDQUhnQixDQUk5QmxILENBSjhCLENBSTNCa0YsQ0FKMkIsQ0FJeEJFLENBSndCLENBSXJCK0IsRUFBS2hHLENBQUF0QyxPQUlibUIsRUFBQSxDQUFJLENBQUdvRixFQUFQLENBQVdxQixDQUFBNUgsT0FEaEIsRUFBQSxDQUNBLElBQUEsQ0FBa0NtQixDQUFsQyxDQUFzQ29GLENBQXRDLENBQXlDcEYsQ0FBQSxFQUF6QyxDQUE4QyxDQUM1Q2tHLENBQUEsQ0FBUU8sQ0FBQSxDQUFVckIsQ0FBVixDQUFjcEYsQ0FBZCxDQUFrQixDQUFsQixDQUNSa0gsRUFBQSxDQXB1QjRCTixDQXV1QjVCLElBdnVCNEJBLENBdXVCNUIsQ0FBSUssQ0FBSixDQUE4QyxDQUM1QyxJQUFLL0IsQ0FBTCxDQUFTK0IsQ0FBVCxDQXh1QjBCTCxDQXd1QjFCLENBQW1CMUIsQ0FBbkIsQ0FBc0RBLENBQUEsRUFBdEQsQ0FDRSxHQUFJL0QsQ0FBQSxDQUFLK0UsQ0FBTCxDQUFhaEIsQ0FBYixDQUFpQixDQUFqQixDQUFKLEdBQTRCL0QsQ0FBQSxDQUFLNEIsQ0FBTCxDQUFnQm1DLENBQWhCLENBQW9CLENBQXBCLENBQTVCLENBQ0UsU0FBUyxDQUdiZ0MsRUFBQSxDQUFjRCxDQU44QixDQVU5QyxJQUFBLENBMXVCNEJHLEdBMHVCNUIsQ0FBT0YsQ0FBUCxFQUNPbkUsQ0FEUCxDQUNrQm1FLENBRGxCLENBQ2dDQyxDQURoQyxFQUVPaEcsQ0FBQSxDQUFLK0UsQ0FBTCxDQUFhZ0IsQ0FBYixDQUZQLEdBRXFDL0YsQ0FBQSxDQUFLNEIsQ0FBTCxDQUFnQm1FLENBQWhCLENBRnJDLENBQUEsQ0FHRSxFQUFFQSxDQUlBQSxFQUFKLENBQWtCRCxDQUFsQixHQUNFRCxDQUNBLENBRGVkLENBQ2YsQ0FBQWUsQ0FBQSxDQUFXQyxDQUZiLENBTUEsSUF2dkI0QkUsR0F1dkI1QixHQUFJRixDQUFKLENBQ0UsS0E3QjBDLENBaUM5QyxNQUFPLEtBQUl0QixFQUFKLENBQThCcUIsQ0FBOUIsQ0FBd0NsRSxDQUF4QyxDQUFtRGlFLENBQW5ELENBekMyQjtBQW9LSUssUUFBUSxHQUFBLENBQUM3QixDQUFELENBQVE4QixDQUFSLENBQWUsQ0FFN0QsSUFBSUMsRUFBVy9CLENBQUEzRyxPQUFmLENBRUlrRCxFQUFPLElBQUlQLENBQUosQ0FBYyxHQUFkLENBRlgsQ0FJSTNDLEVBQVMsS0FBS0csQ0FBQSxDQUFpQkMsVUFBakIsQ0FBOEJTLEtBQW5DLEVBQTBDNkgsQ0FBMUMsQ0FKYixDQU1JQyxDQU5KLENBUUlDLENBUkosQ0FVSUMsQ0FWSixDQVlJMUgsQ0FaSixDQWNJQyxDQUdKLElBQUksQ0FBQ2pCLENBQUwsQ0FDRSxJQUFLZ0IsQ0FBTCxDQUFTLENBQVQsQ0FBWUEsQ0FBWixDQUFnQnVILENBQWhCLENBQTBCdkgsQ0FBQSxFQUExQixDQUNFbkIsQ0FBQSxDQUFPbUIsQ0FBUCxDQUFBLENBQVksQ0FLaEIsS0FBS0EsQ0FBTCxDQUFTLENBQVQsQ0FBWUEsQ0FBWixDQUFnQnVILENBQWhCLENBQTBCLEVBQUV2SCxDQUE1QixDQUNpQixDQUFmLENBQUl3RixDQUFBLENBQU14RixDQUFOLENBQUosRUFDRStCLENBQUFKLEtBQUEsQ0FBVTNCLENBQVYsQ0FBYXdGLENBQUEsQ0FBTXhGLENBQU4sQ0FBYixDQUdKd0gsRUFBQSxDQUFZOUgsS0FBSixDQUFVcUMsQ0FBQWxELE9BQVYsQ0FBd0IsQ0FBeEIsQ0FDUjRJLEVBQUEsQ0FBUyxLQUFLekksQ0FBQSxDQUFpQkcsV0FBakIsQ0FBK0JPLEtBQXBDLEVBQTJDcUMsQ0FBQWxELE9BQTNDLENBQXlELENBQXpELENBR1QsSUFBcUIsQ0FBckIsR0FBSTJJLENBQUEzSSxPQUFKLENBRUUsTUFEQUEsRUFBQSxDQUFPa0QsQ0FBQUUsSUFBQSxFQUFBekMsTUFBUCxDQUNPWCxDQURvQixDQUNwQkEsQ0FBQUEsQ0FJSm1CLEVBQUEsQ0FBSSxDQUFULEtBQVlDLENBQVosQ0FBaUI4QixDQUFBbEQsT0FBakIsQ0FBK0IsQ0FBL0IsQ0FBa0NtQixDQUFsQyxDQUFzQ0MsQ0FBdEMsQ0FBMEMsRUFBRUQsQ0FBNUMsQ0FDRXdILENBQUEsQ0FBTXhILENBQU4sQ0FDQSxDQURXK0IsQ0FBQUUsSUFBQSxFQUNYLENBQUF3RixDQUFBLENBQU96SCxDQUFQLENBQUEsQ0FBWXdILENBQUEsQ0FBTXhILENBQU4sQ0FBQTZCLE1BRWQ2RixFQUFBLENBQWFDLEVBQUEsQ0FBMEJGLENBQTFCLENBQWtDQSxDQUFBNUksT0FBbEMsQ0FBaUR5SSxDQUFqRCxDQUVSdEgsRUFBQSxDQUFJLENBQVQsS0FBWUMsQ0FBWixDQUFpQnVILENBQUEzSSxPQUFqQixDQUErQm1CLENBQS9CLENBQW1DQyxDQUFuQyxDQUF1QyxFQUFFRCxDQUF6QyxDQUNFbkIsQ0FBQSxDQUFPMkksQ0FBQSxDQUFNeEgsQ0FBTixDQUFBUixNQUFQLENBQUEsQ0FBeUJrSSxDQUFBLENBQVcxSCxDQUFYLENBRzNCLE9BQU9uQixFQW5Ec0Q7QUE2RGQrSSxRQUFRLEdBQUEsQ0FBQ3BDLENBQUQsQ0FBUXFDLENBQVIsQ0FBaUJQLENBQWpCLENBQXdCLENBK0IvRVEsUUFBU0EsRUFBVyxDQUFDNUMsQ0FBRCxDQUFJLENBRXRCLElBQUk2QyxFQUFJQyxDQUFBLENBQUs5QyxDQUFMLENBQUEsQ0FBUStDLENBQUEsQ0FBZ0IvQyxDQUFoQixDQUFSLENBRUo2QyxFQUFKLEdBQVVGLENBQVYsRUFDRUMsQ0FBQSxDQUFZNUMsQ0FBWixDQUFjLENBQWQsQ0FDQSxDQUFBNEMsQ0FBQSxDQUFZNUMsQ0FBWixDQUFjLENBQWQsQ0FGRixFQUlFLEVBQUV3QyxDQUFBLENBQVdLLENBQVgsQ0FHSixHQUFFRSxDQUFBLENBQWdCL0MsQ0FBaEIsQ0FYb0IsQ0E3QnhCLElBQUlnRCxFQUFjLEtBQUtsSixDQUFBLENBQWlCRSxXQUFqQixDQUErQlEsS0FBcEMsRUFBMkM0SCxDQUEzQyxDQUFsQixDQUVJYSxFQUFPLEtBQUtuSixDQUFBLENBQWlCQyxVQUFqQixDQUE4QlMsS0FBbkMsRUFBMEM0SCxDQUExQyxDQUZYLENBSUlJLEVBQWEsS0FBSzFJLENBQUEsQ0FBaUJDLFVBQWpCLENBQThCUyxLQUFuQyxFQUEwQ21JLENBQTFDLENBSmpCLENBTUloRyxFQUFZbkMsS0FBSixDQUFVNEgsQ0FBVixDQU5aLENBUUlVLEVBQVl0SSxLQUFKLENBQVU0SCxDQUFWLENBUlosQ0FVSVcsRUFBc0J2SSxLQUFKLENBQVU0SCxDQUFWLENBVnRCLENBWUljLEdBQVUsQ0FBVkEsRUFBZWQsQ0FBZmMsRUFBd0JQLENBWjVCLENBY0lRLEVBQVEsQ0FBUkEsRUFBY2YsQ0FBZGUsQ0FBc0IsQ0FkMUIsQ0FnQklySSxDQWhCSixDQWtCSWtGLENBbEJKLENBb0JJb0QsQ0FwQkosQ0FzQklDLENBdEJKLENBd0JJQyxDQW1CSk4sRUFBQSxDQUFZWixDQUFaLENBQWtCLENBQWxCLENBQUEsQ0FBdUJPLENBRXZCLEtBQUszQyxDQUFMLENBQVMsQ0FBVCxDQUFZQSxDQUFaLENBQWdCb0MsQ0FBaEIsQ0FBdUIsRUFBRXBDLENBQXpCLENBQ01rRCxDQUFKLENBQWFDLENBQWIsQ0FDRUYsQ0FBQSxDQUFLakQsQ0FBTCxDQURGLENBQ1ksQ0FEWixFQUdFaUQsQ0FBQSxDQUFLakQsQ0FBTCxDQUNBLENBRFUsQ0FDVixDQUFBa0QsQ0FBQSxFQUFVQyxDQUpaLENBT0EsQ0FEQUQsQ0FDQSxHQURXLENBQ1gsQ0FBQUYsQ0FBQSxDQUFZWixDQUFaLENBQWtCLENBQWxCLENBQW9CcEMsQ0FBcEIsQ0FBQSxFQUEwQmdELENBQUEsQ0FBWVosQ0FBWixDQUFrQixDQUFsQixDQUFvQnBDLENBQXBCLENBQTFCLENBQW1ELENBQW5ELENBQXVELENBQXZELEVBQTREMkMsQ0FFOURLLEVBQUEsQ0FBWSxDQUFaLENBQUEsQ0FBaUJDLENBQUEsQ0FBSyxDQUFMLENBRWpCdEcsRUFBQSxDQUFNLENBQU4sQ0FBQSxDQUFlbkMsS0FBSixDQUFVd0ksQ0FBQSxDQUFZLENBQVosQ0FBVixDQUNYRixFQUFBLENBQUssQ0FBTCxDQUFBLENBQWV0SSxLQUFKLENBQVV3SSxDQUFBLENBQVksQ0FBWixDQUFWLENBQ1gsS0FBS2hELENBQUwsQ0FBUyxDQUFULENBQVlBLENBQVosQ0FBZ0JvQyxDQUFoQixDQUF1QixFQUFFcEMsQ0FBekIsQ0FDTWdELENBQUEsQ0FBWWhELENBQVosQ0FJSixDQUpxQixDQUlyQixDQUp5QmdELENBQUEsQ0FBWWhELENBQVosQ0FBYyxDQUFkLENBSXpCLENBSjRDaUQsQ0FBQSxDQUFLakQsQ0FBTCxDQUk1QyxHQUhFZ0QsQ0FBQSxDQUFZaEQsQ0FBWixDQUdGLENBSG1CLENBR25CLENBSHVCZ0QsQ0FBQSxDQUFZaEQsQ0FBWixDQUFjLENBQWQsQ0FHdkIsQ0FIMENpRCxDQUFBLENBQUtqRCxDQUFMLENBRzFDLEVBREFyRCxDQUFBLENBQU1xRCxDQUFOLENBQ0EsQ0FEZXhGLEtBQUosQ0FBVXdJLENBQUEsQ0FBWWhELENBQVosQ0FBVixDQUNYLENBQUE4QyxDQUFBLENBQUs5QyxDQUFMLENBQUEsQ0FBZXhGLEtBQUosQ0FBVXdJLENBQUEsQ0FBWWhELENBQVosQ0FBVixDQUdiLEtBQUtsRixDQUFMLENBQVMsQ0FBVCxDQUFZQSxDQUFaLENBQWdCNkgsQ0FBaEIsQ0FBeUIsRUFBRTdILENBQTNCLENBQ0UwSCxDQUFBLENBQVcxSCxDQUFYLENBQUEsQ0FBZ0JzSCxDQUdsQixLQUFLZ0IsQ0FBTCxDQUFTLENBQVQsQ0FBWUEsQ0FBWixDQUFnQkosQ0FBQSxDQUFZWixDQUFaLENBQWtCLENBQWxCLENBQWhCLENBQXNDLEVBQUVnQixDQUF4QyxDQUNFekcsQ0FBQSxDQUFNeUYsQ0FBTjtBQUFZLENBQVosQ0FBQSxDQUFlZ0IsQ0FBZixDQUNBLENBRG9COUMsQ0FBQSxDQUFNOEMsQ0FBTixDQUNwQixDQUFBTixDQUFBLENBQUtWLENBQUwsQ0FBVyxDQUFYLENBQUEsQ0FBY2dCLENBQWQsQ0FBQSxDQUFvQkEsQ0FHdEIsS0FBS3RJLENBQUwsQ0FBUyxDQUFULENBQVlBLENBQVosQ0FBZ0JzSCxDQUFoQixDQUF1QixFQUFFdEgsQ0FBekIsQ0FDRWlJLENBQUEsQ0FBZ0JqSSxDQUFoQixDQUFBLENBQXFCLENBRUQsRUFBdEIsR0FBSW1JLENBQUEsQ0FBS2IsQ0FBTCxDQUFXLENBQVgsQ0FBSixHQUNFLEVBQUVJLENBQUEsQ0FBVyxDQUFYLENBQ0YsQ0FBQSxFQUFFTyxDQUFBLENBQWdCWCxDQUFoQixDQUFzQixDQUF0QixDQUZKLENBS0EsS0FBS3BDLENBQUwsQ0FBU29DLENBQVQsQ0FBZSxDQUFmLENBQXVCLENBQXZCLEVBQWtCcEMsQ0FBbEIsQ0FBMEIsRUFBRUEsQ0FBNUIsQ0FBK0IsQ0FFN0JxRCxDQUFBLENBREF2SSxDQUNBLENBREksQ0FFSndJLEVBQUEsQ0FBT1AsQ0FBQSxDQUFnQi9DLENBQWhCLENBQWtCLENBQWxCLENBRVAsS0FBS29ELENBQUwsQ0FBUyxDQUFULENBQVlBLENBQVosQ0FBZ0JKLENBQUEsQ0FBWWhELENBQVosQ0FBaEIsQ0FBZ0NvRCxDQUFBLEVBQWhDLENBQ0VDLENBRUEsQ0FGUzFHLENBQUEsQ0FBTXFELENBQU4sQ0FBUSxDQUFSLENBQUEsQ0FBV3NELENBQVgsQ0FFVCxDQUY0QjNHLENBQUEsQ0FBTXFELENBQU4sQ0FBUSxDQUFSLENBQUEsQ0FBV3NELENBQVgsQ0FBZ0IsQ0FBaEIsQ0FFNUIsQ0FBSUQsQ0FBSixDQUFhL0MsQ0FBQSxDQUFNeEYsQ0FBTixDQUFiLEVBQ0U2QixDQUFBLENBQU1xRCxDQUFOLENBQUEsQ0FBU29ELENBQVQsQ0FFQSxDQUZjQyxDQUVkLENBREFQLENBQUEsQ0FBSzlDLENBQUwsQ0FBQSxDQUFRb0QsQ0FBUixDQUNBLENBRGFULENBQ2IsQ0FBQVcsQ0FBQSxFQUFRLENBSFYsR0FLRTNHLENBQUEsQ0FBTXFELENBQU4sQ0FBQSxDQUFTb0QsQ0FBVCxDQUVBLENBRmM5QyxDQUFBLENBQU14RixDQUFOLENBRWQsQ0FEQWdJLENBQUEsQ0FBSzlDLENBQUwsQ0FBQSxDQUFRb0QsQ0FBUixDQUNBLENBRGF0SSxDQUNiLENBQUEsRUFBRUEsQ0FQSixDQVdGaUksRUFBQSxDQUFnQi9DLENBQWhCLENBQUEsQ0FBcUIsQ0FDTCxFQUFoQixHQUFJaUQsQ0FBQSxDQUFLakQsQ0FBTCxDQUFKLEVBQ0U0QyxDQUFBLENBQVk1QyxDQUFaLENBckIyQixDQXlCL0IsTUFBT3dDLEVBL0d3RTtBQXlIaENlLFFBQVEsR0FBQSxDQUFDQyxDQUFELENBQVUsQ0FBQSxJQUM3RGpELEVBQVEsS0FBS3pHLENBQUEsQ0FBaUJFLFdBQWpCLENBQStCUSxLQUFwQyxFQUEyQ2dKLENBQUE3SixPQUEzQyxDQURxRCxDQUU3RDhKLEVBQVEsRUFGcUQsQ0FHN0RDLEVBQVksRUFIaUQsQ0FJN0RqRSxFQUFPLENBSnNELENBSW5EM0UsQ0FKbUQsQ0FJaERDLENBSmdELENBSTVDaUYsQ0FKNEMsQ0FJekMyRCxDQUduQjdJLEVBQUEsQ0FBSSxDQUFULEtBQVlDLENBQVosQ0FBaUJ5SSxDQUFBN0osT0FBakIsQ0FBaUNtQixDQUFqQyxDQUFxQ0MsQ0FBckMsQ0FBeUNELENBQUEsRUFBekMsQ0FDRTJJLENBQUEsQ0FBTUQsQ0FBQSxDQUFRMUksQ0FBUixDQUFOLENBQUEsRUFBcUIySSxDQUFBLENBQU1ELENBQUEsQ0FBUTFJLENBQVIsQ0FBTixDQUFyQixDQUF5QyxDQUF6QyxFQUE4QyxDQUkzQ0EsRUFBQSxDQUFJLENBQVQsS0FBWUMsQ0FBWixDQTNpQzhCNkksRUEyaUM5QixDQUFnRDlJLENBQWhELEVBQXFEQyxDQUFyRCxDQUF5REQsQ0FBQSxFQUF6RCxDQUNFNEksQ0FBQSxDQUFVNUksQ0FBVixDQUVBLENBRmUyRSxDQUVmLENBREFBLENBQ0EsRUFEUWdFLENBQUEsQ0FBTTNJLENBQU4sQ0FDUixDQURtQixDQUNuQixDQUFBMkUsQ0FBQSxHQUFTLENBSU4zRSxFQUFBLENBQUksQ0FBVCxLQUFZQyxDQUFaLENBQWlCeUksQ0FBQTdKLE9BQWpCLENBQWlDbUIsQ0FBakMsQ0FBcUNDLENBQXJDLENBQXlDRCxDQUFBLEVBQXpDLENBQThDLENBQzVDMkUsQ0FBQSxDQUFPaUUsQ0FBQSxDQUFVRixDQUFBLENBQVExSSxDQUFSLENBQVYsQ0FDUDRJLEVBQUEsQ0FBVUYsQ0FBQSxDQUFRMUksQ0FBUixDQUFWLENBQUEsRUFBeUIsQ0FHcEJrRixFQUFBLENBRkxPLENBQUEsQ0FBTXpGLENBQU4sQ0FFSyxDQUZNLENBRVgsS0FBWTZJLENBQVosQ0FBZ0JILENBQUEsQ0FBUTFJLENBQVIsQ0FBaEIsQ0FBNEJrRixDQUE1QixDQUFnQzJELENBQWhDLENBQW1DM0QsQ0FBQSxFQUFuQyxDQUNFTyxDQUFBLENBQU16RixDQUFOLENBQ0EsQ0FEWXlGLENBQUEsQ0FBTXpGLENBQU4sQ0FDWixFQUR3QixDQUN4QixDQUQ4QjJFLENBQzlCLENBRHFDLENBQ3JDLENBQUFBLENBQUEsSUFBVSxDQVBnQyxDQVc5QyxNQUFPYyxFQTlCMEQsQyxDQ3BuQ3ZEc0QsUUFBUSxHQUFBLENBQUMzRyxDQUFELENBQVFDLENBQVIsQ0FBb0IsQ0FFdEMsSUFBQUQsTUFBQSxDQUFhQSxDQU1iLEtBQUFLLEVBQUEsQ0FKQSxJQUFBdUcsRUFJQSxDQUpVLENBTVYsS0FBQUMsRUFBQSxDQUFhLEVBU1Q1RyxFQUFKLEdBQ01BLENBQUEsTUFTSixHQVJFLElBQUE0RyxFQVFGLENBUmU1RyxDQUFBLE1BUWYsRUFOc0MsUUFNdEMsR0FOSSxNQUFPQSxFQUFBLFNBTVgsR0FMRSxJQUFBNkcsU0FLRixDQUxrQjdHLENBQUEsU0FLbEIsRUFIcUMsUUFHckMsR0FISSxNQUFPQSxFQUFBLFFBR1gsR0FGRSxJQUFBOEcsRUFFRixDQUZpQjlHLENBQUEsUUFFakIsRUFBSUEsQ0FBQSxlQUFKLEdBQ0UsSUFBQStHLEVBREYsQ0FDd0IvRyxDQUFBLGVBRHhCLENBVkYsQ0FlSyxLQUFBK0csRUFBTCxHQUNFLElBQUFBLEVBREYsQ0FDd0IsRUFEeEIsQ0FsQ3NDO0FBaUR4Q0wsRUFBQTVJLFVBQUF5QyxFQUFBLENBQStCeUcsUUFBUSxFQUFHLENBRXhDLElBQUlDLENBQUosQ0FFSUMsQ0FGSixDQUlJQyxDQUpKLENBTUlDLENBTkosQ0FRSUMsQ0FSSixDQVVJNUQsQ0FWSixDQVlJOUYsQ0FaSixDQWNJQyxDQWRKLENBZ0JJWSxFQUNGLEtBQUs3QixDQUFBLENBQWlCQyxVQUFqQixDQUE4QlMsS0FBbkMsRUF6QjBCaUssS0F5QjFCLENBakJGLENBbUJJbEgsRUFBSyxDQW5CVCxDQXFCSUwsRUFBUSxJQUFBQSxNQXJCWixDQXNCSTRHLEVBQUssSUFBQUEsRUF0QlQsQ0F1QklFLEVBQVcsSUFBQUEsU0F2QmYsQ0F3QklDLEVBQVUsSUFBQUEsRUFHZHRJLEVBQUEsQ0FBTzRCLENBQUEsRUFBUCxDQUFBLENBQWUsRUFDZjVCLEVBQUEsQ0FBTzRCLENBQUEsRUFBUCxDQUFBLENBQWUsR0FHZjVCLEVBQUEsQ0FBTzRCLENBQUEsRUFBUCxDQUFBLENBQWUsQ0FHZjZHLEVBQUEsQ0FBTSxDQUNGLEtBQUFMLEVBQUEsTUFBSixHQUE0QkssQ0FBNUIsRUFBbUNNLEVBQW5DLENBQ0ksS0FBQVgsRUFBQSxTQUFKLEdBQTRCSyxDQUE1QixFQUFtQ08sRUFBbkMsQ0FDSSxLQUFBWixFQUFBLE1BQUosR0FBNEJLLENBQTVCLEVBQW1DUSxFQUFuQyxDQUdBakosRUFBQSxDQUFPNEIsQ0FBQSxFQUFQLENBQUEsQ0FBZTZHLENBR2ZDLEVBQUEsRUFBU1EsSUFBQUMsSUFBQSxDQUFXRCxJQUFBQyxJQUFBLEVBQVgsQ0FBd0IsQ0FBQyxJQUFJRCxJQUF0QyxFQUFnRCxHQUFoRCxDQUF1RCxDQUN2RGxKLEVBQUEsQ0FBTzRCLENBQUEsRUFBUCxDQUFBLENBQWU4RyxDQUFmLENBQThCLEdBQzlCMUksRUFBQSxDQUFPNEIsQ0FBQSxFQUFQLENBQUEsQ0FBZThHLENBQWYsR0FBMEIsQ0FBMUIsQ0FBOEIsR0FDOUIxSSxFQUFBLENBQU80QixDQUFBLEVBQVAsQ0FBQSxDQUFlOEcsQ0FBZixHQUF5QixFQUF6QixDQUE4QixHQUM5QjFJLEVBQUEsQ0FBTzRCLENBQUEsRUFBUCxDQUFBLENBQWU4RyxDQUFmLEdBQXlCLEVBQXpCLENBQThCLEdBRzlCMUksRUFBQSxDQUFPNEIsQ0FBQSxFQUFQLENBQUEsQ0FBZSxDQUdmNUIsRUFBQSxDQUFPNEIsQ0FBQSxFQUFQLENBQUEsQ0FBZXdILEVBTWYsSUFBSSxJQUFBaEIsRUFBQSxNQUFKLEdBQTRCbEssQ0FBNUIsQ0FBb0MsQ0FDN0JpQixDQUFBLENBQUksQ0FBVCxLQUFZQyxDQUFaLENBQWlCaUosQ0FBQXJLLE9BQWpCLENBQWtDbUIsQ0FBbEMsQ0FBc0NDLENBQXRDLENBQTBDLEVBQUVELENBQTVDLENBQ0U4RixDQUVBLENBRklvRCxDQUFBZ0IsV0FBQSxDQUFvQmxLLENBQXBCLENBRUosQ0FEUSxHQUNSLENBREk4RixDQUNKLEdBRGdCakYsQ0FBQSxDQUFPNEIsQ0FBQSxFQUFQLENBQ2hCLENBRGdDcUQsQ0FDaEMsR0FEc0MsQ0FDdEMsQ0FEMkMsR0FDM0MsRUFBQWpGLENBQUEsQ0FBTzRCLENBQUEsRUFBUCxDQUFBLENBQWVxRCxDQUFmLENBQW1CLEdBRXJCakYsRUFBQSxDQUFPNEIsQ0FBQSxFQUFQLENBQUEsQ0FBZSxDQU5tQixDQVVwQyxHQUFJLElBQUF3RyxFQUFBLFFBQUosQ0FBMkIsQ0FDcEJqSixDQUFBO0FBQUksQ0FBVCxLQUFZQyxDQUFaLENBQWlCa0osQ0FBQXRLLE9BQWpCLENBQWlDbUIsQ0FBakMsQ0FBcUNDLENBQXJDLENBQXlDLEVBQUVELENBQTNDLENBQ0U4RixDQUVBLENBRklxRCxDQUFBZSxXQUFBLENBQW1CbEssQ0FBbkIsQ0FFSixDQURRLEdBQ1IsQ0FESThGLENBQ0osR0FEZ0JqRixDQUFBLENBQU80QixDQUFBLEVBQVAsQ0FDaEIsQ0FEZ0NxRCxDQUNoQyxHQURzQyxDQUN0QyxDQUQyQyxHQUMzQyxFQUFBakYsQ0FBQSxDQUFPNEIsQ0FBQSxFQUFQLENBQUEsQ0FBZXFELENBQWYsQ0FBbUIsR0FFckJqRixFQUFBLENBQU80QixDQUFBLEVBQVAsQ0FBQSxDQUFlLENBTlUsQ0FVdkIsSUFBQXdHLEVBQUEsTUFBSixHQUNFTyxDQUVBLENIaklLdEksRUFBQSxDRytIbUJMLENIL0huQixDRytIMkJPLENIL0gzQixDRytIOEJxQixDSC9IOUIsQ0dpSUwsQ0FGeUMsS0FFekMsQ0FEQTVCLENBQUEsQ0FBTzRCLENBQUEsRUFBUCxDQUNBLENBRGdCK0csQ0FDaEIsQ0FEK0IsR0FDL0IsQ0FBQTNJLENBQUEsQ0FBTzRCLENBQUEsRUFBUCxDQUFBLENBQWdCK0csQ0FBaEIsR0FBMEIsQ0FBMUIsQ0FBK0IsR0FIakMsQ0FPQSxLQUFBSixFQUFBLGFBQUEsQ0FBc0N2SSxDQUN0QyxLQUFBdUksRUFBQSxZQUFBLENBQXFDM0csQ0FHckNpSCxFQUFBLENBQWEsSUFBSXZILEVBQUosQ0FBb0JDLENBQXBCLENBQTJCLElBQUFnSCxFQUEzQixDQUNidkksRUFBQSxDQUFTNkksQ0FBQTlHLEVBQUEsRUFDVEgsRUFBQSxDQUFLaUgsQ0FBQWpILEVBR0R6RCxFQUFKLEdBQ015RCxDQUFKLENBQVMsQ0FBVCxDQUFhNUIsQ0FBQXZCLE9BQUE2SyxXQUFiLEVBQ0UsSUFBQXRKLEVBRUEsQ0FGYyxJQUFJNUIsVUFBSixDQUFld0QsQ0FBZixDQUFvQixDQUFwQixDQUVkLENBREEsSUFBQTVCLEVBQUFYLElBQUEsQ0FBZ0IsSUFBSWpCLFVBQUosQ0FBZTRCLENBQUF2QixPQUFmLENBQWhCLENBQ0EsQ0FBQXVCLENBQUEsQ0FBUyxJQUFBQSxFQUhYLEVBS0VBLENBTEYsQ0FLVyxJQUFJNUIsVUFBSixDQUFlNEIsQ0FBQXZCLE9BQWYsQ0FOYixDQVdBbUssRUFBQSxDSHpKT3ZJLEVBQUEsQ0d5SmlCa0IsQ0h6SmpCLENHeUpDckQsQ0h6SkQsQ0d5SkNBLENIekpELENHMEpQOEIsRUFBQSxDQUFPNEIsQ0FBQSxFQUFQLENBQUEsQ0FBZ0JnSCxDQUFoQixDQUFnQyxHQUNoQzVJLEVBQUEsQ0FBTzRCLENBQUEsRUFBUCxDQUFBLENBQWdCZ0gsQ0FBaEIsR0FBMkIsQ0FBM0IsQ0FBZ0MsR0FDaEM1SSxFQUFBLENBQU80QixDQUFBLEVBQVAsQ0FBQSxDQUFnQmdILENBQWhCLEdBQTBCLEVBQTFCLENBQWdDLEdBQ2hDNUksRUFBQSxDQUFPNEIsQ0FBQSxFQUFQLENBQUEsQ0FBZ0JnSCxDQUFoQixHQUEwQixFQUExQixDQUFnQyxHQUdoQ3hKLEVBQUEsQ0FBS21DLENBQUF2RCxPQUNMZ0MsRUFBQSxDQUFPNEIsQ0FBQSxFQUFQLENBQUEsQ0FBZ0J4QyxDQUFoQixDQUE2QixHQUM3QlksRUFBQSxDQUFPNEIsQ0FBQSxFQUFQLENBQUEsQ0FBZ0J4QyxDQUFoQixHQUF3QixDQUF4QixDQUE2QixHQUM3QlksRUFBQSxDQUFPNEIsQ0FBQSxFQUFQLENBQUEsQ0FBZ0J4QyxDQUFoQixHQUF1QixFQUF2QixDQUE2QixHQUM3QlksRUFBQSxDQUFPNEIsQ0FBQSxFQUFQLENBQUE7QUFBZ0J4QyxDQUFoQixHQUF1QixFQUF2QixDQUE2QixHQUU3QixLQUFBK0ksRUFBQSxDQUFVQSxDQUVOaEssRUFBSixFQUFzQnlELENBQXRCLENBQTJCNUIsQ0FBQWhDLE9BQTNCLEdBQ0UsSUFBQWdDLEVBREYsQ0FDZ0JBLENBRGhCLENBQ3lCQSxDQUFBQyxTQUFBLENBQWdCLENBQWhCLENBQW1CMkIsQ0FBbkIsQ0FEekIsQ0FJQSxPQUFPNUIsRUEvSGlDLENBa0p4Q3VKLEtBQUFBLEdBQVNBLEdBQVRBLENBTUFDLEdBQU9BLENBTlBELENBUUFFLEdBQU9BLENBUlBGLENBU0FHLEdBQVVBLEUsQ042bkNWbE0sRUFBQSxDT3YxQ2dCbU0sV1B1MUNoQixDT3YxQzZCekIsRVB1MUM3QixDQUFBMUssR0FBQSxDT3IxQ0FtTSw4QlBxMUNBLENPcDFDQXpCLEVBQUE1SSxVQUFBeUMsRVBvMUNBOyIsInNvdXJjZXMiOlsiY2xvc3VyZS1wcmltaXRpdmVzL2Jhc2UuanMiLCJkZWZpbmUvdHlwZWRhcnJheS9oeWJyaWQuanMiLCJzcmMvYml0c3RyZWFtLmpzIiwic3JjL2NyYzMyLmpzIiwic3JjL2hlYXAuanMiLCJzcmMvcmF3ZGVmbGF0ZS5qcyIsInNyYy9nemlwLmpzIiwiZXhwb3J0L2d6aXAuanMiXSwibmFtZXMiOlsiZ29vZy5nbG9iYWwiLCJnb29nLmV4cG9ydFBhdGhfIiwibmFtZSIsIm9wdF9vYmplY3QiLCJwYXJ0cyIsInNwbGl0IiwiY3VyIiwiZXhlY1NjcmlwdCIsInBhcnQiLCJsZW5ndGgiLCJzaGlmdCIsIkpTQ29tcGlsZXJfYWxpYXNfVk9JRCIsIlVTRV9UWVBFREFSUkFZIiwiVWludDhBcnJheSIsIlVpbnQxNkFycmF5IiwiVWludDMyQXJyYXkiLCJEYXRhVmlldyIsIlpsaWIuQml0U3RyZWFtIiwiYnVmZmVyIiwiYnVmZmVyUG9zaXRpb24iLCJpbmRleCIsImJpdGluZGV4IiwiQXJyYXkiLCJabGliLkJpdFN0cmVhbS5EZWZhdWx0QmxvY2tTaXplIiwiRXJyb3IiLCJleHBhbmRCdWZmZXIiLCJabGliLkJpdFN0cmVhbS5wcm90b3R5cGUuZXhwYW5kQnVmZmVyIiwib2xkYnVmIiwiaSIsImlsIiwic2V0IiwicHJvdG90eXBlIiwid3JpdGVCaXRzIiwiWmxpYi5CaXRTdHJlYW0ucHJvdG90eXBlLndyaXRlQml0cyIsIm51bWJlciIsIm4iLCJyZXZlcnNlIiwiY3VycmVudCIsIlpsaWIuQml0U3RyZWFtLlJldmVyc2VUYWJsZSIsImZpbmlzaCIsIlpsaWIuQml0U3RyZWFtLnByb3RvdHlwZS5maW5pc2giLCJvdXRwdXQiLCJzdWJhcnJheSIsInRhYmxlIiwiciIsInMiLCJabGliLkNSQzMyLnVwZGF0ZSIsImRhdGEiLCJwb3MiLCJjcmMiLCJabGliLkNSQzMyLlRhYmxlIiwiWmxpYi5DUkMzMi5UYWJsZV8iLCJabGliLkhlYXAiLCJnZXRQYXJlbnQiLCJabGliLkhlYXAucHJvdG90eXBlLmdldFBhcmVudCIsInB1c2giLCJabGliLkhlYXAucHJvdG90eXBlLnB1c2giLCJ2YWx1ZSIsInBhcmVudCIsImhlYXAiLCJzd2FwIiwicG9wIiwiWmxpYi5IZWFwLnByb3RvdHlwZS5wb3AiLCJabGliLlJhd0RlZmxhdGUiLCJpbnB1dCIsIm9wdF9wYXJhbXMiLCJjb21wcmVzc2lvblR5cGUiLCJabGliLlJhd0RlZmxhdGUuQ29tcHJlc3Npb25UeXBlLkRZTkFNSUMiLCJsYXp5Iiwib3AiLCJEWU5BTUlDIiwiSlNDb21waWxlcl9hbGlhc19UUlVFIiwiY29tcHJlc3MiLCJabGliLlJhd0RlZmxhdGUucHJvdG90eXBlLmNvbXByZXNzIiwiYmxvY2tBcnJheSIsInBvc2l0aW9uIiwiTk9ORSIsInNsaWNlIiwiYmZpbmFsIiwibGVuIiwibmxlbiIsIm1ha2VOb2NvbXByZXNzQmxvY2siLCJpc0ZpbmFsQmxvY2siLCJGSVhFRCIsInN0cmVhbSIsIm1ha2VGaXhlZEh1ZmZtYW5CbG9jayIsImx6NzciLCJsaXRlcmFsIiwiZGF0YUFycmF5IiwiYXBwbHkiLCJtYWtlRHluYW1pY0h1ZmZtYW5CbG9jayIsImJ0eXBlIiwiaGxpdCIsImhkaXN0IiwiaGNsZW4iLCJoY2xlbk9yZGVyIiwibGl0TGVuTGVuZ3RocyIsImxpdExlbkNvZGVzIiwiZGlzdExlbmd0aHMiLCJkaXN0Q29kZXMiLCJ0cmVlTGVuZ3RocyIsInRyYW5zTGVuZ3RocyIsInRyZWVDb2RlcyIsImNvZGUiLCJiaXRsZW4iLCJnZXRMZW5ndGhzXyIsImZyZXFzTGl0TGVuIiwiZ2V0Q29kZXNGcm9tTGVuZ3Roc18iLCJmcmVxc0Rpc3QiLCJzcmMiLCJqIiwicnVuTGVuZ3RoIiwibCIsInJlc3VsdCIsIm5SZXN1bHQiLCJycHQiLCJmcmVxcyIsImNvZGVzIiwibGl0TGVuIiwiZGlzdCIsIlpsaWIuUmF3RGVmbGF0ZS5Mejc3TWF0Y2giLCJiYWNrd2FyZERpc3RhbmNlIiwiYyIsIlpsaWIuUmF3RGVmbGF0ZS5Mejc3TWF0Y2guTGVuZ3RoQ29kZVRhYmxlIiwiWmxpYi5SYXdEZWZsYXRlLnByb3RvdHlwZS5sejc3Iiwid3JpdGVNYXRjaCIsIm1hdGNoIiwib2Zmc2V0IiwiY29kZUFycmF5IiwibHo3N2J1ZiIsInNraXBMZW5ndGgiLCJwcmV2TWF0Y2giLCJtYXRjaEtleSIsIm1hdGNoTGlzdCIsImxvbmdlc3RNYXRjaCIsInRtcCIsIlpsaWIuUmF3RGVmbGF0ZS5Mejc3TWluTGVuZ3RoIiwiWmxpYi5SYXdEZWZsYXRlLldpbmRvd1NpemUiLCJzZWFyY2hMb25nZXN0TWF0Y2hfIiwiWmxpYi5SYXdEZWZsYXRlLnByb3RvdHlwZS5zZWFyY2hMb25nZXN0TWF0Y2hfIiwiY3VycmVudE1hdGNoIiwibWF0Y2hNYXgiLCJtYXRjaExlbmd0aCIsImRsIiwiWmxpYi5SYXdEZWZsYXRlLkx6NzdNYXhMZW5ndGgiLCJabGliLlJhd0RlZmxhdGUucHJvdG90eXBlLmdldExlbmd0aHNfIiwibGltaXQiLCJuU3ltYm9scyIsIm5vZGVzIiwidmFsdWVzIiwiY29kZUxlbmd0aCIsInJldmVyc2VQYWNrYWdlTWVyZ2VfIiwiWmxpYi5SYXdEZWZsYXRlLnByb3RvdHlwZS5yZXZlcnNlUGFja2FnZU1lcmdlXyIsInN5bWJvbHMiLCJ0YWtlUGFja2FnZSIsIngiLCJ0eXBlIiwiY3VycmVudFBvc2l0aW9uIiwibWluaW11bUNvc3QiLCJmbGFnIiwiZXhjZXNzIiwiaGFsZiIsInQiLCJ3ZWlnaHQiLCJuZXh0IiwiWmxpYi5SYXdEZWZsYXRlLnByb3RvdHlwZS5nZXRDb2Rlc0Zyb21MZW5ndGhzXyIsImxlbmd0aHMiLCJjb3VudCIsInN0YXJ0Q29kZSIsIm0iLCJabGliLlJhd0RlZmxhdGUuTWF4Q29kZUxlbmd0aCIsIlpsaWIuR3ppcCIsImlwIiwiZmxhZ3MiLCJmaWxlbmFtZSIsImNvbW1lbnQiLCJkZWZsYXRlT3B0aW9ucyIsIlpsaWIuR3ppcC5wcm90b3R5cGUuY29tcHJlc3MiLCJmbGciLCJtdGltZSIsImNyYzE2IiwiY3JjMzIiLCJyYXdkZWZsYXRlIiwiWmxpYi5HemlwLkRlZmF1bHRCdWZmZXJTaXplIiwiWmxpYi5HemlwLkZsYWdzTWFzay5GTkFNRSIsIlpsaWIuR3ppcC5GbGFnc01hc2suRkNPTU1FTlQiLCJabGliLkd6aXAuRmxhZ3NNYXNrLkZIQ1JDIiwiRGF0ZSIsIm5vdyIsIlpsaWIuR3ppcC5PcGVyYXRpbmdTeXN0ZW0uVU5LTk9XTiIsImNoYXJDb2RlQXQiLCJieXRlTGVuZ3RoIiwiVU5LTk9XTiIsIkZIQ1JDIiwiRk5BTUUiLCJGQ09NTUVOVCIsInB1YmxpY1BhdGgiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMDYgVGhlIENsb3N1cmUgTGlicmFyeSBBdXRob3JzLiBBbGwgUmlnaHRzIFJlc2VydmVkLlxuLy9cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4vLyB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4vLyBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbi8vXG4vLyAgICAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuLy9cbi8vIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbi8vIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMtSVNcIiBCQVNJUyxcbi8vIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuLy8gU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuLy8gbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG5cbi8qKlxuICogQGZpbGVvdmVydmlldyBCb290c3RyYXAgZm9yIHRoZSBHb29nbGUgSlMgTGlicmFyeSAoQ2xvc3VyZSkuXG4gKlxuICogSW4gdW5jb21waWxlZCBtb2RlIGJhc2UuanMgd2lsbCB3cml0ZSBvdXQgQ2xvc3VyZSdzIGRlcHMgZmlsZSwgdW5sZXNzIHRoZVxuICogZ2xvYmFsIDxjb2RlPkNMT1NVUkVfTk9fREVQUzwvY29kZT4gaXMgc2V0IHRvIHRydWUuICBUaGlzIGFsbG93cyBwcm9qZWN0cyB0b1xuICogaW5jbHVkZSB0aGVpciBvd24gZGVwcyBmaWxlKHMpIGZyb20gZGlmZmVyZW50IGxvY2F0aW9ucy5cbiAqXG4gKi9cblxuXG4vKipcbiAqIEBkZWZpbmUge2Jvb2xlYW59IE92ZXJyaWRkZW4gdG8gdHJ1ZSBieSB0aGUgY29tcGlsZXIgd2hlbiAtLWNsb3N1cmVfcGFzc1xuICogICAgIG9yIC0tbWFya19hc19jb21waWxlZCBpcyBzcGVjaWZpZWQuXG4gKi9cbnZhciBDT01QSUxFRCA9IGZhbHNlO1xuXG5cbi8qKlxuICogQmFzZSBuYW1lc3BhY2UgZm9yIHRoZSBDbG9zdXJlIGxpYnJhcnkuICBDaGVja3MgdG8gc2VlIGdvb2cgaXNcbiAqIGFscmVhZHkgZGVmaW5lZCBpbiB0aGUgY3VycmVudCBzY29wZSBiZWZvcmUgYXNzaWduaW5nIHRvIHByZXZlbnRcbiAqIGNsb2JiZXJpbmcgaWYgYmFzZS5qcyBpcyBsb2FkZWQgbW9yZSB0aGFuIG9uY2UuXG4gKlxuICogQGNvbnN0XG4gKi9cbnZhciBnb29nID0gZ29vZyB8fCB7fTsgLy8gSWRlbnRpZmllcyB0aGlzIGZpbGUgYXMgdGhlIENsb3N1cmUgYmFzZS5cblxuXG4vKipcbiAqIFJlZmVyZW5jZSB0byB0aGUgZ2xvYmFsIGNvbnRleHQuICBJbiBtb3N0IGNhc2VzIHRoaXMgd2lsbCBiZSAnd2luZG93Jy5cbiAqL1xuZ29vZy5nbG9iYWwgPSB0aGlzO1xuXG5cbi8qKlxuICogQGRlZmluZSB7Ym9vbGVhbn0gREVCVUcgaXMgcHJvdmlkZWQgYXMgYSBjb252ZW5pZW5jZSBzbyB0aGF0IGRlYnVnZ2luZyBjb2RlXG4gKiB0aGF0IHNob3VsZCBub3QgYmUgaW5jbHVkZWQgaW4gYSBwcm9kdWN0aW9uIGpzX2JpbmFyeSBjYW4gYmUgZWFzaWx5IHN0cmlwcGVkXG4gKiBieSBzcGVjaWZ5aW5nIC0tZGVmaW5lIGdvb2cuREVCVUc9ZmFsc2UgdG8gdGhlIEpTQ29tcGlsZXIuIEZvciBleGFtcGxlLCBtb3N0XG4gKiB0b1N0cmluZygpIG1ldGhvZHMgc2hvdWxkIGJlIGRlY2xhcmVkIGluc2lkZSBhbiBcImlmIChnb29nLkRFQlVHKVwiIGNvbmRpdGlvbmFsXG4gKiBiZWNhdXNlIHRoZXkgYXJlIGdlbmVyYWxseSB1c2VkIGZvciBkZWJ1Z2dpbmcgcHVycG9zZXMgYW5kIGl0IGlzIGRpZmZpY3VsdFxuICogZm9yIHRoZSBKU0NvbXBpbGVyIHRvIHN0YXRpY2FsbHkgZGV0ZXJtaW5lIHdoZXRoZXIgdGhleSBhcmUgdXNlZC5cbiAqL1xuZ29vZy5ERUJVRyA9IHRydWU7XG5cblxuLyoqXG4gKiBAZGVmaW5lIHtzdHJpbmd9IExPQ0FMRSBkZWZpbmVzIHRoZSBsb2NhbGUgYmVpbmcgdXNlZCBmb3IgY29tcGlsYXRpb24uIEl0IGlzXG4gKiB1c2VkIHRvIHNlbGVjdCBsb2NhbGUgc3BlY2lmaWMgZGF0YSB0byBiZSBjb21waWxlZCBpbiBqcyBiaW5hcnkuIEJVSUxEIHJ1bGVcbiAqIGNhbiBzcGVjaWZ5IHRoaXMgdmFsdWUgYnkgXCItLWRlZmluZSBnb29nLkxPQ0FMRT08bG9jYWxlX25hbWU+XCIgYXMgSlNDb21waWxlclxuICogb3B0aW9uLlxuICpcbiAqIFRha2UgaW50byBhY2NvdW50IHRoYXQgdGhlIGxvY2FsZSBjb2RlIGZvcm1hdCBpcyBpbXBvcnRhbnQuIFlvdSBzaG91bGQgdXNlXG4gKiB0aGUgY2Fub25pY2FsIFVuaWNvZGUgZm9ybWF0IHdpdGggaHlwaGVuIGFzIGEgZGVsaW1pdGVyLiBMYW5ndWFnZSBtdXN0IGJlXG4gKiBsb3dlcmNhc2UsIExhbmd1YWdlIFNjcmlwdCAtIENhcGl0YWxpemVkLCBSZWdpb24gLSBVUFBFUkNBU0UuXG4gKiBUaGVyZSBhcmUgZmV3IGV4YW1wbGVzOiBwdC1CUiwgZW4sIGVuLVVTLCBzci1MYXRpbi1CTywgemgtSGFucy1DTi5cbiAqXG4gKiBTZWUgbW9yZSBpbmZvIGFib3V0IGxvY2FsZSBjb2RlcyBoZXJlOlxuICogaHR0cDovL3d3dy51bmljb2RlLm9yZy9yZXBvcnRzL3RyMzUvI1VuaWNvZGVfTGFuZ3VhZ2VfYW5kX0xvY2FsZV9JZGVudGlmaWVyc1xuICpcbiAqIEZvciBsYW5ndWFnZSBjb2RlcyB5b3Ugc2hvdWxkIHVzZSB2YWx1ZXMgZGVmaW5lZCBieSBJU08gNjkzLTEuIFNlZSBpdCBoZXJlXG4gKiBodHRwOi8vd3d3LnczLm9yZy9XQUkvRVIvSUcvZXJ0L2lzbzYzOS5odG0uIFRoZXJlIGlzIG9ubHkgb25lIGV4Y2VwdGlvbiBmcm9tXG4gKiB0aGlzIHJ1bGU6IHRoZSBIZWJyZXcgbGFuZ3VhZ2UuIEZvciBsZWdhY3kgcmVhc29ucyB0aGUgb2xkIGNvZGUgKGl3KSBzaG91bGRcbiAqIGJlIHVzZWQgaW5zdGVhZCBvZiB0aGUgbmV3IGNvZGUgKGhlKSwgc2VlIGh0dHA6Ly93aWtpL01haW4vSUlJU3lub255bXMuXG4gKi9cbmdvb2cuTE9DQUxFID0gJ2VuJzsgIC8vIGRlZmF1bHQgdG8gZW5cblxuXG4vKipcbiAqIENyZWF0ZXMgb2JqZWN0IHN0dWJzIGZvciBhIG5hbWVzcGFjZS4gIFRoZSBwcmVzZW5jZSBvZiBvbmUgb3IgbW9yZVxuICogZ29vZy5wcm92aWRlKCkgY2FsbHMgaW5kaWNhdGUgdGhhdCB0aGUgZmlsZSBkZWZpbmVzIHRoZSBnaXZlblxuICogb2JqZWN0cy9uYW1lc3BhY2VzLiAgQnVpbGQgdG9vbHMgYWxzbyBzY2FuIGZvciBwcm92aWRlL3JlcXVpcmUgc3RhdGVtZW50c1xuICogdG8gZGlzY2VybiBkZXBlbmRlbmNpZXMsIGJ1aWxkIGRlcGVuZGVuY3kgZmlsZXMgKHNlZSBkZXBzLmpzKSwgZXRjLlxuICogQHNlZSBnb29nLnJlcXVpcmVcbiAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lIE5hbWVzcGFjZSBwcm92aWRlZCBieSB0aGlzIGZpbGUgaW4gdGhlIGZvcm1cbiAqICAgICBcImdvb2cucGFja2FnZS5wYXJ0XCIuXG4gKi9cbmdvb2cucHJvdmlkZSA9IGZ1bmN0aW9uKG5hbWUpIHtcbiAgaWYgKCFDT01QSUxFRCkge1xuICAgIC8vIEVuc3VyZSB0aGF0IHRoZSBzYW1lIG5hbWVzcGFjZSBpc24ndCBwcm92aWRlZCB0d2ljZS4gVGhpcyBpcyBpbnRlbmRlZFxuICAgIC8vIHRvIHRlYWNoIG5ldyBkZXZlbG9wZXJzIHRoYXQgJ2dvb2cucHJvdmlkZScgaXMgZWZmZWN0aXZlbHkgYSB2YXJpYWJsZVxuICAgIC8vIGRlY2xhcmF0aW9uLiBBbmQgd2hlbiBKU0NvbXBpbGVyIHRyYW5zZm9ybXMgZ29vZy5wcm92aWRlIGludG8gYSByZWFsXG4gICAgLy8gdmFyaWFibGUgZGVjbGFyYXRpb24sIHRoZSBjb21waWxlZCBKUyBzaG91bGQgd29yayB0aGUgc2FtZSBhcyB0aGUgcmF3XG4gICAgLy8gSlMtLWV2ZW4gd2hlbiB0aGUgcmF3IEpTIHVzZXMgZ29vZy5wcm92aWRlIGluY29ycmVjdGx5LlxuICAgIGlmIChnb29nLmlzUHJvdmlkZWRfKG5hbWUpKSB7XG4gICAgICB0aHJvdyBFcnJvcignTmFtZXNwYWNlIFwiJyArIG5hbWUgKyAnXCIgYWxyZWFkeSBkZWNsYXJlZC4nKTtcbiAgICB9XG4gICAgZGVsZXRlIGdvb2cuaW1wbGljaXROYW1lc3BhY2VzX1tuYW1lXTtcblxuICAgIHZhciBuYW1lc3BhY2UgPSBuYW1lO1xuICAgIHdoaWxlICgobmFtZXNwYWNlID0gbmFtZXNwYWNlLnN1YnN0cmluZygwLCBuYW1lc3BhY2UubGFzdEluZGV4T2YoJy4nKSkpKSB7XG4gICAgICBpZiAoZ29vZy5nZXRPYmplY3RCeU5hbWUobmFtZXNwYWNlKSkge1xuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIGdvb2cuaW1wbGljaXROYW1lc3BhY2VzX1tuYW1lc3BhY2VdID0gdHJ1ZTtcbiAgICB9XG4gIH1cblxuICBnb29nLmV4cG9ydFBhdGhfKG5hbWUpO1xufTtcblxuXG4vKipcbiAqIE1hcmtzIHRoYXQgdGhlIGN1cnJlbnQgZmlsZSBzaG91bGQgb25seSBiZSB1c2VkIGZvciB0ZXN0aW5nLCBhbmQgbmV2ZXIgZm9yXG4gKiBsaXZlIGNvZGUgaW4gcHJvZHVjdGlvbi5cbiAqIEBwYXJhbSB7c3RyaW5nPX0gb3B0X21lc3NhZ2UgT3B0aW9uYWwgbWVzc2FnZSB0byBhZGQgdG8gdGhlIGVycm9yIHRoYXQnc1xuICogICAgIHJhaXNlZCB3aGVuIHVzZWQgaW4gcHJvZHVjdGlvbiBjb2RlLlxuICovXG5nb29nLnNldFRlc3RPbmx5ID0gZnVuY3Rpb24ob3B0X21lc3NhZ2UpIHtcbiAgaWYgKENPTVBJTEVEICYmICFnb29nLkRFQlVHKSB7XG4gICAgb3B0X21lc3NhZ2UgPSBvcHRfbWVzc2FnZSB8fCAnJztcbiAgICB0aHJvdyBFcnJvcignSW1wb3J0aW5nIHRlc3Qtb25seSBjb2RlIGludG8gbm9uLWRlYnVnIGVudmlyb25tZW50JyArXG4gICAgICAgICAgICAgICAgb3B0X21lc3NhZ2UgPyAnOiAnICsgb3B0X21lc3NhZ2UgOiAnLicpO1xuICB9XG59O1xuXG5cbmlmICghQ09NUElMRUQpIHtcblxuICAvKipcbiAgICogQ2hlY2sgaWYgdGhlIGdpdmVuIG5hbWUgaGFzIGJlZW4gZ29vZy5wcm92aWRlZC4gVGhpcyB3aWxsIHJldHVybiBmYWxzZSBmb3JcbiAgICogbmFtZXMgdGhhdCBhcmUgYXZhaWxhYmxlIG9ubHkgYXMgaW1wbGljaXQgbmFtZXNwYWNlcy5cbiAgICogQHBhcmFtIHtzdHJpbmd9IG5hbWUgbmFtZSBvZiB0aGUgb2JqZWN0IHRvIGxvb2sgZm9yLlxuICAgKiBAcmV0dXJuIHtib29sZWFufSBXaGV0aGVyIHRoZSBuYW1lIGhhcyBiZWVuIHByb3ZpZGVkLlxuICAgKiBAcHJpdmF0ZVxuICAgKi9cbiAgZ29vZy5pc1Byb3ZpZGVkXyA9IGZ1bmN0aW9uKG5hbWUpIHtcbiAgICByZXR1cm4gIWdvb2cuaW1wbGljaXROYW1lc3BhY2VzX1tuYW1lXSAmJiAhIWdvb2cuZ2V0T2JqZWN0QnlOYW1lKG5hbWUpO1xuICB9O1xuXG4gIC8qKlxuICAgKiBOYW1lc3BhY2VzIGltcGxpY2l0bHkgZGVmaW5lZCBieSBnb29nLnByb3ZpZGUuIEZvciBleGFtcGxlLFxuICAgKiBnb29nLnByb3ZpZGUoJ2dvb2cuZXZlbnRzLkV2ZW50JykgaW1wbGljaXRseSBkZWNsYXJlc1xuICAgKiB0aGF0ICdnb29nJyBhbmQgJ2dvb2cuZXZlbnRzJyBtdXN0IGJlIG5hbWVzcGFjZXMuXG4gICAqXG4gICAqIEB0eXBlIHtPYmplY3R9XG4gICAqIEBwcml2YXRlXG4gICAqL1xuICBnb29nLmltcGxpY2l0TmFtZXNwYWNlc18gPSB7fTtcbn1cblxuXG4vKipcbiAqIEJ1aWxkcyBhbiBvYmplY3Qgc3RydWN0dXJlIGZvciB0aGUgcHJvdmlkZWQgbmFtZXNwYWNlIHBhdGgsXG4gKiBlbnN1cmluZyB0aGF0IG5hbWVzIHRoYXQgYWxyZWFkeSBleGlzdCBhcmUgbm90IG92ZXJ3cml0dGVuLiBGb3JcbiAqIGV4YW1wbGU6XG4gKiBcImEuYi5jXCIgLT4gYSA9IHt9O2EuYj17fTthLmIuYz17fTtcbiAqIFVzZWQgYnkgZ29vZy5wcm92aWRlIGFuZCBnb29nLmV4cG9ydFN5bWJvbC5cbiAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lIG5hbWUgb2YgdGhlIG9iamVjdCB0aGF0IHRoaXMgZmlsZSBkZWZpbmVzLlxuICogQHBhcmFtIHsqPX0gb3B0X29iamVjdCB0aGUgb2JqZWN0IHRvIGV4cG9zZSBhdCB0aGUgZW5kIG9mIHRoZSBwYXRoLlxuICogQHBhcmFtIHtPYmplY3Q9fSBvcHRfb2JqZWN0VG9FeHBvcnRUbyBUaGUgb2JqZWN0IHRvIGFkZCB0aGUgcGF0aCB0bzsgZGVmYXVsdFxuICogICAgIGlzIHxnb29nLmdsb2JhbHwuXG4gKiBAcHJpdmF0ZVxuICovXG5nb29nLmV4cG9ydFBhdGhfID0gZnVuY3Rpb24obmFtZSwgb3B0X29iamVjdCwgb3B0X29iamVjdFRvRXhwb3J0VG8pIHtcbiAgdmFyIHBhcnRzID0gbmFtZS5zcGxpdCgnLicpO1xuICB2YXIgY3VyID0gb3B0X29iamVjdFRvRXhwb3J0VG8gfHwgZ29vZy5nbG9iYWw7XG5cbiAgLy8gSW50ZXJuZXQgRXhwbG9yZXIgZXhoaWJpdHMgc3RyYW5nZSBiZWhhdmlvciB3aGVuIHRocm93aW5nIGVycm9ycyBmcm9tXG4gIC8vIG1ldGhvZHMgZXh0ZXJuZWQgaW4gdGhpcyBtYW5uZXIuICBTZWUgdGhlIHRlc3RFeHBvcnRTeW1ib2xFeGNlcHRpb25zIGluXG4gIC8vIGJhc2VfdGVzdC5odG1sIGZvciBhbiBleGFtcGxlLlxuICBpZiAoIShwYXJ0c1swXSBpbiBjdXIpICYmIGN1ci5leGVjU2NyaXB0KSB7XG4gICAgY3VyLmV4ZWNTY3JpcHQoJ3ZhciAnICsgcGFydHNbMF0pO1xuICB9XG5cbiAgLy8gQ2VydGFpbiBicm93c2VycyBjYW5ub3QgcGFyc2UgY29kZSBpbiB0aGUgZm9ybSBmb3IoKGEgaW4gYik7IGM7KTtcbiAgLy8gVGhpcyBwYXR0ZXJuIGlzIHByb2R1Y2VkIGJ5IHRoZSBKU0NvbXBpbGVyIHdoZW4gaXQgY29sbGFwc2VzIHRoZVxuICAvLyBzdGF0ZW1lbnQgYWJvdmUgaW50byB0aGUgY29uZGl0aW9uYWwgbG9vcCBiZWxvdy4gVG8gcHJldmVudCB0aGlzIGZyb21cbiAgLy8gaGFwcGVuaW5nLCB1c2UgYSBmb3ItbG9vcCBhbmQgcmVzZXJ2ZSB0aGUgaW5pdCBsb2dpYyBhcyBiZWxvdy5cblxuICAvLyBQYXJlbnRoZXNlcyBhZGRlZCB0byBlbGltaW5hdGUgc3RyaWN0IEpTIHdhcm5pbmcgaW4gRmlyZWZveC5cbiAgZm9yICh2YXIgcGFydDsgcGFydHMubGVuZ3RoICYmIChwYXJ0ID0gcGFydHMuc2hpZnQoKSk7KSB7XG4gICAgaWYgKCFwYXJ0cy5sZW5ndGggJiYgZ29vZy5pc0RlZihvcHRfb2JqZWN0KSkge1xuICAgICAgLy8gbGFzdCBwYXJ0IGFuZCB3ZSBoYXZlIGFuIG9iamVjdDsgdXNlIGl0XG4gICAgICBjdXJbcGFydF0gPSBvcHRfb2JqZWN0O1xuICAgIH0gZWxzZSBpZiAoY3VyW3BhcnRdKSB7XG4gICAgICBjdXIgPSBjdXJbcGFydF07XG4gICAgfSBlbHNlIHtcbiAgICAgIGN1ciA9IGN1cltwYXJ0XSA9IHt9O1xuICAgIH1cbiAgfVxufTtcblxuXG4vKipcbiAqIFJldHVybnMgYW4gb2JqZWN0IGJhc2VkIG9uIGl0cyBmdWxseSBxdWFsaWZpZWQgZXh0ZXJuYWwgbmFtZS4gIElmIHlvdSBhcmVcbiAqIHVzaW5nIGEgY29tcGlsYXRpb24gcGFzcyB0aGF0IHJlbmFtZXMgcHJvcGVydHkgbmFtZXMgYmV3YXJlIHRoYXQgdXNpbmcgdGhpc1xuICogZnVuY3Rpb24gd2lsbCBub3QgZmluZCByZW5hbWVkIHByb3BlcnRpZXMuXG4gKlxuICogQHBhcmFtIHtzdHJpbmd9IG5hbWUgVGhlIGZ1bGx5IHF1YWxpZmllZCBuYW1lLlxuICogQHBhcmFtIHtPYmplY3Q9fSBvcHRfb2JqIFRoZSBvYmplY3Qgd2l0aGluIHdoaWNoIHRvIGxvb2s7IGRlZmF1bHQgaXNcbiAqICAgICB8Z29vZy5nbG9iYWx8LlxuICogQHJldHVybiB7P30gVGhlIHZhbHVlIChvYmplY3Qgb3IgcHJpbWl0aXZlKSBvciwgaWYgbm90IGZvdW5kLCBudWxsLlxuICovXG5nb29nLmdldE9iamVjdEJ5TmFtZSA9IGZ1bmN0aW9uKG5hbWUsIG9wdF9vYmopIHtcbiAgdmFyIHBhcnRzID0gbmFtZS5zcGxpdCgnLicpO1xuICB2YXIgY3VyID0gb3B0X29iaiB8fCBnb29nLmdsb2JhbDtcbiAgZm9yICh2YXIgcGFydDsgcGFydCA9IHBhcnRzLnNoaWZ0KCk7ICkge1xuICAgIGlmIChnb29nLmlzRGVmQW5kTm90TnVsbChjdXJbcGFydF0pKSB7XG4gICAgICBjdXIgPSBjdXJbcGFydF07XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgfVxuICByZXR1cm4gY3VyO1xufTtcblxuXG4vKipcbiAqIEdsb2JhbGl6ZXMgYSB3aG9sZSBuYW1lc3BhY2UsIHN1Y2ggYXMgZ29vZyBvciBnb29nLmxhbmcuXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IG9iaiBUaGUgbmFtZXNwYWNlIHRvIGdsb2JhbGl6ZS5cbiAqIEBwYXJhbSB7T2JqZWN0PX0gb3B0X2dsb2JhbCBUaGUgb2JqZWN0IHRvIGFkZCB0aGUgcHJvcGVydGllcyB0by5cbiAqIEBkZXByZWNhdGVkIFByb3BlcnRpZXMgbWF5IGJlIGV4cGxpY2l0bHkgZXhwb3J0ZWQgdG8gdGhlIGdsb2JhbCBzY29wZSwgYnV0XG4gKiAgICAgdGhpcyBzaG91bGQgbm8gbG9uZ2VyIGJlIGRvbmUgaW4gYnVsay5cbiAqL1xuZ29vZy5nbG9iYWxpemUgPSBmdW5jdGlvbihvYmosIG9wdF9nbG9iYWwpIHtcbiAgdmFyIGdsb2JhbCA9IG9wdF9nbG9iYWwgfHwgZ29vZy5nbG9iYWw7XG4gIGZvciAodmFyIHggaW4gb2JqKSB7XG4gICAgZ2xvYmFsW3hdID0gb2JqW3hdO1xuICB9XG59O1xuXG5cbi8qKlxuICogQWRkcyBhIGRlcGVuZGVuY3kgZnJvbSBhIGZpbGUgdG8gdGhlIGZpbGVzIGl0IHJlcXVpcmVzLlxuICogQHBhcmFtIHtzdHJpbmd9IHJlbFBhdGggVGhlIHBhdGggdG8gdGhlIGpzIGZpbGUuXG4gKiBAcGFyYW0ge0FycmF5fSBwcm92aWRlcyBBbiBhcnJheSBvZiBzdHJpbmdzIHdpdGggdGhlIG5hbWVzIG9mIHRoZSBvYmplY3RzXG4gKiAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzIGZpbGUgcHJvdmlkZXMuXG4gKiBAcGFyYW0ge0FycmF5fSByZXF1aXJlcyBBbiBhcnJheSBvZiBzdHJpbmdzIHdpdGggdGhlIG5hbWVzIG9mIHRoZSBvYmplY3RzXG4gKiAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzIGZpbGUgcmVxdWlyZXMuXG4gKi9cbmdvb2cuYWRkRGVwZW5kZW5jeSA9IGZ1bmN0aW9uKHJlbFBhdGgsIHByb3ZpZGVzLCByZXF1aXJlcykge1xuICBpZiAoIUNPTVBJTEVEKSB7XG4gICAgdmFyIHByb3ZpZGUsIHJlcXVpcmU7XG4gICAgdmFyIHBhdGggPSByZWxQYXRoLnJlcGxhY2UoL1xcXFwvZywgJy8nKTtcbiAgICB2YXIgZGVwcyA9IGdvb2cuZGVwZW5kZW5jaWVzXztcbiAgICBmb3IgKHZhciBpID0gMDsgcHJvdmlkZSA9IHByb3ZpZGVzW2ldOyBpKyspIHtcbiAgICAgIGRlcHMubmFtZVRvUGF0aFtwcm92aWRlXSA9IHBhdGg7XG4gICAgICBpZiAoIShwYXRoIGluIGRlcHMucGF0aFRvTmFtZXMpKSB7XG4gICAgICAgIGRlcHMucGF0aFRvTmFtZXNbcGF0aF0gPSB7fTtcbiAgICAgIH1cbiAgICAgIGRlcHMucGF0aFRvTmFtZXNbcGF0aF1bcHJvdmlkZV0gPSB0cnVlO1xuICAgIH1cbiAgICBmb3IgKHZhciBqID0gMDsgcmVxdWlyZSA9IHJlcXVpcmVzW2pdOyBqKyspIHtcbiAgICAgIGlmICghKHBhdGggaW4gZGVwcy5yZXF1aXJlcykpIHtcbiAgICAgICAgZGVwcy5yZXF1aXJlc1twYXRoXSA9IHt9O1xuICAgICAgfVxuICAgICAgZGVwcy5yZXF1aXJlc1twYXRoXVtyZXF1aXJlXSA9IHRydWU7XG4gICAgfVxuICB9XG59O1xuXG5cblxuXG4vLyBOT1RFKG5uYXplKTogVGhlIGRlYnVnIERPTSBsb2FkZXIgd2FzIGluY2x1ZGVkIGluIGJhc2UuanMgYXMgYW4gb3JpZ25hbFxuLy8gd2F5IHRvIGRvIFwiZGVidWctbW9kZVwiIGRldmVsb3BtZW50LiAgVGhlIGRlcGVuZGVuY3kgc3lzdGVtIGNhbiBzb21ldGltZXNcbi8vIGJlIGNvbmZ1c2luZywgYXMgY2FuIHRoZSBkZWJ1ZyBET00gbG9hZGVyJ3MgYXN5bmNyb25vdXMgbmF0dXJlLlxuLy9cbi8vIFdpdGggdGhlIERPTSBsb2FkZXIsIGEgY2FsbCB0byBnb29nLnJlcXVpcmUoKSBpcyBub3QgYmxvY2tpbmcgLS0gdGhlXG4vLyBzY3JpcHQgd2lsbCBub3QgbG9hZCB1bnRpbCBzb21lIHBvaW50IGFmdGVyIHRoZSBjdXJyZW50IHNjcmlwdC4gIElmIGFcbi8vIG5hbWVzcGFjZSBpcyBuZWVkZWQgYXQgcnVudGltZSwgaXQgbmVlZHMgdG8gYmUgZGVmaW5lZCBpbiBhIHByZXZpb3VzXG4vLyBzY3JpcHQsIG9yIGxvYWRlZCB2aWEgcmVxdWlyZSgpIHdpdGggaXRzIHJlZ2lzdGVyZWQgZGVwZW5kZW5jaWVzLlxuLy8gVXNlci1kZWZpbmVkIG5hbWVzcGFjZXMgbWF5IG5lZWQgdGhlaXIgb3duIGRlcHMgZmlsZS4gIFNlZSBodHRwOi8vZ28vanNfZGVwcyxcbi8vIGh0dHA6Ly9nby9nZW5qc2RlcHMsIG9yLCBleHRlcm5hbGx5LCBEZXBzV3JpdGVyLlxuLy8gaHR0cDovL2NvZGUuZ29vZ2xlLmNvbS9jbG9zdXJlL2xpYnJhcnkvZG9jcy9kZXBzd3JpdGVyLmh0bWxcbi8vXG4vLyBCZWNhdXNlIG9mIGxlZ2FjeSBjbGllbnRzLCB0aGUgRE9NIGxvYWRlciBjYW4ndCBiZSBlYXNpbHkgcmVtb3ZlZCBmcm9tXG4vLyBiYXNlLmpzLiAgV29yayBpcyBiZWluZyBkb25lIHRvIG1ha2UgaXQgZGlzYWJsZWFibGUgb3IgcmVwbGFjZWFibGUgZm9yXG4vLyBkaWZmZXJlbnQgZW52aXJvbm1lbnRzIChET00tbGVzcyBKYXZhU2NyaXB0IGludGVycHJldGVycyBsaWtlIFJoaW5vIG9yIFY4LFxuLy8gZm9yIGV4YW1wbGUpLiBTZWUgYm9vdHN0cmFwLyBmb3IgbW9yZSBpbmZvcm1hdGlvbi5cblxuXG4vKipcbiAqIEBkZWZpbmUge2Jvb2xlYW59IFdoZXRoZXIgdG8gZW5hYmxlIHRoZSBkZWJ1ZyBsb2FkZXIuXG4gKlxuICogSWYgZW5hYmxlZCwgYSBjYWxsIHRvIGdvb2cucmVxdWlyZSgpIHdpbGwgYXR0ZW1wdCB0byBsb2FkIHRoZSBuYW1lc3BhY2UgYnlcbiAqIGFwcGVuZGluZyBhIHNjcmlwdCB0YWcgdG8gdGhlIERPTSAoaWYgdGhlIG5hbWVzcGFjZSBoYXMgYmVlbiByZWdpc3RlcmVkKS5cbiAqXG4gKiBJZiBkaXNhYmxlZCwgZ29vZy5yZXF1aXJlKCkgd2lsbCBzaW1wbHkgYXNzZXJ0IHRoYXQgdGhlIG5hbWVzcGFjZSBoYXMgYmVlblxuICogcHJvdmlkZWQgKGFuZCBkZXBlbmQgb24gdGhlIGZhY3QgdGhhdCBzb21lIG91dHNpZGUgdG9vbCBjb3JyZWN0bHkgb3JkZXJlZFxuICogdGhlIHNjcmlwdCkuXG4gKi9cbmdvb2cuRU5BQkxFX0RFQlVHX0xPQURFUiA9IHRydWU7XG5cblxuLyoqXG4gKiBJbXBsZW1lbnRzIGEgc3lzdGVtIGZvciB0aGUgZHluYW1pYyByZXNvbHV0aW9uIG9mIGRlcGVuZGVuY2llc1xuICogdGhhdCB3b3JrcyBpbiBwYXJhbGxlbCB3aXRoIHRoZSBCVUlMRCBzeXN0ZW0uIE5vdGUgdGhhdCBhbGwgY2FsbHNcbiAqIHRvIGdvb2cucmVxdWlyZSB3aWxsIGJlIHN0cmlwcGVkIGJ5IHRoZSBKU0NvbXBpbGVyIHdoZW4gdGhlXG4gKiAtLWNsb3N1cmVfcGFzcyBvcHRpb24gaXMgdXNlZC5cbiAqIEBzZWUgZ29vZy5wcm92aWRlXG4gKiBAcGFyYW0ge3N0cmluZ30gbmFtZSBOYW1lc3BhY2UgdG8gaW5jbHVkZSAoYXMgd2FzIGdpdmVuIGluIGdvb2cucHJvdmlkZSgpKVxuICogICAgIGluIHRoZSBmb3JtIFwiZ29vZy5wYWNrYWdlLnBhcnRcIi5cbiAqL1xuZ29vZy5yZXF1aXJlID0gZnVuY3Rpb24obmFtZSkge1xuXG4gIC8vIGlmIHRoZSBvYmplY3QgYWxyZWFkeSBleGlzdHMgd2UgZG8gbm90IG5lZWQgZG8gZG8gYW55dGhpbmdcbiAgLy8gVE9ETyhhcnYpOiBJZiB3ZSBzdGFydCB0byBzdXBwb3J0IHJlcXVpcmUgYmFzZWQgb24gZmlsZSBuYW1lIHRoaXMgaGFzXG4gIC8vICAgICAgICAgICAgdG8gY2hhbmdlXG4gIC8vIFRPRE8oYXJ2KTogSWYgd2UgYWxsb3cgZ29vZy5mb28uKiB0aGlzIGhhcyB0byBjaGFuZ2VcbiAgLy8gVE9ETyhhcnYpOiBJZiB3ZSBpbXBsZW1lbnQgZHluYW1pYyBsb2FkIGFmdGVyIHBhZ2UgbG9hZCB3ZSBzaG91bGQgcHJvYmFibHlcbiAgLy8gICAgICAgICAgICBub3QgcmVtb3ZlIHRoaXMgY29kZSBmb3IgdGhlIGNvbXBpbGVkIG91dHB1dFxuICBpZiAoIUNPTVBJTEVEKSB7XG4gICAgaWYgKGdvb2cuaXNQcm92aWRlZF8obmFtZSkpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAoZ29vZy5FTkFCTEVfREVCVUdfTE9BREVSKSB7XG4gICAgICB2YXIgcGF0aCA9IGdvb2cuZ2V0UGF0aEZyb21EZXBzXyhuYW1lKTtcbiAgICAgIGlmIChwYXRoKSB7XG4gICAgICAgIGdvb2cuaW5jbHVkZWRfW3BhdGhdID0gdHJ1ZTtcbiAgICAgICAgZ29vZy53cml0ZVNjcmlwdHNfKCk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICB9XG5cbiAgICB2YXIgZXJyb3JNZXNzYWdlID0gJ2dvb2cucmVxdWlyZSBjb3VsZCBub3QgZmluZDogJyArIG5hbWU7XG4gICAgaWYgKGdvb2cuZ2xvYmFsLmNvbnNvbGUpIHtcbiAgICAgIGdvb2cuZ2xvYmFsLmNvbnNvbGVbJ2Vycm9yJ10oZXJyb3JNZXNzYWdlKTtcbiAgICB9XG5cblxuICAgICAgdGhyb3cgRXJyb3IoZXJyb3JNZXNzYWdlKTtcblxuICB9XG59O1xuXG5cbi8qKlxuICogUGF0aCBmb3IgaW5jbHVkZWQgc2NyaXB0c1xuICogQHR5cGUge3N0cmluZ31cbiAqL1xuZ29vZy5iYXNlUGF0aCA9ICcnO1xuXG5cbi8qKlxuICogQSBob29rIGZvciBvdmVycmlkaW5nIHRoZSBiYXNlIHBhdGguXG4gKiBAdHlwZSB7c3RyaW5nfHVuZGVmaW5lZH1cbiAqL1xuZ29vZy5nbG9iYWwuQ0xPU1VSRV9CQVNFX1BBVEg7XG5cblxuLyoqXG4gKiBXaGV0aGVyIHRvIHdyaXRlIG91dCBDbG9zdXJlJ3MgZGVwcyBmaWxlLiBCeSBkZWZhdWx0LFxuICogdGhlIGRlcHMgYXJlIHdyaXR0ZW4uXG4gKiBAdHlwZSB7Ym9vbGVhbnx1bmRlZmluZWR9XG4gKi9cbmdvb2cuZ2xvYmFsLkNMT1NVUkVfTk9fREVQUztcblxuXG4vKipcbiAqIEEgZnVuY3Rpb24gdG8gaW1wb3J0IGEgc2luZ2xlIHNjcmlwdC4gVGhpcyBpcyBtZWFudCB0byBiZSBvdmVycmlkZGVuIHdoZW5cbiAqIENsb3N1cmUgaXMgYmVpbmcgcnVuIGluIG5vbi1IVE1MIGNvbnRleHRzLCBzdWNoIGFzIHdlYiB3b3JrZXJzLiBJdCdzIGRlZmluZWRcbiAqIGluIHRoZSBnbG9iYWwgc2NvcGUgc28gdGhhdCBpdCBjYW4gYmUgc2V0IGJlZm9yZSBiYXNlLmpzIGlzIGxvYWRlZCwgd2hpY2hcbiAqIGFsbG93cyBkZXBzLmpzIHRvIGJlIGltcG9ydGVkIHByb3Blcmx5LlxuICpcbiAqIFRoZSBmdW5jdGlvbiBpcyBwYXNzZWQgdGhlIHNjcmlwdCBzb3VyY2UsIHdoaWNoIGlzIGEgcmVsYXRpdmUgVVJJLiBJdCBzaG91bGRcbiAqIHJldHVybiB0cnVlIGlmIHRoZSBzY3JpcHQgd2FzIGltcG9ydGVkLCBmYWxzZSBvdGhlcndpc2UuXG4gKi9cbmdvb2cuZ2xvYmFsLkNMT1NVUkVfSU1QT1JUX1NDUklQVDtcblxuXG4vKipcbiAqIE51bGwgZnVuY3Rpb24gdXNlZCBmb3IgZGVmYXVsdCB2YWx1ZXMgb2YgY2FsbGJhY2tzLCBldGMuXG4gKiBAcmV0dXJuIHt2b2lkfSBOb3RoaW5nLlxuICovXG5nb29nLm51bGxGdW5jdGlvbiA9IGZ1bmN0aW9uKCkge307XG5cblxuLyoqXG4gKiBUaGUgaWRlbnRpdHkgZnVuY3Rpb24uIFJldHVybnMgaXRzIGZpcnN0IGFyZ3VtZW50LlxuICpcbiAqIEBwYXJhbSB7Kj19IG9wdF9yZXR1cm5WYWx1ZSBUaGUgc2luZ2xlIHZhbHVlIHRoYXQgd2lsbCBiZSByZXR1cm5lZC5cbiAqIEBwYXJhbSB7Li4uKn0gdmFyX2FyZ3MgT3B0aW9uYWwgdHJhaWxpbmcgYXJndW1lbnRzLiBUaGVzZSBhcmUgaWdub3JlZC5cbiAqIEByZXR1cm4gez99IFRoZSBmaXJzdCBhcmd1bWVudC4gV2UgY2FuJ3Qga25vdyB0aGUgdHlwZSAtLSBqdXN0IHBhc3MgaXQgYWxvbmdcbiAqICAgICAgd2l0aG91dCB0eXBlLlxuICogQGRlcHJlY2F0ZWQgVXNlIGdvb2cuZnVuY3Rpb25zLmlkZW50aXR5IGluc3RlYWQuXG4gKi9cbmdvb2cuaWRlbnRpdHlGdW5jdGlvbiA9IGZ1bmN0aW9uKG9wdF9yZXR1cm5WYWx1ZSwgdmFyX2FyZ3MpIHtcbiAgcmV0dXJuIG9wdF9yZXR1cm5WYWx1ZTtcbn07XG5cblxuLyoqXG4gKiBXaGVuIGRlZmluaW5nIGEgY2xhc3MgRm9vIHdpdGggYW4gYWJzdHJhY3QgbWV0aG9kIGJhcigpLCB5b3UgY2FuIGRvOlxuICpcbiAqIEZvby5wcm90b3R5cGUuYmFyID0gZ29vZy5hYnN0cmFjdE1ldGhvZFxuICpcbiAqIE5vdyBpZiBhIHN1YmNsYXNzIG9mIEZvbyBmYWlscyB0byBvdmVycmlkZSBiYXIoKSwgYW4gZXJyb3JcbiAqIHdpbGwgYmUgdGhyb3duIHdoZW4gYmFyKCkgaXMgaW52b2tlZC5cbiAqXG4gKiBOb3RlOiBUaGlzIGRvZXMgbm90IHRha2UgdGhlIG5hbWUgb2YgdGhlIGZ1bmN0aW9uIHRvIG92ZXJyaWRlIGFzXG4gKiBhbiBhcmd1bWVudCBiZWNhdXNlIHRoYXQgd291bGQgbWFrZSBpdCBtb3JlIGRpZmZpY3VsdCB0byBvYmZ1c2NhdGVcbiAqIG91ciBKYXZhU2NyaXB0IGNvZGUuXG4gKlxuICogQHR5cGUgeyFGdW5jdGlvbn1cbiAqIEB0aHJvd3Mge0Vycm9yfSB3aGVuIGludm9rZWQgdG8gaW5kaWNhdGUgdGhlIG1ldGhvZCBzaG91bGQgYmVcbiAqICAgb3ZlcnJpZGRlbi5cbiAqL1xuZ29vZy5hYnN0cmFjdE1ldGhvZCA9IGZ1bmN0aW9uKCkge1xuICB0aHJvdyBFcnJvcigndW5pbXBsZW1lbnRlZCBhYnN0cmFjdCBtZXRob2QnKTtcbn07XG5cblxuLyoqXG4gKiBBZGRzIGEge0Bjb2RlIGdldEluc3RhbmNlfSBzdGF0aWMgbWV0aG9kIHRoYXQgYWx3YXlzIHJldHVybiB0aGUgc2FtZSBpbnN0YW5jZVxuICogb2JqZWN0LlxuICogQHBhcmFtIHshRnVuY3Rpb259IGN0b3IgVGhlIGNvbnN0cnVjdG9yIGZvciB0aGUgY2xhc3MgdG8gYWRkIHRoZSBzdGF0aWNcbiAqICAgICBtZXRob2QgdG8uXG4gKi9cbmdvb2cuYWRkU2luZ2xldG9uR2V0dGVyID0gZnVuY3Rpb24oY3Rvcikge1xuICBjdG9yLmdldEluc3RhbmNlID0gZnVuY3Rpb24oKSB7XG4gICAgaWYgKGN0b3IuaW5zdGFuY2VfKSB7XG4gICAgICByZXR1cm4gY3Rvci5pbnN0YW5jZV87XG4gICAgfVxuICAgIGlmIChnb29nLkRFQlVHKSB7XG4gICAgICAvLyBOT1RFOiBKU0NvbXBpbGVyIGNhbid0IG9wdGltaXplIGF3YXkgQXJyYXkjcHVzaC5cbiAgICAgIGdvb2cuaW5zdGFudGlhdGVkU2luZ2xldG9uc19bZ29vZy5pbnN0YW50aWF0ZWRTaW5nbGV0b25zXy5sZW5ndGhdID0gY3RvcjtcbiAgICB9XG4gICAgcmV0dXJuIGN0b3IuaW5zdGFuY2VfID0gbmV3IGN0b3I7XG4gIH07XG59O1xuXG5cbi8qKlxuICogQWxsIHNpbmdsZXRvbiBjbGFzc2VzIHRoYXQgaGF2ZSBiZWVuIGluc3RhbnRpYXRlZCwgZm9yIHRlc3RpbmcuIERvbid0IHJlYWRcbiAqIGl0IGRpcmVjdGx5LCB1c2UgdGhlIHtAY29kZSBnb29nLnRlc3Rpbmcuc2luZ2xldG9ufSBtb2R1bGUuIFRoZSBjb21waWxlclxuICogcmVtb3ZlcyB0aGlzIHZhcmlhYmxlIGlmIHVudXNlZC5cbiAqIEB0eXBlIHshQXJyYXkuPCFGdW5jdGlvbj59XG4gKiBAcHJpdmF0ZVxuICovXG5nb29nLmluc3RhbnRpYXRlZFNpbmdsZXRvbnNfID0gW107XG5cblxuaWYgKCFDT01QSUxFRCAmJiBnb29nLkVOQUJMRV9ERUJVR19MT0FERVIpIHtcbiAgLyoqXG4gICAqIE9iamVjdCB1c2VkIHRvIGtlZXAgdHJhY2sgb2YgdXJscyB0aGF0IGhhdmUgYWxyZWFkeSBiZWVuIGFkZGVkLiBUaGlzXG4gICAqIHJlY29yZCBhbGxvd3MgdGhlIHByZXZlbnRpb24gb2YgY2lyY3VsYXIgZGVwZW5kZW5jaWVzLlxuICAgKiBAdHlwZSB7T2JqZWN0fVxuICAgKiBAcHJpdmF0ZVxuICAgKi9cbiAgZ29vZy5pbmNsdWRlZF8gPSB7fTtcblxuXG4gIC8qKlxuICAgKiBUaGlzIG9iamVjdCBpcyB1c2VkIHRvIGtlZXAgdHJhY2sgb2YgZGVwZW5kZW5jaWVzIGFuZCBvdGhlciBkYXRhIHRoYXQgaXNcbiAgICogdXNlZCBmb3IgbG9hZGluZyBzY3JpcHRzXG4gICAqIEBwcml2YXRlXG4gICAqIEB0eXBlIHtPYmplY3R9XG4gICAqL1xuICBnb29nLmRlcGVuZGVuY2llc18gPSB7XG4gICAgcGF0aFRvTmFtZXM6IHt9LCAvLyAxIHRvIG1hbnlcbiAgICBuYW1lVG9QYXRoOiB7fSwgLy8gMSB0byAxXG4gICAgcmVxdWlyZXM6IHt9LCAvLyAxIHRvIG1hbnlcbiAgICAvLyB1c2VkIHdoZW4gcmVzb2x2aW5nIGRlcGVuZGVuY2llcyB0byBwcmV2ZW50IHVzIGZyb21cbiAgICAvLyB2aXNpdGluZyB0aGUgZmlsZSB0d2ljZVxuICAgIHZpc2l0ZWQ6IHt9LFxuICAgIHdyaXR0ZW46IHt9IC8vIHVzZWQgdG8ga2VlcCB0cmFjayBvZiBzY3JpcHQgZmlsZXMgd2UgaGF2ZSB3cml0dGVuXG4gIH07XG5cblxuICAvKipcbiAgICogVHJpZXMgdG8gZGV0ZWN0IHdoZXRoZXIgaXMgaW4gdGhlIGNvbnRleHQgb2YgYW4gSFRNTCBkb2N1bWVudC5cbiAgICogQHJldHVybiB7Ym9vbGVhbn0gVHJ1ZSBpZiBpdCBsb29rcyBsaWtlIEhUTUwgZG9jdW1lbnQuXG4gICAqIEBwcml2YXRlXG4gICAqL1xuICBnb29nLmluSHRtbERvY3VtZW50XyA9IGZ1bmN0aW9uKCkge1xuICAgIHZhciBkb2MgPSBnb29nLmdsb2JhbC5kb2N1bWVudDtcbiAgICByZXR1cm4gdHlwZW9mIGRvYyAhPSAndW5kZWZpbmVkJyAmJlxuICAgICAgICAgICAnd3JpdGUnIGluIGRvYzsgIC8vIFhVTERvY3VtZW50IG1pc3NlcyB3cml0ZS5cbiAgfTtcblxuXG4gIC8qKlxuICAgKiBUcmllcyB0byBkZXRlY3QgdGhlIGJhc2UgcGF0aCBvZiB0aGUgYmFzZS5qcyBzY3JpcHQgdGhhdCBib290c3RyYXBzIENsb3N1cmVcbiAgICogQHByaXZhdGVcbiAgICovXG4gIGdvb2cuZmluZEJhc2VQYXRoXyA9IGZ1bmN0aW9uKCkge1xuICAgIGlmIChnb29nLmdsb2JhbC5DTE9TVVJFX0JBU0VfUEFUSCkge1xuICAgICAgZ29vZy5iYXNlUGF0aCA9IGdvb2cuZ2xvYmFsLkNMT1NVUkVfQkFTRV9QQVRIO1xuICAgICAgcmV0dXJuO1xuICAgIH0gZWxzZSBpZiAoIWdvb2cuaW5IdG1sRG9jdW1lbnRfKCkpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgdmFyIGRvYyA9IGdvb2cuZ2xvYmFsLmRvY3VtZW50O1xuICAgIHZhciBzY3JpcHRzID0gZG9jLmdldEVsZW1lbnRzQnlUYWdOYW1lKCdzY3JpcHQnKTtcbiAgICAvLyBTZWFyY2ggYmFja3dhcmRzIHNpbmNlIHRoZSBjdXJyZW50IHNjcmlwdCBpcyBpbiBhbG1vc3QgYWxsIGNhc2VzIHRoZSBvbmVcbiAgICAvLyB0aGF0IGhhcyBiYXNlLmpzLlxuICAgIGZvciAodmFyIGkgPSBzY3JpcHRzLmxlbmd0aCAtIDE7IGkgPj0gMDsgLS1pKSB7XG4gICAgICB2YXIgc3JjID0gc2NyaXB0c1tpXS5zcmM7XG4gICAgICB2YXIgcW1hcmsgPSBzcmMubGFzdEluZGV4T2YoJz8nKTtcbiAgICAgIHZhciBsID0gcW1hcmsgPT0gLTEgPyBzcmMubGVuZ3RoIDogcW1hcms7XG4gICAgICBpZiAoc3JjLnN1YnN0cihsIC0gNywgNykgPT0gJ2Jhc2UuanMnKSB7XG4gICAgICAgIGdvb2cuYmFzZVBhdGggPSBzcmMuc3Vic3RyKDAsIGwgLSA3KTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuXG4gIC8qKlxuICAgKiBJbXBvcnRzIGEgc2NyaXB0IGlmLCBhbmQgb25seSBpZiwgdGhhdCBzY3JpcHQgaGFzbid0IGFscmVhZHkgYmVlbiBpbXBvcnRlZC5cbiAgICogKE11c3QgYmUgY2FsbGVkIGF0IGV4ZWN1dGlvbiB0aW1lKVxuICAgKiBAcGFyYW0ge3N0cmluZ30gc3JjIFNjcmlwdCBzb3VyY2UuXG4gICAqIEBwcml2YXRlXG4gICAqL1xuICBnb29nLmltcG9ydFNjcmlwdF8gPSBmdW5jdGlvbihzcmMpIHtcbiAgICB2YXIgaW1wb3J0U2NyaXB0ID0gZ29vZy5nbG9iYWwuQ0xPU1VSRV9JTVBPUlRfU0NSSVBUIHx8XG4gICAgICAgIGdvb2cud3JpdGVTY3JpcHRUYWdfO1xuICAgIGlmICghZ29vZy5kZXBlbmRlbmNpZXNfLndyaXR0ZW5bc3JjXSAmJiBpbXBvcnRTY3JpcHQoc3JjKSkge1xuICAgICAgZ29vZy5kZXBlbmRlbmNpZXNfLndyaXR0ZW5bc3JjXSA9IHRydWU7XG4gICAgfVxuICB9O1xuXG5cbiAgLyoqXG4gICAqIFRoZSBkZWZhdWx0IGltcGxlbWVudGF0aW9uIG9mIHRoZSBpbXBvcnQgZnVuY3Rpb24uIFdyaXRlcyBhIHNjcmlwdCB0YWcgdG9cbiAgICogaW1wb3J0IHRoZSBzY3JpcHQuXG4gICAqXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBzcmMgVGhlIHNjcmlwdCBzb3VyY2UuXG4gICAqIEByZXR1cm4ge2Jvb2xlYW59IFRydWUgaWYgdGhlIHNjcmlwdCB3YXMgaW1wb3J0ZWQsIGZhbHNlIG90aGVyd2lzZS5cbiAgICogQHByaXZhdGVcbiAgICovXG4gIGdvb2cud3JpdGVTY3JpcHRUYWdfID0gZnVuY3Rpb24oc3JjKSB7XG4gICAgaWYgKGdvb2cuaW5IdG1sRG9jdW1lbnRfKCkpIHtcbiAgICAgIHZhciBkb2MgPSBnb29nLmdsb2JhbC5kb2N1bWVudDtcbiAgICAgIGRvYy53cml0ZShcbiAgICAgICAgICAnPHNjcmlwdCB0eXBlPVwidGV4dC9qYXZhc2NyaXB0XCIgc3JjPVwiJyArIHNyYyArICdcIj48LycgKyAnc2NyaXB0PicpO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH07XG5cblxuICAvKipcbiAgICogUmVzb2x2ZXMgZGVwZW5kZW5jaWVzIGJhc2VkIG9uIHRoZSBkZXBlbmRlbmNpZXMgYWRkZWQgdXNpbmcgYWRkRGVwZW5kZW5jeVxuICAgKiBhbmQgY2FsbHMgaW1wb3J0U2NyaXB0XyBpbiB0aGUgY29ycmVjdCBvcmRlci5cbiAgICogQHByaXZhdGVcbiAgICovXG4gIGdvb2cud3JpdGVTY3JpcHRzXyA9IGZ1bmN0aW9uKCkge1xuICAgIC8vIHRoZSBzY3JpcHRzIHdlIG5lZWQgdG8gd3JpdGUgdGhpcyB0aW1lXG4gICAgdmFyIHNjcmlwdHMgPSBbXTtcbiAgICB2YXIgc2VlblNjcmlwdCA9IHt9O1xuICAgIHZhciBkZXBzID0gZ29vZy5kZXBlbmRlbmNpZXNfO1xuXG4gICAgZnVuY3Rpb24gdmlzaXROb2RlKHBhdGgpIHtcbiAgICAgIGlmIChwYXRoIGluIGRlcHMud3JpdHRlbikge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIHdlIGhhdmUgYWxyZWFkeSB2aXNpdGVkIHRoaXMgb25lLiBXZSBjYW4gZ2V0IGhlcmUgaWYgd2UgaGF2ZSBjeWNsaWNcbiAgICAgIC8vIGRlcGVuZGVuY2llc1xuICAgICAgaWYgKHBhdGggaW4gZGVwcy52aXNpdGVkKSB7XG4gICAgICAgIGlmICghKHBhdGggaW4gc2VlblNjcmlwdCkpIHtcbiAgICAgICAgICBzZWVuU2NyaXB0W3BhdGhdID0gdHJ1ZTtcbiAgICAgICAgICBzY3JpcHRzLnB1c2gocGF0aCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBkZXBzLnZpc2l0ZWRbcGF0aF0gPSB0cnVlO1xuXG4gICAgICBpZiAocGF0aCBpbiBkZXBzLnJlcXVpcmVzKSB7XG4gICAgICAgIGZvciAodmFyIHJlcXVpcmVOYW1lIGluIGRlcHMucmVxdWlyZXNbcGF0aF0pIHtcbiAgICAgICAgICAvLyBJZiB0aGUgcmVxdWlyZWQgbmFtZSBpcyBkZWZpbmVkLCB3ZSBhc3N1bWUgdGhhdCBpdCB3YXMgYWxyZWFkeVxuICAgICAgICAgIC8vIGJvb3RzdHJhcHBlZCBieSBvdGhlciBtZWFucy5cbiAgICAgICAgICBpZiAoIWdvb2cuaXNQcm92aWRlZF8ocmVxdWlyZU5hbWUpKSB7XG4gICAgICAgICAgICBpZiAocmVxdWlyZU5hbWUgaW4gZGVwcy5uYW1lVG9QYXRoKSB7XG4gICAgICAgICAgICAgIHZpc2l0Tm9kZShkZXBzLm5hbWVUb1BhdGhbcmVxdWlyZU5hbWVdKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgIHRocm93IEVycm9yKCdVbmRlZmluZWQgbmFtZVRvUGF0aCBmb3IgJyArIHJlcXVpcmVOYW1lKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaWYgKCEocGF0aCBpbiBzZWVuU2NyaXB0KSkge1xuICAgICAgICBzZWVuU2NyaXB0W3BhdGhdID0gdHJ1ZTtcbiAgICAgICAgc2NyaXB0cy5wdXNoKHBhdGgpO1xuICAgICAgfVxuICAgIH1cblxuICAgIGZvciAodmFyIHBhdGggaW4gZ29vZy5pbmNsdWRlZF8pIHtcbiAgICAgIGlmICghZGVwcy53cml0dGVuW3BhdGhdKSB7XG4gICAgICAgIHZpc2l0Tm9kZShwYXRoKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBmb3IgKHZhciBpID0gMDsgaSA8IHNjcmlwdHMubGVuZ3RoOyBpKyspIHtcbiAgICAgIGlmIChzY3JpcHRzW2ldKSB7XG4gICAgICAgIGdvb2cuaW1wb3J0U2NyaXB0Xyhnb29nLmJhc2VQYXRoICsgc2NyaXB0c1tpXSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBFcnJvcignVW5kZWZpbmVkIHNjcmlwdCBpbnB1dCcpO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuXG4gIC8qKlxuICAgKiBMb29rcyBhdCB0aGUgZGVwZW5kZW5jeSBydWxlcyBhbmQgdHJpZXMgdG8gZGV0ZXJtaW5lIHRoZSBzY3JpcHQgZmlsZSB0aGF0XG4gICAqIGZ1bGZpbGxzIGEgcGFydGljdWxhciBydWxlLlxuICAgKiBAcGFyYW0ge3N0cmluZ30gcnVsZSBJbiB0aGUgZm9ybSBnb29nLm5hbWVzcGFjZS5DbGFzcyBvciBwcm9qZWN0LnNjcmlwdC5cbiAgICogQHJldHVybiB7P3N0cmluZ30gVXJsIGNvcnJlc3BvbmRpbmcgdG8gdGhlIHJ1bGUsIG9yIG51bGwuXG4gICAqIEBwcml2YXRlXG4gICAqL1xuICBnb29nLmdldFBhdGhGcm9tRGVwc18gPSBmdW5jdGlvbihydWxlKSB7XG4gICAgaWYgKHJ1bGUgaW4gZ29vZy5kZXBlbmRlbmNpZXNfLm5hbWVUb1BhdGgpIHtcbiAgICAgIHJldHVybiBnb29nLmRlcGVuZGVuY2llc18ubmFtZVRvUGF0aFtydWxlXTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICB9O1xuXG4gIGdvb2cuZmluZEJhc2VQYXRoXygpO1xuXG4gIC8vIEFsbG93IHByb2plY3RzIHRvIG1hbmFnZSB0aGUgZGVwcyBmaWxlcyB0aGVtc2VsdmVzLlxuICBpZiAoIWdvb2cuZ2xvYmFsLkNMT1NVUkVfTk9fREVQUykge1xuICAgIGdvb2cuaW1wb3J0U2NyaXB0Xyhnb29nLmJhc2VQYXRoICsgJ2RlcHMuanMnKTtcbiAgfVxufVxuXG5cblxuLy89PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIExhbmd1YWdlIEVuaGFuY2VtZW50c1xuLy89PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cblxuXG4vKipcbiAqIFRoaXMgaXMgYSBcImZpeGVkXCIgdmVyc2lvbiBvZiB0aGUgdHlwZW9mIG9wZXJhdG9yLiAgSXQgZGlmZmVycyBmcm9tIHRoZSB0eXBlb2ZcbiAqIG9wZXJhdG9yIGluIHN1Y2ggYSB3YXkgdGhhdCBudWxsIHJldHVybnMgJ251bGwnIGFuZCBhcnJheXMgcmV0dXJuICdhcnJheScuXG4gKiBAcGFyYW0geyp9IHZhbHVlIFRoZSB2YWx1ZSB0byBnZXQgdGhlIHR5cGUgb2YuXG4gKiBAcmV0dXJuIHtzdHJpbmd9IFRoZSBuYW1lIG9mIHRoZSB0eXBlLlxuICovXG5nb29nLnR5cGVPZiA9IGZ1bmN0aW9uKHZhbHVlKSB7XG4gIHZhciBzID0gdHlwZW9mIHZhbHVlO1xuICBpZiAocyA9PSAnb2JqZWN0Jykge1xuICAgIGlmICh2YWx1ZSkge1xuICAgICAgLy8gQ2hlY2sgdGhlc2UgZmlyc3QsIHNvIHdlIGNhbiBhdm9pZCBjYWxsaW5nIE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmcgaWZcbiAgICAgIC8vIHBvc3NpYmxlLlxuICAgICAgLy9cbiAgICAgIC8vIElFIGltcHJvcGVybHkgbWFyc2hhbHMgdHllcG9mIGFjcm9zcyBleGVjdXRpb24gY29udGV4dHMsIGJ1dCBhXG4gICAgICAvLyBjcm9zcy1jb250ZXh0IG9iamVjdCB3aWxsIHN0aWxsIHJldHVybiBmYWxzZSBmb3IgXCJpbnN0YW5jZW9mIE9iamVjdFwiLlxuICAgICAgaWYgKHZhbHVlIGluc3RhbmNlb2YgQXJyYXkpIHtcbiAgICAgICAgcmV0dXJuICdhcnJheSc7XG4gICAgICB9IGVsc2UgaWYgKHZhbHVlIGluc3RhbmNlb2YgT2JqZWN0KSB7XG4gICAgICAgIHJldHVybiBzO1xuICAgICAgfVxuXG4gICAgICAvLyBIQUNLOiBJbiBvcmRlciB0byB1c2UgYW4gT2JqZWN0IHByb3RvdHlwZSBtZXRob2Qgb24gdGhlIGFyYml0cmFyeVxuICAgICAgLy8gICB2YWx1ZSwgdGhlIGNvbXBpbGVyIHJlcXVpcmVzIHRoZSB2YWx1ZSBiZSBjYXN0IHRvIHR5cGUgT2JqZWN0LFxuICAgICAgLy8gICBldmVuIHRob3VnaCB0aGUgRUNNQSBzcGVjIGV4cGxpY2l0bHkgYWxsb3dzIGl0LlxuICAgICAgdmFyIGNsYXNzTmFtZSA9IE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmcuY2FsbChcbiAgICAgICAgICAvKiogQHR5cGUge09iamVjdH0gKi8gKHZhbHVlKSk7XG4gICAgICAvLyBJbiBGaXJlZm94IDMuNiwgYXR0ZW1wdGluZyB0byBhY2Nlc3MgaWZyYW1lIHdpbmRvdyBvYmplY3RzJyBsZW5ndGhcbiAgICAgIC8vIHByb3BlcnR5IHRocm93cyBhbiBOU19FUlJPUl9GQUlMVVJFLCBzbyB3ZSBuZWVkIHRvIHNwZWNpYWwtY2FzZSBpdFxuICAgICAgLy8gaGVyZS5cbiAgICAgIGlmIChjbGFzc05hbWUgPT0gJ1tvYmplY3QgV2luZG93XScpIHtcbiAgICAgICAgcmV0dXJuICdvYmplY3QnO1xuICAgICAgfVxuXG4gICAgICAvLyBXZSBjYW5ub3QgYWx3YXlzIHVzZSBjb25zdHJ1Y3RvciA9PSBBcnJheSBvciBpbnN0YW5jZW9mIEFycmF5IGJlY2F1c2VcbiAgICAgIC8vIGRpZmZlcmVudCBmcmFtZXMgaGF2ZSBkaWZmZXJlbnQgQXJyYXkgb2JqZWN0cy4gSW4gSUU2LCBpZiB0aGUgaWZyYW1lXG4gICAgICAvLyB3aGVyZSB0aGUgYXJyYXkgd2FzIGNyZWF0ZWQgaXMgZGVzdHJveWVkLCB0aGUgYXJyYXkgbG9zZXMgaXRzXG4gICAgICAvLyBwcm90b3R5cGUuIFRoZW4gZGVyZWZlcmVuY2luZyB2YWwuc3BsaWNlIGhlcmUgdGhyb3dzIGFuIGV4Y2VwdGlvbiwgc29cbiAgICAgIC8vIHdlIGNhbid0IHVzZSBnb29nLmlzRnVuY3Rpb24uIENhbGxpbmcgdHlwZW9mIGRpcmVjdGx5IHJldHVybnMgJ3Vua25vd24nXG4gICAgICAvLyBzbyB0aGF0IHdpbGwgd29yay4gSW4gdGhpcyBjYXNlLCB0aGlzIGZ1bmN0aW9uIHdpbGwgcmV0dXJuIGZhbHNlIGFuZFxuICAgICAgLy8gbW9zdCBhcnJheSBmdW5jdGlvbnMgd2lsbCBzdGlsbCB3b3JrIGJlY2F1c2UgdGhlIGFycmF5IGlzIHN0aWxsXG4gICAgICAvLyBhcnJheS1saWtlIChzdXBwb3J0cyBsZW5ndGggYW5kIFtdKSBldmVuIHRob3VnaCBpdCBoYXMgbG9zdCBpdHNcbiAgICAgIC8vIHByb3RvdHlwZS5cbiAgICAgIC8vIE1hcmsgTWlsbGVyIG5vdGljZWQgdGhhdCBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nXG4gICAgICAvLyBhbGxvd3MgYWNjZXNzIHRvIHRoZSB1bmZvcmdlYWJsZSBbW0NsYXNzXV0gcHJvcGVydHkuXG4gICAgICAvLyAgMTUuMi40LjIgT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZyAoIClcbiAgICAgIC8vICBXaGVuIHRoZSB0b1N0cmluZyBtZXRob2QgaXMgY2FsbGVkLCB0aGUgZm9sbG93aW5nIHN0ZXBzIGFyZSB0YWtlbjpcbiAgICAgIC8vICAgICAgMS4gR2V0IHRoZSBbW0NsYXNzXV0gcHJvcGVydHkgb2YgdGhpcyBvYmplY3QuXG4gICAgICAvLyAgICAgIDIuIENvbXB1dGUgYSBzdHJpbmcgdmFsdWUgYnkgY29uY2F0ZW5hdGluZyB0aGUgdGhyZWUgc3RyaW5nc1xuICAgICAgLy8gICAgICAgICBcIltvYmplY3QgXCIsIFJlc3VsdCgxKSwgYW5kIFwiXVwiLlxuICAgICAgLy8gICAgICAzLiBSZXR1cm4gUmVzdWx0KDIpLlxuICAgICAgLy8gYW5kIHRoaXMgYmVoYXZpb3Igc3Vydml2ZXMgdGhlIGRlc3RydWN0aW9uIG9mIHRoZSBleGVjdXRpb24gY29udGV4dC5cbiAgICAgIGlmICgoY2xhc3NOYW1lID09ICdbb2JqZWN0IEFycmF5XScgfHxcbiAgICAgICAgICAgLy8gSW4gSUUgYWxsIG5vbiB2YWx1ZSB0eXBlcyBhcmUgd3JhcHBlZCBhcyBvYmplY3RzIGFjcm9zcyB3aW5kb3dcbiAgICAgICAgICAgLy8gYm91bmRhcmllcyAobm90IGlmcmFtZSB0aG91Z2gpIHNvIHdlIGhhdmUgdG8gZG8gb2JqZWN0IGRldGVjdGlvblxuICAgICAgICAgICAvLyBmb3IgdGhpcyBlZGdlIGNhc2VcbiAgICAgICAgICAgdHlwZW9mIHZhbHVlLmxlbmd0aCA9PSAnbnVtYmVyJyAmJlxuICAgICAgICAgICB0eXBlb2YgdmFsdWUuc3BsaWNlICE9ICd1bmRlZmluZWQnICYmXG4gICAgICAgICAgIHR5cGVvZiB2YWx1ZS5wcm9wZXJ0eUlzRW51bWVyYWJsZSAhPSAndW5kZWZpbmVkJyAmJlxuICAgICAgICAgICAhdmFsdWUucHJvcGVydHlJc0VudW1lcmFibGUoJ3NwbGljZScpXG5cbiAgICAgICAgICApKSB7XG4gICAgICAgIHJldHVybiAnYXJyYXknO1xuICAgICAgfVxuICAgICAgLy8gSEFDSzogVGhlcmUgaXMgc3RpbGwgYW4gYXJyYXkgY2FzZSB0aGF0IGZhaWxzLlxuICAgICAgLy8gICAgIGZ1bmN0aW9uIEFycmF5SW1wb3N0b3IoKSB7fVxuICAgICAgLy8gICAgIEFycmF5SW1wb3N0b3IucHJvdG90eXBlID0gW107XG4gICAgICAvLyAgICAgdmFyIGltcG9zdG9yID0gbmV3IEFycmF5SW1wb3N0b3I7XG4gICAgICAvLyB0aGlzIGNhbiBiZSBmaXhlZCBieSBnZXR0aW5nIHJpZCBvZiB0aGUgZmFzdCBwYXRoXG4gICAgICAvLyAodmFsdWUgaW5zdGFuY2VvZiBBcnJheSkgYW5kIHNvbGVseSByZWx5aW5nIG9uXG4gICAgICAvLyAodmFsdWUgJiYgT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy52YWxsKHZhbHVlKSA9PT0gJ1tvYmplY3QgQXJyYXldJylcbiAgICAgIC8vIGJ1dCB0aGF0IHdvdWxkIHJlcXVpcmUgbWFueSBtb3JlIGZ1bmN0aW9uIGNhbGxzIGFuZCBpcyBub3Qgd2FycmFudGVkXG4gICAgICAvLyB1bmxlc3MgY2xvc3VyZSBjb2RlIGlzIHJlY2VpdmluZyBvYmplY3RzIGZyb20gdW50cnVzdGVkIHNvdXJjZXMuXG5cbiAgICAgIC8vIElFIGluIGNyb3NzLXdpbmRvdyBjYWxscyBkb2VzIG5vdCBjb3JyZWN0bHkgbWFyc2hhbCB0aGUgZnVuY3Rpb24gdHlwZVxuICAgICAgLy8gKGl0IGFwcGVhcnMganVzdCBhcyBhbiBvYmplY3QpIHNvIHdlIGNhbm5vdCB1c2UganVzdCB0eXBlb2YgdmFsID09XG4gICAgICAvLyAnZnVuY3Rpb24nLiBIb3dldmVyLCBpZiB0aGUgb2JqZWN0IGhhcyBhIGNhbGwgcHJvcGVydHksIGl0IGlzIGFcbiAgICAgIC8vIGZ1bmN0aW9uLlxuICAgICAgaWYgKChjbGFzc05hbWUgPT0gJ1tvYmplY3QgRnVuY3Rpb25dJyB8fFxuICAgICAgICAgIHR5cGVvZiB2YWx1ZS5jYWxsICE9ICd1bmRlZmluZWQnICYmXG4gICAgICAgICAgdHlwZW9mIHZhbHVlLnByb3BlcnR5SXNFbnVtZXJhYmxlICE9ICd1bmRlZmluZWQnICYmXG4gICAgICAgICAgIXZhbHVlLnByb3BlcnR5SXNFbnVtZXJhYmxlKCdjYWxsJykpKSB7XG4gICAgICAgIHJldHVybiAnZnVuY3Rpb24nO1xuICAgICAgfVxuXG5cbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuICdudWxsJztcbiAgICB9XG5cbiAgfSBlbHNlIGlmIChzID09ICdmdW5jdGlvbicgJiYgdHlwZW9mIHZhbHVlLmNhbGwgPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAvLyBJbiBTYWZhcmkgdHlwZW9mIG5vZGVMaXN0IHJldHVybnMgJ2Z1bmN0aW9uJywgYW5kIG9uIEZpcmVmb3hcbiAgICAvLyB0eXBlb2YgYmVoYXZlcyBzaW1pbGFybHkgZm9yIEhUTUx7QXBwbGV0LEVtYmVkLE9iamVjdH1FbGVtZW50c1xuICAgIC8vIGFuZCBSZWdFeHBzLiAgV2Ugd291bGQgbGlrZSB0byByZXR1cm4gb2JqZWN0IGZvciB0aG9zZSBhbmQgd2UgY2FuXG4gICAgLy8gZGV0ZWN0IGFuIGludmFsaWQgZnVuY3Rpb24gYnkgbWFraW5nIHN1cmUgdGhhdCB0aGUgZnVuY3Rpb25cbiAgICAvLyBvYmplY3QgaGFzIGEgY2FsbCBtZXRob2QuXG4gICAgcmV0dXJuICdvYmplY3QnO1xuICB9XG4gIHJldHVybiBzO1xufTtcblxuXG4vKipcbiAqIFJldHVybnMgdHJ1ZSBpZiB0aGUgc3BlY2lmaWVkIHZhbHVlIGlzIG5vdCB8dW5kZWZpbmVkfC5cbiAqIFdBUk5JTkc6IERvIG5vdCB1c2UgdGhpcyB0byB0ZXN0IGlmIGFuIG9iamVjdCBoYXMgYSBwcm9wZXJ0eS4gVXNlIHRoZSBpblxuICogb3BlcmF0b3IgaW5zdGVhZC4gIEFkZGl0aW9uYWxseSwgdGhpcyBmdW5jdGlvbiBhc3N1bWVzIHRoYXQgdGhlIGdsb2JhbFxuICogdW5kZWZpbmVkIHZhcmlhYmxlIGhhcyBub3QgYmVlbiByZWRlZmluZWQuXG4gKiBAcGFyYW0geyp9IHZhbCBWYXJpYWJsZSB0byB0ZXN0LlxuICogQHJldHVybiB7Ym9vbGVhbn0gV2hldGhlciB2YXJpYWJsZSBpcyBkZWZpbmVkLlxuICovXG5nb29nLmlzRGVmID0gZnVuY3Rpb24odmFsKSB7XG4gIHJldHVybiB2YWwgIT09IHVuZGVmaW5lZDtcbn07XG5cblxuLyoqXG4gKiBSZXR1cm5zIHRydWUgaWYgdGhlIHNwZWNpZmllZCB2YWx1ZSBpcyB8bnVsbHxcbiAqIEBwYXJhbSB7Kn0gdmFsIFZhcmlhYmxlIHRvIHRlc3QuXG4gKiBAcmV0dXJuIHtib29sZWFufSBXaGV0aGVyIHZhcmlhYmxlIGlzIG51bGwuXG4gKi9cbmdvb2cuaXNOdWxsID0gZnVuY3Rpb24odmFsKSB7XG4gIHJldHVybiB2YWwgPT09IG51bGw7XG59O1xuXG5cbi8qKlxuICogUmV0dXJucyB0cnVlIGlmIHRoZSBzcGVjaWZpZWQgdmFsdWUgaXMgZGVmaW5lZCBhbmQgbm90IG51bGxcbiAqIEBwYXJhbSB7Kn0gdmFsIFZhcmlhYmxlIHRvIHRlc3QuXG4gKiBAcmV0dXJuIHtib29sZWFufSBXaGV0aGVyIHZhcmlhYmxlIGlzIGRlZmluZWQgYW5kIG5vdCBudWxsLlxuICovXG5nb29nLmlzRGVmQW5kTm90TnVsbCA9IGZ1bmN0aW9uKHZhbCkge1xuICAvLyBOb3RlIHRoYXQgdW5kZWZpbmVkID09IG51bGwuXG4gIHJldHVybiB2YWwgIT0gbnVsbDtcbn07XG5cblxuLyoqXG4gKiBSZXR1cm5zIHRydWUgaWYgdGhlIHNwZWNpZmllZCB2YWx1ZSBpcyBhbiBhcnJheVxuICogQHBhcmFtIHsqfSB2YWwgVmFyaWFibGUgdG8gdGVzdC5cbiAqIEByZXR1cm4ge2Jvb2xlYW59IFdoZXRoZXIgdmFyaWFibGUgaXMgYW4gYXJyYXkuXG4gKi9cbmdvb2cuaXNBcnJheSA9IGZ1bmN0aW9uKHZhbCkge1xuICByZXR1cm4gZ29vZy50eXBlT2YodmFsKSA9PSAnYXJyYXknO1xufTtcblxuXG4vKipcbiAqIFJldHVybnMgdHJ1ZSBpZiB0aGUgb2JqZWN0IGxvb2tzIGxpa2UgYW4gYXJyYXkuIFRvIHF1YWxpZnkgYXMgYXJyYXkgbGlrZVxuICogdGhlIHZhbHVlIG5lZWRzIHRvIGJlIGVpdGhlciBhIE5vZGVMaXN0IG9yIGFuIG9iamVjdCB3aXRoIGEgTnVtYmVyIGxlbmd0aFxuICogcHJvcGVydHkuXG4gKiBAcGFyYW0geyp9IHZhbCBWYXJpYWJsZSB0byB0ZXN0LlxuICogQHJldHVybiB7Ym9vbGVhbn0gV2hldGhlciB2YXJpYWJsZSBpcyBhbiBhcnJheS5cbiAqL1xuZ29vZy5pc0FycmF5TGlrZSA9IGZ1bmN0aW9uKHZhbCkge1xuICB2YXIgdHlwZSA9IGdvb2cudHlwZU9mKHZhbCk7XG4gIHJldHVybiB0eXBlID09ICdhcnJheScgfHwgdHlwZSA9PSAnb2JqZWN0JyAmJiB0eXBlb2YgdmFsLmxlbmd0aCA9PSAnbnVtYmVyJztcbn07XG5cblxuLyoqXG4gKiBSZXR1cm5zIHRydWUgaWYgdGhlIG9iamVjdCBsb29rcyBsaWtlIGEgRGF0ZS4gVG8gcXVhbGlmeSBhcyBEYXRlLWxpa2VcbiAqIHRoZSB2YWx1ZSBuZWVkcyB0byBiZSBhbiBvYmplY3QgYW5kIGhhdmUgYSBnZXRGdWxsWWVhcigpIGZ1bmN0aW9uLlxuICogQHBhcmFtIHsqfSB2YWwgVmFyaWFibGUgdG8gdGVzdC5cbiAqIEByZXR1cm4ge2Jvb2xlYW59IFdoZXRoZXIgdmFyaWFibGUgaXMgYSBsaWtlIGEgRGF0ZS5cbiAqL1xuZ29vZy5pc0RhdGVMaWtlID0gZnVuY3Rpb24odmFsKSB7XG4gIHJldHVybiBnb29nLmlzT2JqZWN0KHZhbCkgJiYgdHlwZW9mIHZhbC5nZXRGdWxsWWVhciA9PSAnZnVuY3Rpb24nO1xufTtcblxuXG4vKipcbiAqIFJldHVybnMgdHJ1ZSBpZiB0aGUgc3BlY2lmaWVkIHZhbHVlIGlzIGEgc3RyaW5nXG4gKiBAcGFyYW0geyp9IHZhbCBWYXJpYWJsZSB0byB0ZXN0LlxuICogQHJldHVybiB7Ym9vbGVhbn0gV2hldGhlciB2YXJpYWJsZSBpcyBhIHN0cmluZy5cbiAqL1xuZ29vZy5pc1N0cmluZyA9IGZ1bmN0aW9uKHZhbCkge1xuICByZXR1cm4gdHlwZW9mIHZhbCA9PSAnc3RyaW5nJztcbn07XG5cblxuLyoqXG4gKiBSZXR1cm5zIHRydWUgaWYgdGhlIHNwZWNpZmllZCB2YWx1ZSBpcyBhIGJvb2xlYW5cbiAqIEBwYXJhbSB7Kn0gdmFsIFZhcmlhYmxlIHRvIHRlc3QuXG4gKiBAcmV0dXJuIHtib29sZWFufSBXaGV0aGVyIHZhcmlhYmxlIGlzIGJvb2xlYW4uXG4gKi9cbmdvb2cuaXNCb29sZWFuID0gZnVuY3Rpb24odmFsKSB7XG4gIHJldHVybiB0eXBlb2YgdmFsID09ICdib29sZWFuJztcbn07XG5cblxuLyoqXG4gKiBSZXR1cm5zIHRydWUgaWYgdGhlIHNwZWNpZmllZCB2YWx1ZSBpcyBhIG51bWJlclxuICogQHBhcmFtIHsqfSB2YWwgVmFyaWFibGUgdG8gdGVzdC5cbiAqIEByZXR1cm4ge2Jvb2xlYW59IFdoZXRoZXIgdmFyaWFibGUgaXMgYSBudW1iZXIuXG4gKi9cbmdvb2cuaXNOdW1iZXIgPSBmdW5jdGlvbih2YWwpIHtcbiAgcmV0dXJuIHR5cGVvZiB2YWwgPT0gJ251bWJlcic7XG59O1xuXG5cbi8qKlxuICogUmV0dXJucyB0cnVlIGlmIHRoZSBzcGVjaWZpZWQgdmFsdWUgaXMgYSBmdW5jdGlvblxuICogQHBhcmFtIHsqfSB2YWwgVmFyaWFibGUgdG8gdGVzdC5cbiAqIEByZXR1cm4ge2Jvb2xlYW59IFdoZXRoZXIgdmFyaWFibGUgaXMgYSBmdW5jdGlvbi5cbiAqL1xuZ29vZy5pc0Z1bmN0aW9uID0gZnVuY3Rpb24odmFsKSB7XG4gIHJldHVybiBnb29nLnR5cGVPZih2YWwpID09ICdmdW5jdGlvbic7XG59O1xuXG5cbi8qKlxuICogUmV0dXJucyB0cnVlIGlmIHRoZSBzcGVjaWZpZWQgdmFsdWUgaXMgYW4gb2JqZWN0LiAgVGhpcyBpbmNsdWRlcyBhcnJheXNcbiAqIGFuZCBmdW5jdGlvbnMuXG4gKiBAcGFyYW0geyp9IHZhbCBWYXJpYWJsZSB0byB0ZXN0LlxuICogQHJldHVybiB7Ym9vbGVhbn0gV2hldGhlciB2YXJpYWJsZSBpcyBhbiBvYmplY3QuXG4gKi9cbmdvb2cuaXNPYmplY3QgPSBmdW5jdGlvbih2YWwpIHtcbiAgdmFyIHR5cGUgPSB0eXBlb2YgdmFsO1xuICByZXR1cm4gdHlwZSA9PSAnb2JqZWN0JyAmJiB2YWwgIT0gbnVsbCB8fCB0eXBlID09ICdmdW5jdGlvbic7XG4gIC8vIHJldHVybiBPYmplY3QodmFsKSA9PT0gdmFsIGFsc28gd29ya3MsIGJ1dCBpcyBzbG93ZXIsIGVzcGVjaWFsbHkgaWYgdmFsIGlzXG4gIC8vIG5vdCBhbiBvYmplY3QuXG59O1xuXG5cbi8qKlxuICogR2V0cyBhIHVuaXF1ZSBJRCBmb3IgYW4gb2JqZWN0LiBUaGlzIG11dGF0ZXMgdGhlIG9iamVjdCBzbyB0aGF0IGZ1cnRoZXJcbiAqIGNhbGxzIHdpdGggdGhlIHNhbWUgb2JqZWN0IGFzIGEgcGFyYW1ldGVyIHJldHVybnMgdGhlIHNhbWUgdmFsdWUuIFRoZSB1bmlxdWVcbiAqIElEIGlzIGd1YXJhbnRlZWQgdG8gYmUgdW5pcXVlIGFjcm9zcyB0aGUgY3VycmVudCBzZXNzaW9uIGFtb25nc3Qgb2JqZWN0cyB0aGF0XG4gKiBhcmUgcGFzc2VkIGludG8ge0Bjb2RlIGdldFVpZH0uIFRoZXJlIGlzIG5vIGd1YXJhbnRlZSB0aGF0IHRoZSBJRCBpcyB1bmlxdWVcbiAqIG9yIGNvbnNpc3RlbnQgYWNyb3NzIHNlc3Npb25zLiBJdCBpcyB1bnNhZmUgdG8gZ2VuZXJhdGUgdW5pcXVlIElEIGZvclxuICogZnVuY3Rpb24gcHJvdG90eXBlcy5cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gb2JqIFRoZSBvYmplY3QgdG8gZ2V0IHRoZSB1bmlxdWUgSUQgZm9yLlxuICogQHJldHVybiB7bnVtYmVyfSBUaGUgdW5pcXVlIElEIGZvciB0aGUgb2JqZWN0LlxuICovXG5nb29nLmdldFVpZCA9IGZ1bmN0aW9uKG9iaikge1xuICAvLyBUT0RPKGFydik6IE1ha2UgdGhlIHR5cGUgc3RyaWN0ZXIsIGRvIG5vdCBhY2NlcHQgbnVsbC5cblxuICAvLyBJbiBPcGVyYSB3aW5kb3cuaGFzT3duUHJvcGVydHkgZXhpc3RzIGJ1dCBhbHdheXMgcmV0dXJucyBmYWxzZSBzbyB3ZSBhdm9pZFxuICAvLyB1c2luZyBpdC4gQXMgYSBjb25zZXF1ZW5jZSB0aGUgdW5pcXVlIElEIGdlbmVyYXRlZCBmb3IgQmFzZUNsYXNzLnByb3RvdHlwZVxuICAvLyBhbmQgU3ViQ2xhc3MucHJvdG90eXBlIHdpbGwgYmUgdGhlIHNhbWUuXG4gIHJldHVybiBvYmpbZ29vZy5VSURfUFJPUEVSVFlfXSB8fFxuICAgICAgKG9ialtnb29nLlVJRF9QUk9QRVJUWV9dID0gKytnb29nLnVpZENvdW50ZXJfKTtcbn07XG5cblxuLyoqXG4gKiBSZW1vdmVzIHRoZSB1bmlxdWUgSUQgZnJvbSBhbiBvYmplY3QuIFRoaXMgaXMgdXNlZnVsIGlmIHRoZSBvYmplY3Qgd2FzXG4gKiBwcmV2aW91c2x5IG11dGF0ZWQgdXNpbmcge0Bjb2RlIGdvb2cuZ2V0VWlkfSBpbiB3aGljaCBjYXNlIHRoZSBtdXRhdGlvbiBpc1xuICogdW5kb25lLlxuICogQHBhcmFtIHtPYmplY3R9IG9iaiBUaGUgb2JqZWN0IHRvIHJlbW92ZSB0aGUgdW5pcXVlIElEIGZpZWxkIGZyb20uXG4gKi9cbmdvb2cucmVtb3ZlVWlkID0gZnVuY3Rpb24ob2JqKSB7XG4gIC8vIFRPRE8oYXJ2KTogTWFrZSB0aGUgdHlwZSBzdHJpY3RlciwgZG8gbm90IGFjY2VwdCBudWxsLlxuXG4gIC8vIERPTSBub2RlcyBpbiBJRSBhcmUgbm90IGluc3RhbmNlIG9mIE9iamVjdCBhbmQgdGhyb3dzIGV4Y2VwdGlvblxuICAvLyBmb3IgZGVsZXRlLiBJbnN0ZWFkIHdlIHRyeSB0byB1c2UgcmVtb3ZlQXR0cmlidXRlXG4gIGlmICgncmVtb3ZlQXR0cmlidXRlJyBpbiBvYmopIHtcbiAgICBvYmoucmVtb3ZlQXR0cmlidXRlKGdvb2cuVUlEX1BST1BFUlRZXyk7XG4gIH1cbiAgLyoqIEBwcmVzZXJ2ZVRyeSAqL1xuICB0cnkge1xuICAgIGRlbGV0ZSBvYmpbZ29vZy5VSURfUFJPUEVSVFlfXTtcbiAgfSBjYXRjaCAoZXgpIHtcbiAgfVxufTtcblxuXG4vKipcbiAqIE5hbWUgZm9yIHVuaXF1ZSBJRCBwcm9wZXJ0eS4gSW5pdGlhbGl6ZWQgaW4gYSB3YXkgdG8gaGVscCBhdm9pZCBjb2xsaXNpb25zXG4gKiB3aXRoIG90aGVyIGNsb3N1cmUgamF2YXNjcmlwdCBvbiB0aGUgc2FtZSBwYWdlLlxuICogQHR5cGUge3N0cmluZ31cbiAqIEBwcml2YXRlXG4gKi9cbmdvb2cuVUlEX1BST1BFUlRZXyA9ICdjbG9zdXJlX3VpZF8nICtcbiAgICBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAyMTQ3NDgzNjQ4KS50b1N0cmluZygzNik7XG5cblxuLyoqXG4gKiBDb3VudGVyIGZvciBVSUQuXG4gKiBAdHlwZSB7bnVtYmVyfVxuICogQHByaXZhdGVcbiAqL1xuZ29vZy51aWRDb3VudGVyXyA9IDA7XG5cblxuLyoqXG4gKiBBZGRzIGEgaGFzaCBjb2RlIGZpZWxkIHRvIGFuIG9iamVjdC4gVGhlIGhhc2ggY29kZSBpcyB1bmlxdWUgZm9yIHRoZVxuICogZ2l2ZW4gb2JqZWN0LlxuICogQHBhcmFtIHtPYmplY3R9IG9iaiBUaGUgb2JqZWN0IHRvIGdldCB0aGUgaGFzaCBjb2RlIGZvci5cbiAqIEByZXR1cm4ge251bWJlcn0gVGhlIGhhc2ggY29kZSBmb3IgdGhlIG9iamVjdC5cbiAqIEBkZXByZWNhdGVkIFVzZSBnb29nLmdldFVpZCBpbnN0ZWFkLlxuICovXG5nb29nLmdldEhhc2hDb2RlID0gZ29vZy5nZXRVaWQ7XG5cblxuLyoqXG4gKiBSZW1vdmVzIHRoZSBoYXNoIGNvZGUgZmllbGQgZnJvbSBhbiBvYmplY3QuXG4gKiBAcGFyYW0ge09iamVjdH0gb2JqIFRoZSBvYmplY3QgdG8gcmVtb3ZlIHRoZSBmaWVsZCBmcm9tLlxuICogQGRlcHJlY2F0ZWQgVXNlIGdvb2cucmVtb3ZlVWlkIGluc3RlYWQuXG4gKi9cbmdvb2cucmVtb3ZlSGFzaENvZGUgPSBnb29nLnJlbW92ZVVpZDtcblxuXG4vKipcbiAqIENsb25lcyBhIHZhbHVlLiBUaGUgaW5wdXQgbWF5IGJlIGFuIE9iamVjdCwgQXJyYXksIG9yIGJhc2ljIHR5cGUuIE9iamVjdHMgYW5kXG4gKiBhcnJheXMgd2lsbCBiZSBjbG9uZWQgcmVjdXJzaXZlbHkuXG4gKlxuICogV0FSTklOR1M6XG4gKiA8Y29kZT5nb29nLmNsb25lT2JqZWN0PC9jb2RlPiBkb2VzIG5vdCBkZXRlY3QgcmVmZXJlbmNlIGxvb3BzLiBPYmplY3RzIHRoYXRcbiAqIHJlZmVyIHRvIHRoZW1zZWx2ZXMgd2lsbCBjYXVzZSBpbmZpbml0ZSByZWN1cnNpb24uXG4gKlxuICogPGNvZGU+Z29vZy5jbG9uZU9iamVjdDwvY29kZT4gaXMgdW5hd2FyZSBvZiB1bmlxdWUgaWRlbnRpZmllcnMsIGFuZCBjb3BpZXNcbiAqIFVJRHMgY3JlYXRlZCBieSA8Y29kZT5nZXRVaWQ8L2NvZGU+IGludG8gY2xvbmVkIHJlc3VsdHMuXG4gKlxuICogQHBhcmFtIHsqfSBvYmogVGhlIHZhbHVlIHRvIGNsb25lLlxuICogQHJldHVybiB7Kn0gQSBjbG9uZSBvZiB0aGUgaW5wdXQgdmFsdWUuXG4gKiBAZGVwcmVjYXRlZCBnb29nLmNsb25lT2JqZWN0IGlzIHVuc2FmZS4gUHJlZmVyIHRoZSBnb29nLm9iamVjdCBtZXRob2RzLlxuICovXG5nb29nLmNsb25lT2JqZWN0ID0gZnVuY3Rpb24ob2JqKSB7XG4gIHZhciB0eXBlID0gZ29vZy50eXBlT2Yob2JqKTtcbiAgaWYgKHR5cGUgPT0gJ29iamVjdCcgfHwgdHlwZSA9PSAnYXJyYXknKSB7XG4gICAgaWYgKG9iai5jbG9uZSkge1xuICAgICAgcmV0dXJuIG9iai5jbG9uZSgpO1xuICAgIH1cbiAgICB2YXIgY2xvbmUgPSB0eXBlID09ICdhcnJheScgPyBbXSA6IHt9O1xuICAgIGZvciAodmFyIGtleSBpbiBvYmopIHtcbiAgICAgIGNsb25lW2tleV0gPSBnb29nLmNsb25lT2JqZWN0KG9ialtrZXldKTtcbiAgICB9XG4gICAgcmV0dXJuIGNsb25lO1xuICB9XG5cbiAgcmV0dXJuIG9iajtcbn07XG5cblxuLyoqXG4gKiBGb3J3YXJkIGRlY2xhcmF0aW9uIGZvciB0aGUgY2xvbmUgbWV0aG9kLiBUaGlzIGlzIG5lY2Vzc2FyeSB1bnRpbCB0aGVcbiAqIGNvbXBpbGVyIGNhbiBiZXR0ZXIgc3VwcG9ydCBkdWNrLXR5cGluZyBjb25zdHJ1Y3RzIGFzIHVzZWQgaW5cbiAqIGdvb2cuY2xvbmVPYmplY3QuXG4gKlxuICogVE9ETyhicmVubmVtYW4pOiBSZW1vdmUgb25jZSB0aGUgSlNDb21waWxlciBjYW4gaW5mZXIgdGhhdCB0aGUgY2hlY2sgZm9yXG4gKiBwcm90by5jbG9uZSBpcyBzYWZlIGluIGdvb2cuY2xvbmVPYmplY3QuXG4gKlxuICogQHR5cGUge0Z1bmN0aW9ufVxuICovXG5PYmplY3QucHJvdG90eXBlLmNsb25lO1xuXG5cbi8qKlxuICogQSBuYXRpdmUgaW1wbGVtZW50YXRpb24gb2YgZ29vZy5iaW5kLlxuICogQHBhcmFtIHtGdW5jdGlvbn0gZm4gQSBmdW5jdGlvbiB0byBwYXJ0aWFsbHkgYXBwbHkuXG4gKiBAcGFyYW0ge09iamVjdHx1bmRlZmluZWR9IHNlbGZPYmogU3BlY2lmaWVzIHRoZSBvYmplY3Qgd2hpY2ggfHRoaXN8IHNob3VsZFxuICogICAgIHBvaW50IHRvIHdoZW4gdGhlIGZ1bmN0aW9uIGlzIHJ1bi5cbiAqIEBwYXJhbSB7Li4uKn0gdmFyX2FyZ3MgQWRkaXRpb25hbCBhcmd1bWVudHMgdGhhdCBhcmUgcGFydGlhbGx5XG4gKiAgICAgYXBwbGllZCB0byB0aGUgZnVuY3Rpb24uXG4gKiBAcmV0dXJuIHshRnVuY3Rpb259IEEgcGFydGlhbGx5LWFwcGxpZWQgZm9ybSBvZiB0aGUgZnVuY3Rpb24gYmluZCgpIHdhc1xuICogICAgIGludm9rZWQgYXMgYSBtZXRob2Qgb2YuXG4gKiBAcHJpdmF0ZVxuICogQHN1cHByZXNzIHtkZXByZWNhdGVkfSBUaGUgY29tcGlsZXIgdGhpbmtzIHRoYXQgRnVuY3Rpb24ucHJvdG90eXBlLmJpbmRcbiAqICAgICBpcyBkZXByZWNhdGVkIGJlY2F1c2Ugc29tZSBwZW9wbGUgaGF2ZSBkZWNsYXJlZCBhIHB1cmUtSlMgdmVyc2lvbi5cbiAqICAgICBPbmx5IHRoZSBwdXJlLUpTIHZlcnNpb24gaXMgdHJ1bHkgZGVwcmVjYXRlZC5cbiAqL1xuZ29vZy5iaW5kTmF0aXZlXyA9IGZ1bmN0aW9uKGZuLCBzZWxmT2JqLCB2YXJfYXJncykge1xuICByZXR1cm4gLyoqIEB0eXBlIHshRnVuY3Rpb259ICovIChmbi5jYWxsLmFwcGx5KGZuLmJpbmQsIGFyZ3VtZW50cykpO1xufTtcblxuXG4vKipcbiAqIEEgcHVyZS1KUyBpbXBsZW1lbnRhdGlvbiBvZiBnb29nLmJpbmQuXG4gKiBAcGFyYW0ge0Z1bmN0aW9ufSBmbiBBIGZ1bmN0aW9uIHRvIHBhcnRpYWxseSBhcHBseS5cbiAqIEBwYXJhbSB7T2JqZWN0fHVuZGVmaW5lZH0gc2VsZk9iaiBTcGVjaWZpZXMgdGhlIG9iamVjdCB3aGljaCB8dGhpc3wgc2hvdWxkXG4gKiAgICAgcG9pbnQgdG8gd2hlbiB0aGUgZnVuY3Rpb24gaXMgcnVuLlxuICogQHBhcmFtIHsuLi4qfSB2YXJfYXJncyBBZGRpdGlvbmFsIGFyZ3VtZW50cyB0aGF0IGFyZSBwYXJ0aWFsbHlcbiAqICAgICBhcHBsaWVkIHRvIHRoZSBmdW5jdGlvbi5cbiAqIEByZXR1cm4geyFGdW5jdGlvbn0gQSBwYXJ0aWFsbHktYXBwbGllZCBmb3JtIG9mIHRoZSBmdW5jdGlvbiBiaW5kKCkgd2FzXG4gKiAgICAgaW52b2tlZCBhcyBhIG1ldGhvZCBvZi5cbiAqIEBwcml2YXRlXG4gKi9cbmdvb2cuYmluZEpzXyA9IGZ1bmN0aW9uKGZuLCBzZWxmT2JqLCB2YXJfYXJncykge1xuICBpZiAoIWZuKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCk7XG4gIH1cblxuICBpZiAoYXJndW1lbnRzLmxlbmd0aCA+IDIpIHtcbiAgICB2YXIgYm91bmRBcmdzID0gQXJyYXkucHJvdG90eXBlLnNsaWNlLmNhbGwoYXJndW1lbnRzLCAyKTtcbiAgICByZXR1cm4gZnVuY3Rpb24oKSB7XG4gICAgICAvLyBQcmVwZW5kIHRoZSBib3VuZCBhcmd1bWVudHMgdG8gdGhlIGN1cnJlbnQgYXJndW1lbnRzLlxuICAgICAgdmFyIG5ld0FyZ3MgPSBBcnJheS5wcm90b3R5cGUuc2xpY2UuY2FsbChhcmd1bWVudHMpO1xuICAgICAgQXJyYXkucHJvdG90eXBlLnVuc2hpZnQuYXBwbHkobmV3QXJncywgYm91bmRBcmdzKTtcbiAgICAgIHJldHVybiBmbi5hcHBseShzZWxmT2JqLCBuZXdBcmdzKTtcbiAgICB9O1xuXG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uKCkge1xuICAgICAgcmV0dXJuIGZuLmFwcGx5KHNlbGZPYmosIGFyZ3VtZW50cyk7XG4gICAgfTtcbiAgfVxufTtcblxuXG4vKipcbiAqIFBhcnRpYWxseSBhcHBsaWVzIHRoaXMgZnVuY3Rpb24gdG8gYSBwYXJ0aWN1bGFyICd0aGlzIG9iamVjdCcgYW5kIHplcm8gb3JcbiAqIG1vcmUgYXJndW1lbnRzLiBUaGUgcmVzdWx0IGlzIGEgbmV3IGZ1bmN0aW9uIHdpdGggc29tZSBhcmd1bWVudHMgb2YgdGhlIGZpcnN0XG4gKiBmdW5jdGlvbiBwcmUtZmlsbGVkIGFuZCB0aGUgdmFsdWUgb2YgfHRoaXN8ICdwcmUtc3BlY2lmaWVkJy48YnI+PGJyPlxuICpcbiAqIFJlbWFpbmluZyBhcmd1bWVudHMgc3BlY2lmaWVkIGF0IGNhbGwtdGltZSBhcmUgYXBwZW5kZWQgdG8gdGhlIHByZS1cbiAqIHNwZWNpZmllZCBvbmVzLjxicj48YnI+XG4gKlxuICogQWxzbyBzZWU6IHtAbGluayAjcGFydGlhbH0uPGJyPjxicj5cbiAqXG4gKiBVc2FnZTpcbiAqIDxwcmU+dmFyIGJhck1ldGhCb3VuZCA9IGJpbmQobXlGdW5jdGlvbiwgbXlPYmosICdhcmcxJywgJ2FyZzInKTtcbiAqIGJhck1ldGhCb3VuZCgnYXJnMycsICdhcmc0Jyk7PC9wcmU+XG4gKlxuICogQHBhcmFtIHtGdW5jdGlvbn0gZm4gQSBmdW5jdGlvbiB0byBwYXJ0aWFsbHkgYXBwbHkuXG4gKiBAcGFyYW0ge09iamVjdHx1bmRlZmluZWR9IHNlbGZPYmogU3BlY2lmaWVzIHRoZSBvYmplY3Qgd2hpY2ggfHRoaXN8IHNob3VsZFxuICogICAgIHBvaW50IHRvIHdoZW4gdGhlIGZ1bmN0aW9uIGlzIHJ1bi5cbiAqIEBwYXJhbSB7Li4uKn0gdmFyX2FyZ3MgQWRkaXRpb25hbCBhcmd1bWVudHMgdGhhdCBhcmUgcGFydGlhbGx5XG4gKiAgICAgYXBwbGllZCB0byB0aGUgZnVuY3Rpb24uXG4gKiBAcmV0dXJuIHshRnVuY3Rpb259IEEgcGFydGlhbGx5LWFwcGxpZWQgZm9ybSBvZiB0aGUgZnVuY3Rpb24gYmluZCgpIHdhc1xuICogICAgIGludm9rZWQgYXMgYSBtZXRob2Qgb2YuXG4gKiBAc3VwcHJlc3Mge2RlcHJlY2F0ZWR9IFNlZSBhYm92ZS5cbiAqL1xuZ29vZy5iaW5kID0gZnVuY3Rpb24oZm4sIHNlbGZPYmosIHZhcl9hcmdzKSB7XG4gIC8vIFRPRE8obmlja3NhbnRvcyk6IG5hcnJvdyB0aGUgdHlwZSBzaWduYXR1cmUuXG4gIGlmIChGdW5jdGlvbi5wcm90b3R5cGUuYmluZCAmJlxuICAgICAgLy8gTk9URShuaWNrc2FudG9zKTogU29tZWJvZHkgcHVsbGVkIGJhc2UuanMgaW50byB0aGUgZGVmYXVsdFxuICAgICAgLy8gQ2hyb21lIGV4dGVuc2lvbiBlbnZpcm9ubWVudC4gVGhpcyBtZWFucyB0aGF0IGZvciBDaHJvbWUgZXh0ZW5zaW9ucyxcbiAgICAgIC8vIHRoZXkgZ2V0IHRoZSBpbXBsZW1lbnRhdGlvbiBvZiBGdW5jdGlvbi5wcm90b3R5cGUuYmluZCB0aGF0XG4gICAgICAvLyBjYWxscyBnb29nLmJpbmQgaW5zdGVhZCBvZiB0aGUgbmF0aXZlIG9uZS4gRXZlbiB3b3JzZSwgd2UgZG9uJ3Qgd2FudFxuICAgICAgLy8gdG8gaW50cm9kdWNlIGEgY2lyY3VsYXIgZGVwZW5kZW5jeSBiZXR3ZWVuIGdvb2cuYmluZCBhbmRcbiAgICAgIC8vIEZ1bmN0aW9uLnByb3RvdHlwZS5iaW5kLCBzbyB3ZSBoYXZlIHRvIGhhY2sgdGhpcyB0byBtYWtlIHN1cmUgaXRcbiAgICAgIC8vIHdvcmtzIGNvcnJlY3RseS5cbiAgICAgIEZ1bmN0aW9uLnByb3RvdHlwZS5iaW5kLnRvU3RyaW5nKCkuaW5kZXhPZignbmF0aXZlIGNvZGUnKSAhPSAtMSkge1xuICAgIGdvb2cuYmluZCA9IGdvb2cuYmluZE5hdGl2ZV87XG4gIH0gZWxzZSB7XG4gICAgZ29vZy5iaW5kID0gZ29vZy5iaW5kSnNfO1xuICB9XG4gIHJldHVybiBnb29nLmJpbmQuYXBwbHkobnVsbCwgYXJndW1lbnRzKTtcbn07XG5cblxuLyoqXG4gKiBMaWtlIGJpbmQoKSwgZXhjZXB0IHRoYXQgYSAndGhpcyBvYmplY3QnIGlzIG5vdCByZXF1aXJlZC4gVXNlZnVsIHdoZW4gdGhlXG4gKiB0YXJnZXQgZnVuY3Rpb24gaXMgYWxyZWFkeSBib3VuZC5cbiAqXG4gKiBVc2FnZTpcbiAqIHZhciBnID0gcGFydGlhbChmLCBhcmcxLCBhcmcyKTtcbiAqIGcoYXJnMywgYXJnNCk7XG4gKlxuICogQHBhcmFtIHtGdW5jdGlvbn0gZm4gQSBmdW5jdGlvbiB0byBwYXJ0aWFsbHkgYXBwbHkuXG4gKiBAcGFyYW0gey4uLip9IHZhcl9hcmdzIEFkZGl0aW9uYWwgYXJndW1lbnRzIHRoYXQgYXJlIHBhcnRpYWxseVxuICogICAgIGFwcGxpZWQgdG8gZm4uXG4gKiBAcmV0dXJuIHshRnVuY3Rpb259IEEgcGFydGlhbGx5LWFwcGxpZWQgZm9ybSBvZiB0aGUgZnVuY3Rpb24gYmluZCgpIHdhc1xuICogICAgIGludm9rZWQgYXMgYSBtZXRob2Qgb2YuXG4gKi9cbmdvb2cucGFydGlhbCA9IGZ1bmN0aW9uKGZuLCB2YXJfYXJncykge1xuICB2YXIgYXJncyA9IEFycmF5LnByb3RvdHlwZS5zbGljZS5jYWxsKGFyZ3VtZW50cywgMSk7XG4gIHJldHVybiBmdW5jdGlvbigpIHtcbiAgICAvLyBQcmVwZW5kIHRoZSBib3VuZCBhcmd1bWVudHMgdG8gdGhlIGN1cnJlbnQgYXJndW1lbnRzLlxuICAgIHZhciBuZXdBcmdzID0gQXJyYXkucHJvdG90eXBlLnNsaWNlLmNhbGwoYXJndW1lbnRzKTtcbiAgICBuZXdBcmdzLnVuc2hpZnQuYXBwbHkobmV3QXJncywgYXJncyk7XG4gICAgcmV0dXJuIGZuLmFwcGx5KHRoaXMsIG5ld0FyZ3MpO1xuICB9O1xufTtcblxuXG4vKipcbiAqIENvcGllcyBhbGwgdGhlIG1lbWJlcnMgb2YgYSBzb3VyY2Ugb2JqZWN0IHRvIGEgdGFyZ2V0IG9iamVjdC4gVGhpcyBtZXRob2RcbiAqIGRvZXMgbm90IHdvcmsgb24gYWxsIGJyb3dzZXJzIGZvciBhbGwgb2JqZWN0cyB0aGF0IGNvbnRhaW4ga2V5cyBzdWNoIGFzXG4gKiB0b1N0cmluZyBvciBoYXNPd25Qcm9wZXJ0eS4gVXNlIGdvb2cub2JqZWN0LmV4dGVuZCBmb3IgdGhpcyBwdXJwb3NlLlxuICogQHBhcmFtIHtPYmplY3R9IHRhcmdldCBUYXJnZXQuXG4gKiBAcGFyYW0ge09iamVjdH0gc291cmNlIFNvdXJjZS5cbiAqL1xuZ29vZy5taXhpbiA9IGZ1bmN0aW9uKHRhcmdldCwgc291cmNlKSB7XG4gIGZvciAodmFyIHggaW4gc291cmNlKSB7XG4gICAgdGFyZ2V0W3hdID0gc291cmNlW3hdO1xuICB9XG5cbiAgLy8gRm9yIElFNyBvciBsb3dlciwgdGhlIGZvci1pbi1sb29wIGRvZXMgbm90IGNvbnRhaW4gYW55IHByb3BlcnRpZXMgdGhhdCBhcmVcbiAgLy8gbm90IGVudW1lcmFibGUgb24gdGhlIHByb3RvdHlwZSBvYmplY3QgKGZvciBleGFtcGxlLCBpc1Byb3RvdHlwZU9mIGZyb21cbiAgLy8gT2JqZWN0LnByb3RvdHlwZSkgYnV0IGFsc28gaXQgd2lsbCBub3QgaW5jbHVkZSAncmVwbGFjZScgb24gb2JqZWN0cyB0aGF0XG4gIC8vIGV4dGVuZCBTdHJpbmcgYW5kIGNoYW5nZSAncmVwbGFjZScgKG5vdCB0aGF0IGl0IGlzIGNvbW1vbiBmb3IgYW55b25lIHRvXG4gIC8vIGV4dGVuZCBhbnl0aGluZyBleGNlcHQgT2JqZWN0KS5cbn07XG5cblxuLyoqXG4gKiBAcmV0dXJuIHtudW1iZXJ9IEFuIGludGVnZXIgdmFsdWUgcmVwcmVzZW50aW5nIHRoZSBudW1iZXIgb2YgbWlsbGlzZWNvbmRzXG4gKiAgICAgYmV0d2VlbiBtaWRuaWdodCwgSmFudWFyeSAxLCAxOTcwIGFuZCB0aGUgY3VycmVudCB0aW1lLlxuICovXG5nb29nLm5vdyA9IERhdGUubm93IHx8IChmdW5jdGlvbigpIHtcbiAgLy8gVW5hcnkgcGx1cyBvcGVyYXRvciBjb252ZXJ0cyBpdHMgb3BlcmFuZCB0byBhIG51bWJlciB3aGljaCBpbiB0aGUgY2FzZSBvZlxuICAvLyBhIGRhdGUgaXMgZG9uZSBieSBjYWxsaW5nIGdldFRpbWUoKS5cbiAgcmV0dXJuICtuZXcgRGF0ZSgpO1xufSk7XG5cblxuLyoqXG4gKiBFdmFscyBqYXZhc2NyaXB0IGluIHRoZSBnbG9iYWwgc2NvcGUuICBJbiBJRSB0aGlzIHVzZXMgZXhlY1NjcmlwdCwgb3RoZXJcbiAqIGJyb3dzZXJzIHVzZSBnb29nLmdsb2JhbC5ldmFsLiBJZiBnb29nLmdsb2JhbC5ldmFsIGRvZXMgbm90IGV2YWx1YXRlIGluIHRoZVxuICogZ2xvYmFsIHNjb3BlIChmb3IgZXhhbXBsZSwgaW4gU2FmYXJpKSwgYXBwZW5kcyBhIHNjcmlwdCB0YWcgaW5zdGVhZC5cbiAqIFRocm93cyBhbiBleGNlcHRpb24gaWYgbmVpdGhlciBleGVjU2NyaXB0IG9yIGV2YWwgaXMgZGVmaW5lZC5cbiAqIEBwYXJhbSB7c3RyaW5nfSBzY3JpcHQgSmF2YVNjcmlwdCBzdHJpbmcuXG4gKi9cbmdvb2cuZ2xvYmFsRXZhbCA9IGZ1bmN0aW9uKHNjcmlwdCkge1xuICBpZiAoZ29vZy5nbG9iYWwuZXhlY1NjcmlwdCkge1xuICAgIGdvb2cuZ2xvYmFsLmV4ZWNTY3JpcHQoc2NyaXB0LCAnSmF2YVNjcmlwdCcpO1xuICB9IGVsc2UgaWYgKGdvb2cuZ2xvYmFsLmV2YWwpIHtcbiAgICAvLyBUZXN0IHRvIHNlZSBpZiBldmFsIHdvcmtzXG4gICAgaWYgKGdvb2cuZXZhbFdvcmtzRm9yR2xvYmFsc18gPT0gbnVsbCkge1xuICAgICAgZ29vZy5nbG9iYWwuZXZhbCgndmFyIF9ldF8gPSAxOycpO1xuICAgICAgaWYgKHR5cGVvZiBnb29nLmdsb2JhbFsnX2V0XyddICE9ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIGRlbGV0ZSBnb29nLmdsb2JhbFsnX2V0XyddO1xuICAgICAgICBnb29nLmV2YWxXb3Jrc0Zvckdsb2JhbHNfID0gdHJ1ZTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGdvb2cuZXZhbFdvcmtzRm9yR2xvYmFsc18gPSBmYWxzZTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoZ29vZy5ldmFsV29ya3NGb3JHbG9iYWxzXykge1xuICAgICAgZ29vZy5nbG9iYWwuZXZhbChzY3JpcHQpO1xuICAgIH0gZWxzZSB7XG4gICAgICB2YXIgZG9jID0gZ29vZy5nbG9iYWwuZG9jdW1lbnQ7XG4gICAgICB2YXIgc2NyaXB0RWx0ID0gZG9jLmNyZWF0ZUVsZW1lbnQoJ3NjcmlwdCcpO1xuICAgICAgc2NyaXB0RWx0LnR5cGUgPSAndGV4dC9qYXZhc2NyaXB0JztcbiAgICAgIHNjcmlwdEVsdC5kZWZlciA9IGZhbHNlO1xuICAgICAgLy8gTm90ZSh1c2VyKTogY2FuJ3QgdXNlIC5pbm5lckhUTUwgc2luY2UgXCJ0KCc8dGVzdD4nKVwiIHdpbGwgZmFpbCBhbmRcbiAgICAgIC8vIC50ZXh0IGRvZXNuJ3Qgd29yayBpbiBTYWZhcmkgMi4gIFRoZXJlZm9yZSB3ZSBhcHBlbmQgYSB0ZXh0IG5vZGUuXG4gICAgICBzY3JpcHRFbHQuYXBwZW5kQ2hpbGQoZG9jLmNyZWF0ZVRleHROb2RlKHNjcmlwdCkpO1xuICAgICAgZG9jLmJvZHkuYXBwZW5kQ2hpbGQoc2NyaXB0RWx0KTtcbiAgICAgIGRvYy5ib2R5LnJlbW92ZUNoaWxkKHNjcmlwdEVsdCk7XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIHRocm93IEVycm9yKCdnb29nLmdsb2JhbEV2YWwgbm90IGF2YWlsYWJsZScpO1xuICB9XG59O1xuXG5cbi8qKlxuICogSW5kaWNhdGVzIHdoZXRoZXIgb3Igbm90IHdlIGNhbiBjYWxsICdldmFsJyBkaXJlY3RseSB0byBldmFsIGNvZGUgaW4gdGhlXG4gKiBnbG9iYWwgc2NvcGUuIFNldCB0byBhIEJvb2xlYW4gYnkgdGhlIGZpcnN0IGNhbGwgdG8gZ29vZy5nbG9iYWxFdmFsICh3aGljaFxuICogZW1waXJpY2FsbHkgdGVzdHMgd2hldGhlciBldmFsIHdvcmtzIGZvciBnbG9iYWxzKS4gQHNlZSBnb29nLmdsb2JhbEV2YWxcbiAqIEB0eXBlIHs/Ym9vbGVhbn1cbiAqIEBwcml2YXRlXG4gKi9cbmdvb2cuZXZhbFdvcmtzRm9yR2xvYmFsc18gPSBudWxsO1xuXG5cbi8qKlxuICogT3B0aW9uYWwgbWFwIG9mIENTUyBjbGFzcyBuYW1lcyB0byBvYmZ1c2NhdGVkIG5hbWVzIHVzZWQgd2l0aFxuICogZ29vZy5nZXRDc3NOYW1lKCkuXG4gKiBAdHlwZSB7T2JqZWN0fHVuZGVmaW5lZH1cbiAqIEBwcml2YXRlXG4gKiBAc2VlIGdvb2cuc2V0Q3NzTmFtZU1hcHBpbmdcbiAqL1xuZ29vZy5jc3NOYW1lTWFwcGluZ187XG5cblxuLyoqXG4gKiBPcHRpb25hbCBvYmZ1c2NhdGlvbiBzdHlsZSBmb3IgQ1NTIGNsYXNzIG5hbWVzLiBTaG91bGQgYmUgc2V0IHRvIGVpdGhlclxuICogJ0JZX1dIT0xFJyBvciAnQllfUEFSVCcgaWYgZGVmaW5lZC5cbiAqIEB0eXBlIHtzdHJpbmd8dW5kZWZpbmVkfVxuICogQHByaXZhdGVcbiAqIEBzZWUgZ29vZy5zZXRDc3NOYW1lTWFwcGluZ1xuICovXG5nb29nLmNzc05hbWVNYXBwaW5nU3R5bGVfO1xuXG5cbi8qKlxuICogSGFuZGxlcyBzdHJpbmdzIHRoYXQgYXJlIGludGVuZGVkIHRvIGJlIHVzZWQgYXMgQ1NTIGNsYXNzIG5hbWVzLlxuICpcbiAqIFRoaXMgZnVuY3Rpb24gd29ya3MgaW4gdGFuZGVtIHdpdGggQHNlZSBnb29nLnNldENzc05hbWVNYXBwaW5nLlxuICpcbiAqIFdpdGhvdXQgYW55IG1hcHBpbmcgc2V0LCB0aGUgYXJndW1lbnRzIGFyZSBzaW1wbGUgam9pbmVkIHdpdGggYVxuICogaHlwaGVuIGFuZCBwYXNzZWQgdGhyb3VnaCB1bmFsdGVyZWQuXG4gKlxuICogV2hlbiB0aGVyZSBpcyBhIG1hcHBpbmcsIHRoZXJlIGFyZSB0d28gcG9zc2libGUgc3R5bGVzIGluIHdoaWNoXG4gKiB0aGVzZSBtYXBwaW5ncyBhcmUgdXNlZC4gSW4gdGhlIEJZX1BBUlQgc3R5bGUsIGVhY2ggcGFydCAoaS5lLiBpblxuICogYmV0d2VlbiBoeXBoZW5zKSBvZiB0aGUgcGFzc2VkIGluIGNzcyBuYW1lIGlzIHJld3JpdHRlbiBhY2NvcmRpbmdcbiAqIHRvIHRoZSBtYXAuIEluIHRoZSBCWV9XSE9MRSBzdHlsZSwgdGhlIGZ1bGwgY3NzIG5hbWUgaXMgbG9va2VkIHVwIGluXG4gKiB0aGUgbWFwIGRpcmVjdGx5LiBJZiBhIHJld3JpdGUgaXMgbm90IHNwZWNpZmllZCBieSB0aGUgbWFwLCB0aGVcbiAqIGNvbXBpbGVyIHdpbGwgb3V0cHV0IGEgd2FybmluZy5cbiAqXG4gKiBXaGVuIHRoZSBtYXBwaW5nIGlzIHBhc3NlZCB0byB0aGUgY29tcGlsZXIsIGl0IHdpbGwgcmVwbGFjZSBjYWxsc1xuICogdG8gZ29vZy5nZXRDc3NOYW1lIHdpdGggdGhlIHN0cmluZ3MgZnJvbSB0aGUgbWFwcGluZywgZS5nLlxuICogICAgIHZhciB4ID0gZ29vZy5nZXRDc3NOYW1lKCdmb28nKTtcbiAqICAgICB2YXIgeSA9IGdvb2cuZ2V0Q3NzTmFtZSh0aGlzLmJhc2VDbGFzcywgJ2FjdGl2ZScpO1xuICogIGJlY29tZXM6XG4gKiAgICAgdmFyIHg9ICdmb28nO1xuICogICAgIHZhciB5ID0gdGhpcy5iYXNlQ2xhc3MgKyAnLWFjdGl2ZSc7XG4gKlxuICogSWYgb25lIGFyZ3VtZW50IGlzIHBhc3NlZCBpdCB3aWxsIGJlIHByb2Nlc3NlZCwgaWYgdHdvIGFyZSBwYXNzZWRcbiAqIG9ubHkgdGhlIG1vZGlmaWVyIHdpbGwgYmUgcHJvY2Vzc2VkLCBhcyBpdCBpcyBhc3N1bWVkIHRoZSBmaXJzdFxuICogYXJndW1lbnQgd2FzIGdlbmVyYXRlZCBhcyBhIHJlc3VsdCBvZiBjYWxsaW5nIGdvb2cuZ2V0Q3NzTmFtZS5cbiAqXG4gKiBAcGFyYW0ge3N0cmluZ30gY2xhc3NOYW1lIFRoZSBjbGFzcyBuYW1lLlxuICogQHBhcmFtIHtzdHJpbmc9fSBvcHRfbW9kaWZpZXIgQSBtb2RpZmllciB0byBiZSBhcHBlbmRlZCB0byB0aGUgY2xhc3MgbmFtZS5cbiAqIEByZXR1cm4ge3N0cmluZ30gVGhlIGNsYXNzIG5hbWUgb3IgdGhlIGNvbmNhdGVuYXRpb24gb2YgdGhlIGNsYXNzIG5hbWUgYW5kXG4gKiAgICAgdGhlIG1vZGlmaWVyLlxuICovXG5nb29nLmdldENzc05hbWUgPSBmdW5jdGlvbihjbGFzc05hbWUsIG9wdF9tb2RpZmllcikge1xuICB2YXIgZ2V0TWFwcGluZyA9IGZ1bmN0aW9uKGNzc05hbWUpIHtcbiAgICByZXR1cm4gZ29vZy5jc3NOYW1lTWFwcGluZ19bY3NzTmFtZV0gfHwgY3NzTmFtZTtcbiAgfTtcblxuICB2YXIgcmVuYW1lQnlQYXJ0cyA9IGZ1bmN0aW9uKGNzc05hbWUpIHtcbiAgICAvLyBSZW1hcCBhbGwgdGhlIHBhcnRzIGluZGl2aWR1YWxseS5cbiAgICB2YXIgcGFydHMgPSBjc3NOYW1lLnNwbGl0KCctJyk7XG4gICAgdmFyIG1hcHBlZCA9IFtdO1xuICAgIGZvciAodmFyIGkgPSAwOyBpIDwgcGFydHMubGVuZ3RoOyBpKyspIHtcbiAgICAgIG1hcHBlZC5wdXNoKGdldE1hcHBpbmcocGFydHNbaV0pKTtcbiAgICB9XG4gICAgcmV0dXJuIG1hcHBlZC5qb2luKCctJyk7XG4gIH07XG5cbiAgdmFyIHJlbmFtZTtcbiAgaWYgKGdvb2cuY3NzTmFtZU1hcHBpbmdfKSB7XG4gICAgcmVuYW1lID0gZ29vZy5jc3NOYW1lTWFwcGluZ1N0eWxlXyA9PSAnQllfV0hPTEUnID9cbiAgICAgICAgZ2V0TWFwcGluZyA6IHJlbmFtZUJ5UGFydHM7XG4gIH0gZWxzZSB7XG4gICAgcmVuYW1lID0gZnVuY3Rpb24oYSkge1xuICAgICAgcmV0dXJuIGE7XG4gICAgfTtcbiAgfVxuXG4gIGlmIChvcHRfbW9kaWZpZXIpIHtcbiAgICByZXR1cm4gY2xhc3NOYW1lICsgJy0nICsgcmVuYW1lKG9wdF9tb2RpZmllcik7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIHJlbmFtZShjbGFzc05hbWUpO1xuICB9XG59O1xuXG5cbi8qKlxuICogU2V0cyB0aGUgbWFwIHRvIGNoZWNrIHdoZW4gcmV0dXJuaW5nIGEgdmFsdWUgZnJvbSBnb29nLmdldENzc05hbWUoKS4gRXhhbXBsZTpcbiAqIDxwcmU+XG4gKiBnb29nLnNldENzc05hbWVNYXBwaW5nKHtcbiAqICAgXCJnb29nXCI6IFwiYVwiLFxuICogICBcImRpc2FibGVkXCI6IFwiYlwiLFxuICogfSk7XG4gKlxuICogdmFyIHggPSBnb29nLmdldENzc05hbWUoJ2dvb2cnKTtcbiAqIC8vIFRoZSBmb2xsb3dpbmcgZXZhbHVhdGVzIHRvOiBcImEgYS1iXCIuXG4gKiBnb29nLmdldENzc05hbWUoJ2dvb2cnKSArICcgJyArIGdvb2cuZ2V0Q3NzTmFtZSh4LCAnZGlzYWJsZWQnKVxuICogPC9wcmU+XG4gKiBXaGVuIGRlY2xhcmVkIGFzIGEgbWFwIG9mIHN0cmluZyBsaXRlcmFscyB0byBzdHJpbmcgbGl0ZXJhbHMsIHRoZSBKU0NvbXBpbGVyXG4gKiB3aWxsIHJlcGxhY2UgYWxsIGNhbGxzIHRvIGdvb2cuZ2V0Q3NzTmFtZSgpIHVzaW5nIHRoZSBzdXBwbGllZCBtYXAgaWYgdGhlXG4gKiAtLWNsb3N1cmVfcGFzcyBmbGFnIGlzIHNldC5cbiAqXG4gKiBAcGFyYW0geyFPYmplY3R9IG1hcHBpbmcgQSBtYXAgb2Ygc3RyaW5ncyB0byBzdHJpbmdzIHdoZXJlIGtleXMgYXJlIHBvc3NpYmxlXG4gKiAgICAgYXJndW1lbnRzIHRvIGdvb2cuZ2V0Q3NzTmFtZSgpIGFuZCB2YWx1ZXMgYXJlIHRoZSBjb3JyZXNwb25kaW5nIHZhbHVlc1xuICogICAgIHRoYXQgc2hvdWxkIGJlIHJldHVybmVkLlxuICogQHBhcmFtIHtzdHJpbmc9fSBvcHRfc3R5bGUgVGhlIHN0eWxlIG9mIGNzcyBuYW1lIG1hcHBpbmcuIFRoZXJlIGFyZSB0d28gdmFsaWRcbiAqICAgICBvcHRpb25zOiAnQllfUEFSVCcsIGFuZCAnQllfV0hPTEUnLlxuICogQHNlZSBnb29nLmdldENzc05hbWUgZm9yIGEgZGVzY3JpcHRpb24uXG4gKi9cbmdvb2cuc2V0Q3NzTmFtZU1hcHBpbmcgPSBmdW5jdGlvbihtYXBwaW5nLCBvcHRfc3R5bGUpIHtcbiAgZ29vZy5jc3NOYW1lTWFwcGluZ18gPSBtYXBwaW5nO1xuICBnb29nLmNzc05hbWVNYXBwaW5nU3R5bGVfID0gb3B0X3N0eWxlO1xufTtcblxuXG4vKipcbiAqIFRvIHVzZSBDU1MgcmVuYW1pbmcgaW4gY29tcGlsZWQgbW9kZSwgb25lIG9mIHRoZSBpbnB1dCBmaWxlcyBzaG91bGQgaGF2ZSBhXG4gKiBjYWxsIHRvIGdvb2cuc2V0Q3NzTmFtZU1hcHBpbmcoKSB3aXRoIGFuIG9iamVjdCBsaXRlcmFsIHRoYXQgdGhlIEpTQ29tcGlsZXJcbiAqIGNhbiBleHRyYWN0IGFuZCB1c2UgdG8gcmVwbGFjZSBhbGwgY2FsbHMgdG8gZ29vZy5nZXRDc3NOYW1lKCkuIEluIHVuY29tcGlsZWRcbiAqIG1vZGUsIEphdmFTY3JpcHQgY29kZSBzaG91bGQgYmUgbG9hZGVkIGJlZm9yZSB0aGlzIGJhc2UuanMgZmlsZSB0aGF0IGRlY2xhcmVzXG4gKiBhIGdsb2JhbCB2YXJpYWJsZSwgQ0xPU1VSRV9DU1NfTkFNRV9NQVBQSU5HLCB3aGljaCBpcyB1c2VkIGJlbG93LiBUaGlzIGlzXG4gKiB0byBlbnN1cmUgdGhhdCB0aGUgbWFwcGluZyBpcyBsb2FkZWQgYmVmb3JlIGFueSBjYWxscyB0byBnb29nLmdldENzc05hbWUoKVxuICogYXJlIG1hZGUgaW4gdW5jb21waWxlZCBtb2RlLlxuICpcbiAqIEEgaG9vayBmb3Igb3ZlcnJpZGluZyB0aGUgQ1NTIG5hbWUgbWFwcGluZy5cbiAqIEB0eXBlIHtPYmplY3R8dW5kZWZpbmVkfVxuICovXG5nb29nLmdsb2JhbC5DTE9TVVJFX0NTU19OQU1FX01BUFBJTkc7XG5cblxuaWYgKCFDT01QSUxFRCAmJiBnb29nLmdsb2JhbC5DTE9TVVJFX0NTU19OQU1FX01BUFBJTkcpIHtcbiAgLy8gVGhpcyBkb2VzIG5vdCBjYWxsIGdvb2cuc2V0Q3NzTmFtZU1hcHBpbmcoKSBiZWNhdXNlIHRoZSBKU0NvbXBpbGVyXG4gIC8vIHJlcXVpcmVzIHRoYXQgZ29vZy5zZXRDc3NOYW1lTWFwcGluZygpIGJlIGNhbGxlZCB3aXRoIGFuIG9iamVjdCBsaXRlcmFsLlxuICBnb29nLmNzc05hbWVNYXBwaW5nXyA9IGdvb2cuZ2xvYmFsLkNMT1NVUkVfQ1NTX05BTUVfTUFQUElORztcbn1cblxuXG4vKipcbiAqIEFic3RyYWN0IGltcGxlbWVudGF0aW9uIG9mIGdvb2cuZ2V0TXNnIGZvciB1c2Ugd2l0aCBsb2NhbGl6ZWQgbWVzc2FnZXMuXG4gKiBAcGFyYW0ge3N0cmluZ30gc3RyIFRyYW5zbGF0YWJsZSBzdHJpbmcsIHBsYWNlcyBob2xkZXJzIGluIHRoZSBmb3JtIHskZm9vfS5cbiAqIEBwYXJhbSB7T2JqZWN0PX0gb3B0X3ZhbHVlcyBNYXAgb2YgcGxhY2UgaG9sZGVyIG5hbWUgdG8gdmFsdWUuXG4gKiBAcmV0dXJuIHtzdHJpbmd9IG1lc3NhZ2Ugd2l0aCBwbGFjZWhvbGRlcnMgZmlsbGVkLlxuICovXG5nb29nLmdldE1zZyA9IGZ1bmN0aW9uKHN0ciwgb3B0X3ZhbHVlcykge1xuICB2YXIgdmFsdWVzID0gb3B0X3ZhbHVlcyB8fCB7fTtcbiAgZm9yICh2YXIga2V5IGluIHZhbHVlcykge1xuICAgIHZhciB2YWx1ZSA9ICgnJyArIHZhbHVlc1trZXldKS5yZXBsYWNlKC9cXCQvZywgJyQkJCQnKTtcbiAgICBzdHIgPSBzdHIucmVwbGFjZShuZXcgUmVnRXhwKCdcXFxce1xcXFwkJyArIGtleSArICdcXFxcfScsICdnaScpLCB2YWx1ZSk7XG4gIH1cbiAgcmV0dXJuIHN0cjtcbn07XG5cblxuLyoqXG4gKiBFeHBvc2VzIGFuIHVub2JmdXNjYXRlZCBnbG9iYWwgbmFtZXNwYWNlIHBhdGggZm9yIHRoZSBnaXZlbiBvYmplY3QuXG4gKiBOb3RlIHRoYXQgZmllbGRzIG9mIHRoZSBleHBvcnRlZCBvYmplY3QgKndpbGwqIGJlIG9iZnVzY2F0ZWQsXG4gKiB1bmxlc3MgdGhleSBhcmUgZXhwb3J0ZWQgaW4gdHVybiB2aWEgdGhpcyBmdW5jdGlvbiBvclxuICogZ29vZy5leHBvcnRQcm9wZXJ0eVxuICpcbiAqIDxwPkFsc28gaGFuZHkgZm9yIG1ha2luZyBwdWJsaWMgaXRlbXMgdGhhdCBhcmUgZGVmaW5lZCBpbiBhbm9ueW1vdXNcbiAqIGNsb3N1cmVzLlxuICpcbiAqIGV4LiBnb29nLmV4cG9ydFN5bWJvbCgncHVibGljLnBhdGguRm9vJywgRm9vKTtcbiAqXG4gKiBleC4gZ29vZy5leHBvcnRTeW1ib2woJ3B1YmxpYy5wYXRoLkZvby5zdGF0aWNGdW5jdGlvbicsXG4gKiAgICAgICAgICAgICAgICAgICAgICAgRm9vLnN0YXRpY0Z1bmN0aW9uKTtcbiAqICAgICBwdWJsaWMucGF0aC5Gb28uc3RhdGljRnVuY3Rpb24oKTtcbiAqXG4gKiBleC4gZ29vZy5leHBvcnRTeW1ib2woJ3B1YmxpYy5wYXRoLkZvby5wcm90b3R5cGUubXlNZXRob2QnLFxuICogICAgICAgICAgICAgICAgICAgICAgIEZvby5wcm90b3R5cGUubXlNZXRob2QpO1xuICogICAgIG5ldyBwdWJsaWMucGF0aC5Gb28oKS5teU1ldGhvZCgpO1xuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBwdWJsaWNQYXRoIFVub2JmdXNjYXRlZCBuYW1lIHRvIGV4cG9ydC5cbiAqIEBwYXJhbSB7Kn0gb2JqZWN0IE9iamVjdCB0aGUgbmFtZSBzaG91bGQgcG9pbnQgdG8uXG4gKiBAcGFyYW0ge09iamVjdD19IG9wdF9vYmplY3RUb0V4cG9ydFRvIFRoZSBvYmplY3QgdG8gYWRkIHRoZSBwYXRoIHRvOyBkZWZhdWx0XG4gKiAgICAgaXMgfGdvb2cuZ2xvYmFsfC5cbiAqL1xuZ29vZy5leHBvcnRTeW1ib2wgPSBmdW5jdGlvbihwdWJsaWNQYXRoLCBvYmplY3QsIG9wdF9vYmplY3RUb0V4cG9ydFRvKSB7XG4gIGdvb2cuZXhwb3J0UGF0aF8ocHVibGljUGF0aCwgb2JqZWN0LCBvcHRfb2JqZWN0VG9FeHBvcnRUbyk7XG59O1xuXG5cbi8qKlxuICogRXhwb3J0cyBhIHByb3BlcnR5IHVub2JmdXNjYXRlZCBpbnRvIHRoZSBvYmplY3QncyBuYW1lc3BhY2UuXG4gKiBleC4gZ29vZy5leHBvcnRQcm9wZXJ0eShGb28sICdzdGF0aWNGdW5jdGlvbicsIEZvby5zdGF0aWNGdW5jdGlvbik7XG4gKiBleC4gZ29vZy5leHBvcnRQcm9wZXJ0eShGb28ucHJvdG90eXBlLCAnbXlNZXRob2QnLCBGb28ucHJvdG90eXBlLm15TWV0aG9kKTtcbiAqIEBwYXJhbSB7T2JqZWN0fSBvYmplY3QgT2JqZWN0IHdob3NlIHN0YXRpYyBwcm9wZXJ0eSBpcyBiZWluZyBleHBvcnRlZC5cbiAqIEBwYXJhbSB7c3RyaW5nfSBwdWJsaWNOYW1lIFVub2JmdXNjYXRlZCBuYW1lIHRvIGV4cG9ydC5cbiAqIEBwYXJhbSB7Kn0gc3ltYm9sIE9iamVjdCB0aGUgbmFtZSBzaG91bGQgcG9pbnQgdG8uXG4gKi9cbmdvb2cuZXhwb3J0UHJvcGVydHkgPSBmdW5jdGlvbihvYmplY3QsIHB1YmxpY05hbWUsIHN5bWJvbCkge1xuICBvYmplY3RbcHVibGljTmFtZV0gPSBzeW1ib2w7XG59O1xuXG5cbi8qKlxuICogSW5oZXJpdCB0aGUgcHJvdG90eXBlIG1ldGhvZHMgZnJvbSBvbmUgY29uc3RydWN0b3IgaW50byBhbm90aGVyLlxuICpcbiAqIFVzYWdlOlxuICogPHByZT5cbiAqIGZ1bmN0aW9uIFBhcmVudENsYXNzKGEsIGIpIHsgfVxuICogUGFyZW50Q2xhc3MucHJvdG90eXBlLmZvbyA9IGZ1bmN0aW9uKGEpIHsgfVxuICpcbiAqIGZ1bmN0aW9uIENoaWxkQ2xhc3MoYSwgYiwgYykge1xuICogICBnb29nLmJhc2UodGhpcywgYSwgYik7XG4gKiB9XG4gKiBnb29nLmluaGVyaXRzKENoaWxkQ2xhc3MsIFBhcmVudENsYXNzKTtcbiAqXG4gKiB2YXIgY2hpbGQgPSBuZXcgQ2hpbGRDbGFzcygnYScsICdiJywgJ3NlZScpO1xuICogY2hpbGQuZm9vKCk7IC8vIHdvcmtzXG4gKiA8L3ByZT5cbiAqXG4gKiBJbiBhZGRpdGlvbiwgYSBzdXBlcmNsYXNzJyBpbXBsZW1lbnRhdGlvbiBvZiBhIG1ldGhvZCBjYW4gYmUgaW52b2tlZFxuICogYXMgZm9sbG93czpcbiAqXG4gKiA8cHJlPlxuICogQ2hpbGRDbGFzcy5wcm90b3R5cGUuZm9vID0gZnVuY3Rpb24oYSkge1xuICogICBDaGlsZENsYXNzLnN1cGVyQ2xhc3NfLmZvby5jYWxsKHRoaXMsIGEpO1xuICogICAvLyBvdGhlciBjb2RlXG4gKiB9O1xuICogPC9wcmU+XG4gKlxuICogQHBhcmFtIHtGdW5jdGlvbn0gY2hpbGRDdG9yIENoaWxkIGNsYXNzLlxuICogQHBhcmFtIHtGdW5jdGlvbn0gcGFyZW50Q3RvciBQYXJlbnQgY2xhc3MuXG4gKi9cbmdvb2cuaW5oZXJpdHMgPSBmdW5jdGlvbihjaGlsZEN0b3IsIHBhcmVudEN0b3IpIHtcbiAgLyoqIEBjb25zdHJ1Y3RvciAqL1xuICBmdW5jdGlvbiB0ZW1wQ3RvcigpIHt9O1xuICB0ZW1wQ3Rvci5wcm90b3R5cGUgPSBwYXJlbnRDdG9yLnByb3RvdHlwZTtcbiAgY2hpbGRDdG9yLnN1cGVyQ2xhc3NfID0gcGFyZW50Q3Rvci5wcm90b3R5cGU7XG4gIGNoaWxkQ3Rvci5wcm90b3R5cGUgPSBuZXcgdGVtcEN0b3IoKTtcbiAgY2hpbGRDdG9yLnByb3RvdHlwZS5jb25zdHJ1Y3RvciA9IGNoaWxkQ3Rvcjtcbn07XG5cblxuLyoqXG4gKiBDYWxsIHVwIHRvIHRoZSBzdXBlcmNsYXNzLlxuICpcbiAqIElmIHRoaXMgaXMgY2FsbGVkIGZyb20gYSBjb25zdHJ1Y3RvciwgdGhlbiB0aGlzIGNhbGxzIHRoZSBzdXBlcmNsYXNzXG4gKiBjb250cnVjdG9yIHdpdGggYXJndW1lbnRzIDEtTi5cbiAqXG4gKiBJZiB0aGlzIGlzIGNhbGxlZCBmcm9tIGEgcHJvdG90eXBlIG1ldGhvZCwgdGhlbiB5b3UgbXVzdCBwYXNzXG4gKiB0aGUgbmFtZSBvZiB0aGUgbWV0aG9kIGFzIHRoZSBzZWNvbmQgYXJndW1lbnQgdG8gdGhpcyBmdW5jdGlvbi4gSWZcbiAqIHlvdSBkbyBub3QsIHlvdSB3aWxsIGdldCBhIHJ1bnRpbWUgZXJyb3IuIFRoaXMgY2FsbHMgdGhlIHN1cGVyY2xhc3MnXG4gKiBtZXRob2Qgd2l0aCBhcmd1bWVudHMgMi1OLlxuICpcbiAqIFRoaXMgZnVuY3Rpb24gb25seSB3b3JrcyBpZiB5b3UgdXNlIGdvb2cuaW5oZXJpdHMgdG8gZXhwcmVzc1xuICogaW5oZXJpdGFuY2UgcmVsYXRpb25zaGlwcyBiZXR3ZWVuIHlvdXIgY2xhc3Nlcy5cbiAqXG4gKiBUaGlzIGZ1bmN0aW9uIGlzIGEgY29tcGlsZXIgcHJpbWl0aXZlLiBBdCBjb21waWxlLXRpbWUsIHRoZVxuICogY29tcGlsZXIgd2lsbCBkbyBtYWNybyBleHBhbnNpb24gdG8gcmVtb3ZlIGEgbG90IG9mXG4gKiB0aGUgZXh0cmEgb3ZlcmhlYWQgdGhhdCB0aGlzIGZ1bmN0aW9uIGludHJvZHVjZXMuIFRoZSBjb21waWxlclxuICogd2lsbCBhbHNvIGVuZm9yY2UgYSBsb3Qgb2YgdGhlIGFzc3VtcHRpb25zIHRoYXQgdGhpcyBmdW5jdGlvblxuICogbWFrZXMsIGFuZCB0cmVhdCBpdCBhcyBhIGNvbXBpbGVyIGVycm9yIGlmIHlvdSBicmVhayB0aGVtLlxuICpcbiAqIEBwYXJhbSB7IU9iamVjdH0gbWUgU2hvdWxkIGFsd2F5cyBiZSBcInRoaXNcIi5cbiAqIEBwYXJhbSB7Kj19IG9wdF9tZXRob2ROYW1lIFRoZSBtZXRob2QgbmFtZSBpZiBjYWxsaW5nIGEgc3VwZXIgbWV0aG9kLlxuICogQHBhcmFtIHsuLi4qfSB2YXJfYXJncyBUaGUgcmVzdCBvZiB0aGUgYXJndW1lbnRzLlxuICogQHJldHVybiB7Kn0gVGhlIHJldHVybiB2YWx1ZSBvZiB0aGUgc3VwZXJjbGFzcyBtZXRob2QuXG4gKi9cbmdvb2cuYmFzZSA9IGZ1bmN0aW9uKG1lLCBvcHRfbWV0aG9kTmFtZSwgdmFyX2FyZ3MpIHtcbiAgdmFyIGNhbGxlciA9IGFyZ3VtZW50cy5jYWxsZWUuY2FsbGVyO1xuICBpZiAoY2FsbGVyLnN1cGVyQ2xhc3NfKSB7XG4gICAgLy8gVGhpcyBpcyBhIGNvbnN0cnVjdG9yLiBDYWxsIHRoZSBzdXBlcmNsYXNzIGNvbnN0cnVjdG9yLlxuICAgIHJldHVybiBjYWxsZXIuc3VwZXJDbGFzc18uY29uc3RydWN0b3IuYXBwbHkoXG4gICAgICAgIG1lLCBBcnJheS5wcm90b3R5cGUuc2xpY2UuY2FsbChhcmd1bWVudHMsIDEpKTtcbiAgfVxuXG4gIHZhciBhcmdzID0gQXJyYXkucHJvdG90eXBlLnNsaWNlLmNhbGwoYXJndW1lbnRzLCAyKTtcbiAgdmFyIGZvdW5kQ2FsbGVyID0gZmFsc2U7XG4gIGZvciAodmFyIGN0b3IgPSBtZS5jb25zdHJ1Y3RvcjtcbiAgICAgICBjdG9yOyBjdG9yID0gY3Rvci5zdXBlckNsYXNzXyAmJiBjdG9yLnN1cGVyQ2xhc3NfLmNvbnN0cnVjdG9yKSB7XG4gICAgaWYgKGN0b3IucHJvdG90eXBlW29wdF9tZXRob2ROYW1lXSA9PT0gY2FsbGVyKSB7XG4gICAgICBmb3VuZENhbGxlciA9IHRydWU7XG4gICAgfSBlbHNlIGlmIChmb3VuZENhbGxlcikge1xuICAgICAgcmV0dXJuIGN0b3IucHJvdG90eXBlW29wdF9tZXRob2ROYW1lXS5hcHBseShtZSwgYXJncyk7XG4gICAgfVxuICB9XG5cbiAgLy8gSWYgd2UgZGlkIG5vdCBmaW5kIHRoZSBjYWxsZXIgaW4gdGhlIHByb3RvdHlwZSBjaGFpbixcbiAgLy8gdGhlbiBvbmUgb2YgdHdvIHRoaW5ncyBoYXBwZW5lZDpcbiAgLy8gMSkgVGhlIGNhbGxlciBpcyBhbiBpbnN0YW5jZSBtZXRob2QuXG4gIC8vIDIpIFRoaXMgbWV0aG9kIHdhcyBub3QgY2FsbGVkIGJ5IHRoZSByaWdodCBjYWxsZXIuXG4gIGlmIChtZVtvcHRfbWV0aG9kTmFtZV0gPT09IGNhbGxlcikge1xuICAgIHJldHVybiBtZS5jb25zdHJ1Y3Rvci5wcm90b3R5cGVbb3B0X21ldGhvZE5hbWVdLmFwcGx5KG1lLCBhcmdzKTtcbiAgfSBlbHNlIHtcbiAgICB0aHJvdyBFcnJvcihcbiAgICAgICAgJ2dvb2cuYmFzZSBjYWxsZWQgZnJvbSBhIG1ldGhvZCBvZiBvbmUgbmFtZSAnICtcbiAgICAgICAgJ3RvIGEgbWV0aG9kIG9mIGEgZGlmZmVyZW50IG5hbWUnKTtcbiAgfVxufTtcblxuXG4vKipcbiAqIEFsbG93IGZvciBhbGlhc2luZyB3aXRoaW4gc2NvcGUgZnVuY3Rpb25zLiAgVGhpcyBmdW5jdGlvbiBleGlzdHMgZm9yXG4gKiB1bmNvbXBpbGVkIGNvZGUgLSBpbiBjb21waWxlZCBjb2RlIHRoZSBjYWxscyB3aWxsIGJlIGlubGluZWQgYW5kIHRoZVxuICogYWxpYXNlcyBhcHBsaWVkLiAgSW4gdW5jb21waWxlZCBjb2RlIHRoZSBmdW5jdGlvbiBpcyBzaW1wbHkgcnVuIHNpbmNlIHRoZVxuICogYWxpYXNlcyBhcyB3cml0dGVuIGFyZSB2YWxpZCBKYXZhU2NyaXB0LlxuICogQHBhcmFtIHtmdW5jdGlvbigpfSBmbiBGdW5jdGlvbiB0byBjYWxsLiAgVGhpcyBmdW5jdGlvbiBjYW4gY29udGFpbiBhbGlhc2VzXG4gKiAgICAgdG8gbmFtZXNwYWNlcyAoZS5nLiBcInZhciBkb20gPSBnb29nLmRvbVwiKSBvciBjbGFzc2VzXG4gKiAgICAoZS5nLiBcInZhciBUaW1lciA9IGdvb2cuVGltZXJcIikuXG4gKi9cbmdvb2cuc2NvcGUgPSBmdW5jdGlvbihmbikge1xuICBmbi5jYWxsKGdvb2cuZ2xvYmFsKTtcbn07XG5cblxuIiwiLyoqXG4gKiBkZWZpbmVzXG4gKi9cblxuZ29vZy5wcm92aWRlKCdVU0VfVFlQRURBUlJBWScpO1xuXG4vLyBTYWZhcmkg44GMIHR5cGVvZiBVaW50OEFycmF5ID09PSAnb2JqZWN0JyDjgavjgarjgovjgZ/jgoHjgIFcbi8vIOacquWumue+qeOBi+WQpuOBi+OBpyBUeXBlZCBBcnJheSDjga7kvb/nlKjjgpLmsbrlrprjgZnjgotcblxuLyoqIEBjb25zdCB7Ym9vbGVhbn0gdXNlIHR5cGVkIGFycmF5IGZsYWcuICovXG52YXIgVVNFX1RZUEVEQVJSQVkgPVxuICAodHlwZW9mIFVpbnQ4QXJyYXkgIT09ICd1bmRlZmluZWQnKSAmJlxuICAodHlwZW9mIFVpbnQxNkFycmF5ICE9PSAndW5kZWZpbmVkJykgJiZcbiAgKHR5cGVvZiBVaW50MzJBcnJheSAhPT0gJ3VuZGVmaW5lZCcpICYmXG4gICh0eXBlb2YgRGF0YVZpZXcgIT09ICd1bmRlZmluZWQnKTtcbiIsIi8qKlxuICogQGZpbGVvdmVydmlldyBiaXQg5Y2Y5L2N44Gn44Gu5pu444GN6L6844G/5a6f6KOFLlxuICovXG5nb29nLnByb3ZpZGUoJ1psaWIuQml0U3RyZWFtJyk7XG5cbmdvb2cucmVxdWlyZSgnVVNFX1RZUEVEQVJSQVknKTtcblxuZ29vZy5zY29wZShmdW5jdGlvbigpIHtcblxuLyoqXG4gKiDjg5Pjg4Pjg4jjgrnjg4jjg6rjg7zjg6BcbiAqIEBjb25zdHJ1Y3RvclxuICogQHBhcmFtIHshKEFycmF5fFVpbnQ4QXJyYXkpPX0gYnVmZmVyIG91dHB1dCBidWZmZXIuXG4gKiBAcGFyYW0ge251bWJlcj19IGJ1ZmZlclBvc2l0aW9uIHN0YXJ0IGJ1ZmZlciBwb2ludGVyLlxuICovXG5abGliLkJpdFN0cmVhbSA9IGZ1bmN0aW9uKGJ1ZmZlciwgYnVmZmVyUG9zaXRpb24pIHtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9IGJ1ZmZlciBpbmRleC4gKi9cbiAgdGhpcy5pbmRleCA9IHR5cGVvZiBidWZmZXJQb3NpdGlvbiA9PT0gJ251bWJlcicgPyBidWZmZXJQb3NpdGlvbiA6IDA7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSBiaXQgaW5kZXguICovXG4gIHRoaXMuYml0aW5kZXggPSAwO1xuICAvKiogQHR5cGUgeyEoQXJyYXl8VWludDhBcnJheSl9IGJpdC1zdHJlYW0gb3V0cHV0IGJ1ZmZlci4gKi9cbiAgdGhpcy5idWZmZXIgPSBidWZmZXIgaW5zdGFuY2VvZiAoVVNFX1RZUEVEQVJSQVkgPyBVaW50OEFycmF5IDogQXJyYXkpID9cbiAgICBidWZmZXIgOlxuICAgIG5ldyAoVVNFX1RZUEVEQVJSQVkgPyBVaW50OEFycmF5IDogQXJyYXkpKFpsaWIuQml0U3RyZWFtLkRlZmF1bHRCbG9ja1NpemUpO1xuXG4gIC8vIOWFpeWKm+OBleOCjOOBnyBpbmRleCDjgYzotrPjgorjgarjgYvjgaPjgZ/jgonmi6HlvLXjgZnjgovjgYzjgIHlgI3jgavjgZfjgabjgoLjg4Djg6HjgarjgonkuI3mraPjgajjgZnjgotcbiAgaWYgKHRoaXMuYnVmZmVyLmxlbmd0aCAqIDIgPD0gdGhpcy5pbmRleCkge1xuICAgIHRocm93IG5ldyBFcnJvcihcImludmFsaWQgaW5kZXhcIik7XG4gIH0gZWxzZSBpZiAodGhpcy5idWZmZXIubGVuZ3RoIDw9IHRoaXMuaW5kZXgpIHtcbiAgICB0aGlzLmV4cGFuZEJ1ZmZlcigpO1xuICB9XG59O1xuXG4vKipcbiAqIOODh+ODleOCqeODq+ODiOODluODreODg+OCr+OCteOCpOOCui5cbiAqIEBjb25zdFxuICogQHR5cGUge251bWJlcn1cbiAqL1xuWmxpYi5CaXRTdHJlYW0uRGVmYXVsdEJsb2NrU2l6ZSA9IDB4ODAwMDtcblxuLyoqXG4gKiBleHBhbmQgYnVmZmVyLlxuICogQHJldHVybiB7IShBcnJheXxVaW50OEFycmF5KX0gbmV3IGJ1ZmZlci5cbiAqL1xuWmxpYi5CaXRTdHJlYW0ucHJvdG90eXBlLmV4cGFuZEJ1ZmZlciA9IGZ1bmN0aW9uKCkge1xuICAvKiogQHR5cGUgeyEoQXJyYXl8VWludDhBcnJheSl9IG9sZCBidWZmZXIuICovXG4gIHZhciBvbGRidWYgPSB0aGlzLmJ1ZmZlcjtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9IGxvb3AgY291bnRlci4gKi9cbiAgdmFyIGk7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSBsb29wIGxpbWl0ZXIuICovXG4gIHZhciBpbCA9IG9sZGJ1Zi5sZW5ndGg7XG4gIC8qKiBAdHlwZSB7IShBcnJheXxVaW50OEFycmF5KX0gbmV3IGJ1ZmZlci4gKi9cbiAgdmFyIGJ1ZmZlciA9XG4gICAgbmV3IChVU0VfVFlQRURBUlJBWSA/IFVpbnQ4QXJyYXkgOiBBcnJheSkoaWwgPDwgMSk7XG5cbiAgLy8gY29weSBidWZmZXJcbiAgaWYgKFVTRV9UWVBFREFSUkFZKSB7XG4gICAgYnVmZmVyLnNldChvbGRidWYpO1xuICB9IGVsc2Uge1xuICAgIC8vIFhYWDogbG9vcCB1bnJvbGxpbmdcbiAgICBmb3IgKGkgPSAwOyBpIDwgaWw7ICsraSkge1xuICAgICAgYnVmZmVyW2ldID0gb2xkYnVmW2ldO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiAodGhpcy5idWZmZXIgPSBidWZmZXIpO1xufTtcblxuXG4vKipcbiAqIOaVsOWApOOCkuODk+ODg+ODiOOBp+aMh+WumuOBl+OBn+aVsOOBoOOBkeabuOOBjei+vOOCgC5cbiAqIEBwYXJhbSB7bnVtYmVyfSBudW1iZXIg5pu444GN6L6844KA5pWw5YCkLlxuICogQHBhcmFtIHtudW1iZXJ9IG4g5pu444GN6L6844KA44OT44OD44OI5pWwLlxuICogQHBhcmFtIHtib29sZWFuPX0gcmV2ZXJzZSDpgIbpoIbjgavmm7jjgY3ovrzjgoDjgarjgonjgbAgdHJ1ZS5cbiAqL1xuWmxpYi5CaXRTdHJlYW0ucHJvdG90eXBlLndyaXRlQml0cyA9IGZ1bmN0aW9uKG51bWJlciwgbiwgcmV2ZXJzZSkge1xuICB2YXIgYnVmZmVyID0gdGhpcy5idWZmZXI7XG4gIHZhciBpbmRleCA9IHRoaXMuaW5kZXg7XG4gIHZhciBiaXRpbmRleCA9IHRoaXMuYml0aW5kZXg7XG5cbiAgLyoqIEB0eXBlIHtudW1iZXJ9IGN1cnJlbnQgb2N0ZXQuICovXG4gIHZhciBjdXJyZW50ID0gYnVmZmVyW2luZGV4XTtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9IGxvb3AgY291bnRlci4gKi9cbiAgdmFyIGk7XG5cbiAgLyoqXG4gICAqIDMyLWJpdCDmlbTmlbDjga7jg5Pjg4Pjg4jpoIbjgpLpgIbjgavjgZnjgotcbiAgICogQHBhcmFtIHtudW1iZXJ9IG4gMzItYml0IGludGVnZXIuXG4gICAqIEByZXR1cm4ge251bWJlcn0gcmV2ZXJzZWQgMzItYml0IGludGVnZXIuXG4gICAqIEBwcml2YXRlXG4gICAqL1xuICBmdW5jdGlvbiByZXYzMl8obikge1xuICAgIHJldHVybiAoWmxpYi5CaXRTdHJlYW0uUmV2ZXJzZVRhYmxlW24gJiAweEZGXSA8PCAyNCkgfFxuICAgICAgKFpsaWIuQml0U3RyZWFtLlJldmVyc2VUYWJsZVtuID4+PiA4ICYgMHhGRl0gPDwgMTYpIHxcbiAgICAgIChabGliLkJpdFN0cmVhbS5SZXZlcnNlVGFibGVbbiA+Pj4gMTYgJiAweEZGXSA8PCA4KSB8XG4gICAgICBabGliLkJpdFN0cmVhbS5SZXZlcnNlVGFibGVbbiA+Pj4gMjQgJiAweEZGXTtcbiAgfVxuXG4gIGlmIChyZXZlcnNlICYmIG4gPiAxKSB7XG4gICAgbnVtYmVyID0gbiA+IDggP1xuICAgICAgcmV2MzJfKG51bWJlcikgPj4gKDMyIC0gbikgOlxuICAgICAgWmxpYi5CaXRTdHJlYW0uUmV2ZXJzZVRhYmxlW251bWJlcl0gPj4gKDggLSBuKTtcbiAgfVxuXG4gIC8vIEJ5dGUg5aKD55WM44KS6LaF44GI44Gq44GE44Go44GNXG4gIGlmIChuICsgYml0aW5kZXggPCA4KSB7XG4gICAgY3VycmVudCA9IChjdXJyZW50IDw8IG4pIHwgbnVtYmVyO1xuICAgIGJpdGluZGV4ICs9IG47XG4gIC8vIEJ5dGUg5aKD55WM44KS6LaF44GI44KL44Go44GNXG4gIH0gZWxzZSB7XG4gICAgZm9yIChpID0gMDsgaSA8IG47ICsraSkge1xuICAgICAgY3VycmVudCA9IChjdXJyZW50IDw8IDEpIHwgKChudW1iZXIgPj4gbiAtIGkgLSAxKSAmIDEpO1xuXG4gICAgICAvLyBuZXh0IGJ5dGVcbiAgICAgIGlmICgrK2JpdGluZGV4ID09PSA4KSB7XG4gICAgICAgIGJpdGluZGV4ID0gMDtcbiAgICAgICAgYnVmZmVyW2luZGV4KytdID0gWmxpYi5CaXRTdHJlYW0uUmV2ZXJzZVRhYmxlW2N1cnJlbnRdO1xuICAgICAgICBjdXJyZW50ID0gMDtcblxuICAgICAgICAvLyBleHBhbmRcbiAgICAgICAgaWYgKGluZGV4ID09PSBidWZmZXIubGVuZ3RoKSB7XG4gICAgICAgICAgYnVmZmVyID0gdGhpcy5leHBhbmRCdWZmZXIoKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuICBidWZmZXJbaW5kZXhdID0gY3VycmVudDtcblxuICB0aGlzLmJ1ZmZlciA9IGJ1ZmZlcjtcbiAgdGhpcy5iaXRpbmRleCA9IGJpdGluZGV4O1xuICB0aGlzLmluZGV4ID0gaW5kZXg7XG59O1xuXG5cbi8qKlxuICog44K544OI44Oq44O844Og44Gu57WC56uv5Yem55CG44KS6KGM44GGXG4gKiBAcmV0dXJuIHshKEFycmF5fFVpbnQ4QXJyYXkpfSDntYLnq6/lh6bnkIblvozjga7jg5Djg4Pjg5XjgqHjgpIgYnl0ZSBhcnJheSDjgafov5TjgZkuXG4gKi9cblpsaWIuQml0U3RyZWFtLnByb3RvdHlwZS5maW5pc2ggPSBmdW5jdGlvbigpIHtcbiAgdmFyIGJ1ZmZlciA9IHRoaXMuYnVmZmVyO1xuICB2YXIgaW5kZXggPSB0aGlzLmluZGV4O1xuXG4gIC8qKiBAdHlwZSB7IShBcnJheXxVaW50OEFycmF5KX0gb3V0cHV0IGJ1ZmZlci4gKi9cbiAgdmFyIG91dHB1dDtcblxuICAvLyBiaXRpbmRleCDjgYwgMCDjga7mmYLjga/kvZnliIbjgasgaW5kZXgg44GM6YCy44KT44Gn44GE44KL54q25oWLXG4gIGlmICh0aGlzLmJpdGluZGV4ID4gMCkge1xuICAgIGJ1ZmZlcltpbmRleF0gPDw9IDggLSB0aGlzLmJpdGluZGV4O1xuICAgIGJ1ZmZlcltpbmRleF0gPSBabGliLkJpdFN0cmVhbS5SZXZlcnNlVGFibGVbYnVmZmVyW2luZGV4XV07XG4gICAgaW5kZXgrKztcbiAgfVxuXG4gIC8vIGFycmF5IHRydW5jYXRpb25cbiAgaWYgKFVTRV9UWVBFREFSUkFZKSB7XG4gICAgb3V0cHV0ID0gYnVmZmVyLnN1YmFycmF5KDAsIGluZGV4KTtcbiAgfSBlbHNlIHtcbiAgICBidWZmZXIubGVuZ3RoID0gaW5kZXg7XG4gICAgb3V0cHV0ID0gYnVmZmVyO1xuICB9XG5cbiAgcmV0dXJuIG91dHB1dDtcbn07XG5cbi8qKlxuICogMC0yNTUg44Gu44OT44OD44OI6aCG44KS5Y+N6Lui44GX44Gf44OG44O844OW44OrXG4gKiBAY29uc3RcbiAqIEB0eXBlIHshKFVpbnQ4QXJyYXl8QXJyYXkuPG51bWJlcj4pfVxuICovXG5abGliLkJpdFN0cmVhbS5SZXZlcnNlVGFibGUgPSAoZnVuY3Rpb24odGFibGUpIHtcbiAgcmV0dXJuIHRhYmxlO1xufSkoKGZ1bmN0aW9uKCkge1xuICAvKiogQHR5cGUgeyEoQXJyYXl8VWludDhBcnJheSl9IHJldmVyc2UgdGFibGUuICovXG4gIHZhciB0YWJsZSA9IG5ldyAoVVNFX1RZUEVEQVJSQVkgPyBVaW50OEFycmF5IDogQXJyYXkpKDI1Nik7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSBsb29wIGNvdW50ZXIuICovXG4gIHZhciBpO1xuXG4gIC8vIGdlbmVyYXRlXG4gIGZvciAoaSA9IDA7IGkgPCAyNTY7ICsraSkge1xuICAgIHRhYmxlW2ldID0gKGZ1bmN0aW9uKG4pIHtcbiAgICAgIHZhciByID0gbjtcbiAgICAgIHZhciBzID0gNztcblxuICAgICAgZm9yIChuID4+Pj0gMTsgbjsgbiA+Pj49IDEpIHtcbiAgICAgICAgciA8PD0gMTtcbiAgICAgICAgciB8PSBuICYgMTtcbiAgICAgICAgLS1zO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gKHIgPDwgcyAmIDB4ZmYpID4+PiAwO1xuICAgIH0pKGkpO1xuICB9XG5cbiAgcmV0dXJuIHRhYmxlO1xufSkoKSk7XG5cblxuLy8gZW5kIG9mIHNjb3BlXG59KTtcblxuLyogdmltOnNldCBleHBhbmR0YWIgdHM9MiBzdz0yIHR3PTgwOiAqL1xuIiwiLyoqXG4gKiBAZmlsZW92ZXJ2aWV3IENSQzMyIOWun+ijhS5cbiAqL1xuZ29vZy5wcm92aWRlKCdabGliLkNSQzMyJyk7XG5cbmdvb2cucmVxdWlyZSgnVVNFX1RZUEVEQVJSQVknKTtcblxuLyoqIEBkZWZpbmUge2Jvb2xlYW59ICovXG52YXIgWkxJQl9DUkMzMl9DT01QQUNUID0gZmFsc2U7XG5cbmdvb2cuc2NvcGUoZnVuY3Rpb24oKSB7XG5cbi8qKlxuICogQ1JDMzIg44OP44OD44K344Ol5YCk44KS5Y+W5b6XXG4gKiBAcGFyYW0geyEoQXJyYXkuPG51bWJlcj58VWludDhBcnJheSl9IGRhdGEgZGF0YSBieXRlIGFycmF5LlxuICogQHBhcmFtIHtudW1iZXI9fSBwb3MgZGF0YSBwb3NpdGlvbi5cbiAqIEBwYXJhbSB7bnVtYmVyPX0gbGVuZ3RoIGRhdGEgbGVuZ3RoLlxuICogQHJldHVybiB7bnVtYmVyfSBDUkMzMi5cbiAqL1xuWmxpYi5DUkMzMi5jYWxjID0gZnVuY3Rpb24oZGF0YSwgcG9zLCBsZW5ndGgpIHtcbiAgcmV0dXJuIFpsaWIuQ1JDMzIudXBkYXRlKGRhdGEsIDAsIHBvcywgbGVuZ3RoKTtcbn07XG5cbi8qKlxuICogQ1JDMzLjg4/jg4Pjgrfjg6XlgKTjgpLmm7TmlrBcbiAqIEBwYXJhbSB7IShBcnJheS48bnVtYmVyPnxVaW50OEFycmF5KX0gZGF0YSBkYXRhIGJ5dGUgYXJyYXkuXG4gKiBAcGFyYW0ge251bWJlcn0gY3JjIENSQzMyLlxuICogQHBhcmFtIHtudW1iZXI9fSBwb3MgZGF0YSBwb3NpdGlvbi5cbiAqIEBwYXJhbSB7bnVtYmVyPX0gbGVuZ3RoIGRhdGEgbGVuZ3RoLlxuICogQHJldHVybiB7bnVtYmVyfSBDUkMzMi5cbiAqL1xuWmxpYi5DUkMzMi51cGRhdGUgPSBmdW5jdGlvbihkYXRhLCBjcmMsIHBvcywgbGVuZ3RoKSB7XG4gIHZhciB0YWJsZSA9IFpsaWIuQ1JDMzIuVGFibGU7XG4gIHZhciBpID0gKHR5cGVvZiBwb3MgPT09ICdudW1iZXInKSA/IHBvcyA6IChwb3MgPSAwKTtcbiAgdmFyIGlsID0gKHR5cGVvZiBsZW5ndGggPT09ICdudW1iZXInKSA/IGxlbmd0aCA6IGRhdGEubGVuZ3RoO1xuXG4gIGNyYyBePSAweGZmZmZmZmZmO1xuXG4gIC8vIGxvb3AgdW5yb2xsaW5nIGZvciBwZXJmb3JtYW5jZVxuICBmb3IgKGkgPSBpbCAmIDc7IGktLTsgKytwb3MpIHtcbiAgICBjcmMgPSAoY3JjID4+PiA4KSBeIHRhYmxlWyhjcmMgXiBkYXRhW3Bvc10pICYgMHhmZl07XG4gIH1cbiAgZm9yIChpID0gaWwgPj4gMzsgaS0tOyBwb3MgKz0gOCkge1xuICAgIGNyYyA9IChjcmMgPj4+IDgpIF4gdGFibGVbKGNyYyBeIGRhdGFbcG9zICAgIF0pICYgMHhmZl07XG4gICAgY3JjID0gKGNyYyA+Pj4gOCkgXiB0YWJsZVsoY3JjIF4gZGF0YVtwb3MgKyAxXSkgJiAweGZmXTtcbiAgICBjcmMgPSAoY3JjID4+PiA4KSBeIHRhYmxlWyhjcmMgXiBkYXRhW3BvcyArIDJdKSAmIDB4ZmZdO1xuICAgIGNyYyA9IChjcmMgPj4+IDgpIF4gdGFibGVbKGNyYyBeIGRhdGFbcG9zICsgM10pICYgMHhmZl07XG4gICAgY3JjID0gKGNyYyA+Pj4gOCkgXiB0YWJsZVsoY3JjIF4gZGF0YVtwb3MgKyA0XSkgJiAweGZmXTtcbiAgICBjcmMgPSAoY3JjID4+PiA4KSBeIHRhYmxlWyhjcmMgXiBkYXRhW3BvcyArIDVdKSAmIDB4ZmZdO1xuICAgIGNyYyA9IChjcmMgPj4+IDgpIF4gdGFibGVbKGNyYyBeIGRhdGFbcG9zICsgNl0pICYgMHhmZl07XG4gICAgY3JjID0gKGNyYyA+Pj4gOCkgXiB0YWJsZVsoY3JjIF4gZGF0YVtwb3MgKyA3XSkgJiAweGZmXTtcbiAgfVxuXG4gIHJldHVybiAoY3JjIF4gMHhmZmZmZmZmZikgPj4+IDA7XG59O1xuXG4vKipcbiAqIEBwYXJhbSB7bnVtYmVyfSBudW1cbiAqIEBwYXJhbSB7bnVtYmVyfSBjcmNcbiAqIEByZXR1cm5zIHtudW1iZXJ9XG4gKi9cblpsaWIuQ1JDMzIuc2luZ2xlID0gZnVuY3Rpb24obnVtLCBjcmMpIHtcbiAgcmV0dXJuIChabGliLkNSQzMyLlRhYmxlWyhudW0gXiBjcmMpICYgMHhmZl0gXiAobnVtID4+PiA4KSkgPj4+IDA7XG59O1xuXG4vKipcbiAqIEB0eXBlIHtBcnJheS48bnVtYmVyPn1cbiAqIEBjb25zdFxuICogQHByaXZhdGVcbiAqL1xuWmxpYi5DUkMzMi5UYWJsZV8gPSBbXG4gIDB4MDAwMDAwMDAsIDB4NzcwNzMwOTYsIDB4ZWUwZTYxMmMsIDB4OTkwOTUxYmEsIDB4MDc2ZGM0MTksIDB4NzA2YWY0OGYsXG4gIDB4ZTk2M2E1MzUsIDB4OWU2NDk1YTMsIDB4MGVkYjg4MzIsIDB4NzlkY2I4YTQsIDB4ZTBkNWU5MWUsIDB4OTdkMmQ5ODgsXG4gIDB4MDliNjRjMmIsIDB4N2ViMTdjYmQsIDB4ZTdiODJkMDcsIDB4OTBiZjFkOTEsIDB4MWRiNzEwNjQsIDB4NmFiMDIwZjIsXG4gIDB4ZjNiOTcxNDgsIDB4ODRiZTQxZGUsIDB4MWFkYWQ0N2QsIDB4NmRkZGU0ZWIsIDB4ZjRkNGI1NTEsIDB4ODNkMzg1YzcsXG4gIDB4MTM2Yzk4NTYsIDB4NjQ2YmE4YzAsIDB4ZmQ2MmY5N2EsIDB4OGE2NWM5ZWMsIDB4MTQwMTVjNGYsIDB4NjMwNjZjZDksXG4gIDB4ZmEwZjNkNjMsIDB4OGQwODBkZjUsIDB4M2I2ZTIwYzgsIDB4NGM2OTEwNWUsIDB4ZDU2MDQxZTQsIDB4YTI2NzcxNzIsXG4gIDB4M2MwM2U0ZDEsIDB4NGIwNGQ0NDcsIDB4ZDIwZDg1ZmQsIDB4YTUwYWI1NmIsIDB4MzViNWE4ZmEsIDB4NDJiMjk4NmMsXG4gIDB4ZGJiYmM5ZDYsIDB4YWNiY2Y5NDAsIDB4MzJkODZjZTMsIDB4NDVkZjVjNzUsIDB4ZGNkNjBkY2YsIDB4YWJkMTNkNTksXG4gIDB4MjZkOTMwYWMsIDB4NTFkZTAwM2EsIDB4YzhkNzUxODAsIDB4YmZkMDYxMTYsIDB4MjFiNGY0YjUsIDB4NTZiM2M0MjMsXG4gIDB4Y2ZiYTk1OTksIDB4YjhiZGE1MGYsIDB4MjgwMmI4OWUsIDB4NWYwNTg4MDgsIDB4YzYwY2Q5YjIsIDB4YjEwYmU5MjQsXG4gIDB4MmY2ZjdjODcsIDB4NTg2ODRjMTEsIDB4YzE2MTFkYWIsIDB4YjY2NjJkM2QsIDB4NzZkYzQxOTAsIDB4MDFkYjcxMDYsXG4gIDB4OThkMjIwYmMsIDB4ZWZkNTEwMmEsIDB4NzFiMTg1ODksIDB4MDZiNmI1MWYsIDB4OWZiZmU0YTUsIDB4ZThiOGQ0MzMsXG4gIDB4NzgwN2M5YTIsIDB4MGYwMGY5MzQsIDB4OTYwOWE4OGUsIDB4ZTEwZTk4MTgsIDB4N2Y2YTBkYmIsIDB4MDg2ZDNkMmQsXG4gIDB4OTE2NDZjOTcsIDB4ZTY2MzVjMDEsIDB4NmI2YjUxZjQsIDB4MWM2YzYxNjIsIDB4ODU2NTMwZDgsIDB4ZjI2MjAwNGUsXG4gIDB4NmMwNjk1ZWQsIDB4MWIwMWE1N2IsIDB4ODIwOGY0YzEsIDB4ZjUwZmM0NTcsIDB4NjViMGQ5YzYsIDB4MTJiN2U5NTAsXG4gIDB4OGJiZWI4ZWEsIDB4ZmNiOTg4N2MsIDB4NjJkZDFkZGYsIDB4MTVkYTJkNDksIDB4OGNkMzdjZjMsIDB4ZmJkNDRjNjUsXG4gIDB4NGRiMjYxNTgsIDB4M2FiNTUxY2UsIDB4YTNiYzAwNzQsIDB4ZDRiYjMwZTIsIDB4NGFkZmE1NDEsIDB4M2RkODk1ZDcsXG4gIDB4YTRkMWM0NmQsIDB4ZDNkNmY0ZmIsIDB4NDM2OWU5NmEsIDB4MzQ2ZWQ5ZmMsIDB4YWQ2Nzg4NDYsIDB4ZGE2MGI4ZDAsXG4gIDB4NDQwNDJkNzMsIDB4MzMwMzFkZTUsIDB4YWEwYTRjNWYsIDB4ZGQwZDdjYzksIDB4NTAwNTcxM2MsIDB4MjcwMjQxYWEsXG4gIDB4YmUwYjEwMTAsIDB4YzkwYzIwODYsIDB4NTc2OGI1MjUsIDB4MjA2Zjg1YjMsIDB4Yjk2NmQ0MDksIDB4Y2U2MWU0OWYsXG4gIDB4NWVkZWY5MGUsIDB4MjlkOWM5OTgsIDB4YjBkMDk4MjIsIDB4YzdkN2E4YjQsIDB4NTliMzNkMTcsIDB4MmViNDBkODEsXG4gIDB4YjdiZDVjM2IsIDB4YzBiYTZjYWQsIDB4ZWRiODgzMjAsIDB4OWFiZmIzYjYsIDB4MDNiNmUyMGMsIDB4NzRiMWQyOWEsXG4gIDB4ZWFkNTQ3MzksIDB4OWRkMjc3YWYsIDB4MDRkYjI2MTUsIDB4NzNkYzE2ODMsIDB4ZTM2MzBiMTIsIDB4OTQ2NDNiODQsXG4gIDB4MGQ2ZDZhM2UsIDB4N2E2YTVhYTgsIDB4ZTQwZWNmMGIsIDB4OTMwOWZmOWQsIDB4MGEwMGFlMjcsIDB4N2QwNzllYjEsXG4gIDB4ZjAwZjkzNDQsIDB4ODcwOGEzZDIsIDB4MWUwMWYyNjgsIDB4NjkwNmMyZmUsIDB4Zjc2MjU3NWQsIDB4ODA2NTY3Y2IsXG4gIDB4MTk2YzM2NzEsIDB4NmU2YjA2ZTcsIDB4ZmVkNDFiNzYsIDB4ODlkMzJiZTAsIDB4MTBkYTdhNWEsIDB4NjdkZDRhY2MsXG4gIDB4ZjliOWRmNmYsIDB4OGViZWVmZjksIDB4MTdiN2JlNDMsIDB4NjBiMDhlZDUsIDB4ZDZkNmEzZTgsIDB4YTFkMTkzN2UsXG4gIDB4MzhkOGMyYzQsIDB4NGZkZmYyNTIsIDB4ZDFiYjY3ZjEsIDB4YTZiYzU3NjcsIDB4M2ZiNTA2ZGQsIDB4NDhiMjM2NGIsXG4gIDB4ZDgwZDJiZGEsIDB4YWYwYTFiNGMsIDB4MzYwMzRhZjYsIDB4NDEwNDdhNjAsIDB4ZGY2MGVmYzMsIDB4YTg2N2RmNTUsXG4gIDB4MzE2ZThlZWYsIDB4NDY2OWJlNzksIDB4Y2I2MWIzOGMsIDB4YmM2NjgzMWEsIDB4MjU2ZmQyYTAsIDB4NTI2OGUyMzYsXG4gIDB4Y2MwYzc3OTUsIDB4YmIwYjQ3MDMsIDB4MjIwMjE2YjksIDB4NTUwNTI2MmYsIDB4YzViYTNiYmUsIDB4YjJiZDBiMjgsXG4gIDB4MmJiNDVhOTIsIDB4NWNiMzZhMDQsIDB4YzJkN2ZmYTcsIDB4YjVkMGNmMzEsIDB4MmNkOTllOGIsIDB4NWJkZWFlMWQsXG4gIDB4OWI2NGMyYjAsIDB4ZWM2M2YyMjYsIDB4NzU2YWEzOWMsIDB4MDI2ZDkzMGEsIDB4OWMwOTA2YTksIDB4ZWIwZTM2M2YsXG4gIDB4NzIwNzY3ODUsIDB4MDUwMDU3MTMsIDB4OTViZjRhODIsIDB4ZTJiODdhMTQsIDB4N2JiMTJiYWUsIDB4MGNiNjFiMzgsXG4gIDB4OTJkMjhlOWIsIDB4ZTVkNWJlMGQsIDB4N2NkY2VmYjcsIDB4MGJkYmRmMjEsIDB4ODZkM2QyZDQsIDB4ZjFkNGUyNDIsXG4gIDB4NjhkZGIzZjgsIDB4MWZkYTgzNmUsIDB4ODFiZTE2Y2QsIDB4ZjZiOTI2NWIsIDB4NmZiMDc3ZTEsIDB4MThiNzQ3NzcsXG4gIDB4ODgwODVhZTYsIDB4ZmYwZjZhNzAsIDB4NjYwNjNiY2EsIDB4MTEwMTBiNWMsIDB4OGY2NTllZmYsIDB4Zjg2MmFlNjksXG4gIDB4NjE2YmZmZDMsIDB4MTY2Y2NmNDUsIDB4YTAwYWUyNzgsIDB4ZDcwZGQyZWUsIDB4NGUwNDgzNTQsIDB4MzkwM2IzYzIsXG4gIDB4YTc2NzI2NjEsIDB4ZDA2MDE2ZjcsIDB4NDk2OTQ3NGQsIDB4M2U2ZTc3ZGIsIDB4YWVkMTZhNGEsIDB4ZDlkNjVhZGMsXG4gIDB4NDBkZjBiNjYsIDB4MzdkODNiZjAsIDB4YTliY2FlNTMsIDB4ZGViYjllYzUsIDB4NDdiMmNmN2YsIDB4MzBiNWZmZTksXG4gIDB4YmRiZGYyMWMsIDB4Y2FiYWMyOGEsIDB4NTNiMzkzMzAsIDB4MjRiNGEzYTYsIDB4YmFkMDM2MDUsIDB4Y2RkNzA2OTMsXG4gIDB4NTRkZTU3MjksIDB4MjNkOTY3YmYsIDB4YjM2NjdhMmUsIDB4YzQ2MTRhYjgsIDB4NWQ2ODFiMDIsIDB4MmE2ZjJiOTQsXG4gIDB4YjQwYmJlMzcsIDB4YzMwYzhlYTEsIDB4NWEwNWRmMWIsIDB4MmQwMmVmOGRcbl07XG5cbi8qKlxuICogQHR5cGUgeyEoQXJyYXkuPG51bWJlcj58VWludDMyQXJyYXkpfSBDUkMtMzIgVGFibGUuXG4gKiBAY29uc3RcbiAqL1xuWmxpYi5DUkMzMi5UYWJsZSA9IFpMSUJfQ1JDMzJfQ09NUEFDVCA/IChmdW5jdGlvbigpIHtcbiAgLyoqIEB0eXBlIHshKEFycmF5LjxudW1iZXI+fFVpbnQzMkFycmF5KX0gKi9cbiAgdmFyIHRhYmxlID0gbmV3IChVU0VfVFlQRURBUlJBWSA/IFVpbnQzMkFycmF5IDogQXJyYXkpKDI1Nik7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgYztcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciBpO1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgdmFyIGo7XG5cbiAgZm9yIChpID0gMDsgaSA8IDI1NjsgKytpKSB7XG4gICAgYyA9IGk7XG4gICAgZm9yIChqID0gMDsgaiA8IDg7ICsraikge1xuICAgICAgYyA9IChjICYgMSkgPyAoMHhlZEI4ODMyMCBeIChjID4+PiAxKSkgOiAoYyA+Pj4gMSk7XG4gICAgfVxuICAgIHRhYmxlW2ldID0gYyA+Pj4gMDtcbiAgfVxuXG4gIHJldHVybiB0YWJsZTtcbn0pKCkgOiBVU0VfVFlQRURBUlJBWSA/IG5ldyBVaW50MzJBcnJheShabGliLkNSQzMyLlRhYmxlXykgOiBabGliLkNSQzMyLlRhYmxlXztcblxufSk7XG4iLCIvKipcbiAqIEBmaWxlb3ZlcnZpZXcgSGVhcCBTb3J0IOWun+ijhS4g44OP44OV44Oe44Oz56ym5Y+35YyW44Gn5L2/55So44GZ44KLLlxuICovXG5cbmdvb2cucHJvdmlkZSgnWmxpYi5IZWFwJyk7XG5cbmdvb2cucmVxdWlyZSgnVVNFX1RZUEVEQVJSQVknKTtcblxuZ29vZy5zY29wZShmdW5jdGlvbigpIHtcblxuLyoqXG4gKiDjgqvjgrnjgr/jg6Djg4/jg5Xjg57jg7PnrKblj7fjgafkvb/nlKjjgZnjgovjg5Ljg7zjg5flrp/oo4VcbiAqIEBwYXJhbSB7bnVtYmVyfSBsZW5ndGgg44OS44O844OX44K144Kk44K6LlxuICogQGNvbnN0cnVjdG9yXG4gKi9cblpsaWIuSGVhcCA9IGZ1bmN0aW9uKGxlbmd0aCkge1xuICB0aGlzLmJ1ZmZlciA9IG5ldyAoVVNFX1RZUEVEQVJSQVkgPyBVaW50MTZBcnJheSA6IEFycmF5KShsZW5ndGggKiAyKTtcbiAgdGhpcy5sZW5ndGggPSAwO1xufTtcblxuLyoqXG4gKiDopqrjg47jg7zjg4njga4gaW5kZXgg5Y+W5b6XXG4gKiBAcGFyYW0ge251bWJlcn0gaW5kZXgg5a2Q44OO44O844OJ44GuIGluZGV4LlxuICogQHJldHVybiB7bnVtYmVyfSDopqrjg47jg7zjg4njga4gaW5kZXguXG4gKlxuICovXG5abGliLkhlYXAucHJvdG90eXBlLmdldFBhcmVudCA9IGZ1bmN0aW9uKGluZGV4KSB7XG4gIHJldHVybiAoKGluZGV4IC0gMikgLyA0IHwgMCkgKiAyO1xufTtcblxuLyoqXG4gKiDlrZDjg47jg7zjg4njga4gaW5kZXgg5Y+W5b6XXG4gKiBAcGFyYW0ge251bWJlcn0gaW5kZXgg6Kaq44OO44O844OJ44GuIGluZGV4LlxuICogQHJldHVybiB7bnVtYmVyfSDlrZDjg47jg7zjg4njga4gaW5kZXguXG4gKi9cblpsaWIuSGVhcC5wcm90b3R5cGUuZ2V0Q2hpbGQgPSBmdW5jdGlvbihpbmRleCkge1xuICByZXR1cm4gMiAqIGluZGV4ICsgMjtcbn07XG5cbi8qKlxuICogSGVhcCDjgavlgKTjgpLov73liqDjgZnjgotcbiAqIEBwYXJhbSB7bnVtYmVyfSBpbmRleCDjgq3jg7wgaW5kZXguXG4gKiBAcGFyYW0ge251bWJlcn0gdmFsdWUg5YCkLlxuICogQHJldHVybiB7bnVtYmVyfSDnj77lnKjjga7jg5Ljg7zjg5fplbcuXG4gKi9cblpsaWIuSGVhcC5wcm90b3R5cGUucHVzaCA9IGZ1bmN0aW9uKGluZGV4LCB2YWx1ZSkge1xuICB2YXIgY3VycmVudCwgcGFyZW50LFxuICAgICAgaGVhcCA9IHRoaXMuYnVmZmVyLFxuICAgICAgc3dhcDtcblxuICBjdXJyZW50ID0gdGhpcy5sZW5ndGg7XG4gIGhlYXBbdGhpcy5sZW5ndGgrK10gPSB2YWx1ZTtcbiAgaGVhcFt0aGlzLmxlbmd0aCsrXSA9IGluZGV4O1xuXG4gIC8vIOODq+ODvOODiOODjuODvOODieOBq+OBn+OBqeOCiuedgOOBj+OBvuOBp+WFpeOCjOabv+OBiOOCkuippuOBv+OCi1xuICB3aGlsZSAoY3VycmVudCA+IDApIHtcbiAgICBwYXJlbnQgPSB0aGlzLmdldFBhcmVudChjdXJyZW50KTtcblxuICAgIC8vIOimquODjuODvOODieOBqOavlOi8g+OBl+OBpuimquOBruaWueOBjOWwj+OBleOBkeOCjOOBsOWFpeOCjOabv+OBiOOCi1xuICAgIGlmIChoZWFwW2N1cnJlbnRdID4gaGVhcFtwYXJlbnRdKSB7XG4gICAgICBzd2FwID0gaGVhcFtjdXJyZW50XTtcbiAgICAgIGhlYXBbY3VycmVudF0gPSBoZWFwW3BhcmVudF07XG4gICAgICBoZWFwW3BhcmVudF0gPSBzd2FwO1xuXG4gICAgICBzd2FwID0gaGVhcFtjdXJyZW50ICsgMV07XG4gICAgICBoZWFwW2N1cnJlbnQgKyAxXSA9IGhlYXBbcGFyZW50ICsgMV07XG4gICAgICBoZWFwW3BhcmVudCArIDFdID0gc3dhcDtcblxuICAgICAgY3VycmVudCA9IHBhcmVudDtcbiAgICAvLyDlhaXjgozmm7/jgYjjgYzlv4XopoHjgarjgY/jgarjgaPjgZ/jgonjgZ3jgZPjgafmipzjgZHjgotcbiAgICB9IGVsc2Uge1xuICAgICAgYnJlYWs7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHRoaXMubGVuZ3RoO1xufTtcblxuLyoqXG4gKiBIZWFw44GL44KJ5LiA55Wq5aSn44GN44GE5YCk44KS6L+U44GZXG4gKiBAcmV0dXJuIHt7aW5kZXg6IG51bWJlciwgdmFsdWU6IG51bWJlciwgbGVuZ3RoOiBudW1iZXJ9fSB7aW5kZXg6IOOCreODvGluZGV4LFxuICogICAgIHZhbHVlOiDlgKQsIGxlbmd0aDog44OS44O844OX6ZW3fSDjga4gT2JqZWN0LlxuICovXG5abGliLkhlYXAucHJvdG90eXBlLnBvcCA9IGZ1bmN0aW9uKCkge1xuICB2YXIgaW5kZXgsIHZhbHVlLFxuICAgICAgaGVhcCA9IHRoaXMuYnVmZmVyLCBzd2FwLFxuICAgICAgY3VycmVudCwgcGFyZW50O1xuXG4gIHZhbHVlID0gaGVhcFswXTtcbiAgaW5kZXggPSBoZWFwWzFdO1xuXG4gIC8vIOW+jOOCjeOBi+OCieWApOOCkuWPluOCi1xuICB0aGlzLmxlbmd0aCAtPSAyO1xuICBoZWFwWzBdID0gaGVhcFt0aGlzLmxlbmd0aF07XG4gIGhlYXBbMV0gPSBoZWFwW3RoaXMubGVuZ3RoICsgMV07XG5cbiAgcGFyZW50ID0gMDtcbiAgLy8g44Or44O844OI44OO44O844OJ44GL44KJ5LiL44GM44Gj44Gm44GE44GPXG4gIHdoaWxlICh0cnVlKSB7XG4gICAgY3VycmVudCA9IHRoaXMuZ2V0Q2hpbGQocGFyZW50KTtcblxuICAgIC8vIOevhOWbsuODgeOCp+ODg+OCr1xuICAgIGlmIChjdXJyZW50ID49IHRoaXMubGVuZ3RoKSB7XG4gICAgICBicmVhaztcbiAgICB9XG5cbiAgICAvLyDpmqPjga7jg47jg7zjg4njgajmr5TovIPjgZfjgabjgIHpmqPjga7mlrnjgYzlgKTjgYzlpKfjgY3jgZHjgozjgbDpmqPjgpLnj77lnKjjg47jg7zjg4njgajjgZfjgabpgbjmip5cbiAgICBpZiAoY3VycmVudCArIDIgPCB0aGlzLmxlbmd0aCAmJiBoZWFwW2N1cnJlbnQgKyAyXSA+IGhlYXBbY3VycmVudF0pIHtcbiAgICAgIGN1cnJlbnQgKz0gMjtcbiAgICB9XG5cbiAgICAvLyDopqrjg47jg7zjg4njgajmr5TovIPjgZfjgabopqrjga7mlrnjgYzlsI/jgZXjgYTloLTlkIjjga/lhaXjgozmm7/jgYjjgotcbiAgICBpZiAoaGVhcFtjdXJyZW50XSA+IGhlYXBbcGFyZW50XSkge1xuICAgICAgc3dhcCA9IGhlYXBbcGFyZW50XTtcbiAgICAgIGhlYXBbcGFyZW50XSA9IGhlYXBbY3VycmVudF07XG4gICAgICBoZWFwW2N1cnJlbnRdID0gc3dhcDtcblxuICAgICAgc3dhcCA9IGhlYXBbcGFyZW50ICsgMV07XG4gICAgICBoZWFwW3BhcmVudCArIDFdID0gaGVhcFtjdXJyZW50ICsgMV07XG4gICAgICBoZWFwW2N1cnJlbnQgKyAxXSA9IHN3YXA7XG4gICAgfSBlbHNlIHtcbiAgICAgIGJyZWFrO1xuICAgIH1cblxuICAgIHBhcmVudCA9IGN1cnJlbnQ7XG4gIH1cblxuICByZXR1cm4ge2luZGV4OiBpbmRleCwgdmFsdWU6IHZhbHVlLCBsZW5ndGg6IHRoaXMubGVuZ3RofTtcbn07XG5cblxuLy8gZW5kIG9mIHNjb3BlXG59KTtcblxuLyogdmltOnNldCBleHBhbmR0YWIgdHM9MiBzdz0yIHR3PTgwOiAqL1xuIiwiLyoqXG4gKiBAZmlsZW92ZXJ2aWV3IERlZmxhdGUgKFJGQzE5NTEpIOespuWPt+WMluOCouODq+OCtOODquOCuuODoOWun+ijhS5cbiAqL1xuXG5nb29nLnByb3ZpZGUoJ1psaWIuUmF3RGVmbGF0ZScpO1xuXG5nb29nLnJlcXVpcmUoJ1VTRV9UWVBFREFSUkFZJyk7XG5nb29nLnJlcXVpcmUoJ1psaWIuQml0U3RyZWFtJyk7XG5nb29nLnJlcXVpcmUoJ1psaWIuSGVhcCcpO1xuXG5nb29nLnNjb3BlKGZ1bmN0aW9uKCkge1xuXG4vKipcbiAqIFJhdyBEZWZsYXRlIOWun+ijhVxuICpcbiAqIEBjb25zdHJ1Y3RvclxuICogQHBhcmFtIHshKEFycmF5LjxudW1iZXI+fFVpbnQ4QXJyYXkpfSBpbnB1dCDnrKblj7fljJbjgZnjgovlr77osaHjga7jg5Djg4Pjg5XjgqEuXG4gKiBAcGFyYW0ge09iamVjdD19IG9wdF9wYXJhbXMgb3B0aW9uIHBhcmFtZXRlcnMuXG4gKlxuICogdHlwZWQgYXJyYXkg44GM5L2/55So5Y+v6IO944Gq44Go44GN44CBb3V0cHV0QnVmZmVyIOOBjCBBcnJheSDjga/oh6rli5XnmoTjgasgVWludDhBcnJheSDjgatcbiAqIOWkieaPm+OBleOCjOOBvuOBmS5cbiAqIOWIpeOBruOCquODluOCuOOCp+OCr+ODiOOBq+OBquOCi+OBn+OCgeWHuuWKm+ODkOODg+ODleOCoeOCkuWPgueFp+OBl+OBpuOBhOOCi+WkieaVsOOBquOBqeOBr1xuICog5pu05paw44GZ44KL5b+F6KaB44GM44GC44KK44G+44GZLlxuICovXG5abGliLlJhd0RlZmxhdGUgPSBmdW5jdGlvbihpbnB1dCwgb3B0X3BhcmFtcykge1xuICAvKiogQHR5cGUge1psaWIuUmF3RGVmbGF0ZS5Db21wcmVzc2lvblR5cGV9ICovXG4gIHRoaXMuY29tcHJlc3Npb25UeXBlID0gWmxpYi5SYXdEZWZsYXRlLkNvbXByZXNzaW9uVHlwZS5EWU5BTUlDO1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgdGhpcy5sYXp5ID0gMDtcbiAgLyoqIEB0eXBlIHshKEFycmF5LjxudW1iZXI+fFVpbnQzMkFycmF5KX0gKi9cbiAgdGhpcy5mcmVxc0xpdExlbjtcbiAgLyoqIEB0eXBlIHshKEFycmF5LjxudW1iZXI+fFVpbnQzMkFycmF5KX0gKi9cbiAgdGhpcy5mcmVxc0Rpc3Q7XG4gIC8qKiBAdHlwZSB7IShBcnJheS48bnVtYmVyPnxVaW50OEFycmF5KX0gKi9cbiAgdGhpcy5pbnB1dCA9XG4gICAgKFVTRV9UWVBFREFSUkFZICYmIGlucHV0IGluc3RhbmNlb2YgQXJyYXkpID8gbmV3IFVpbnQ4QXJyYXkoaW5wdXQpIDogaW5wdXQ7XG4gIC8qKiBAdHlwZSB7IShBcnJheS48bnVtYmVyPnxVaW50OEFycmF5KX0gb3V0cHV0IG91dHB1dCBidWZmZXIuICovXG4gIHRoaXMub3V0cHV0O1xuICAvKiogQHR5cGUge251bWJlcn0gcG9zIG91dHB1dCBidWZmZXIgcG9zaXRpb24uICovXG4gIHRoaXMub3AgPSAwO1xuXG4gIC8vIG9wdGlvbiBwYXJhbWV0ZXJzXG4gIGlmIChvcHRfcGFyYW1zKSB7XG4gICAgaWYgKG9wdF9wYXJhbXNbJ2xhenknXSkge1xuICAgICAgdGhpcy5sYXp5ID0gb3B0X3BhcmFtc1snbGF6eSddO1xuICAgIH1cbiAgICBpZiAodHlwZW9mIG9wdF9wYXJhbXNbJ2NvbXByZXNzaW9uVHlwZSddID09PSAnbnVtYmVyJykge1xuICAgICAgdGhpcy5jb21wcmVzc2lvblR5cGUgPSBvcHRfcGFyYW1zWydjb21wcmVzc2lvblR5cGUnXTtcbiAgICB9XG4gICAgaWYgKG9wdF9wYXJhbXNbJ291dHB1dEJ1ZmZlciddKSB7XG4gICAgICB0aGlzLm91dHB1dCA9XG4gICAgICAgIChVU0VfVFlQRURBUlJBWSAmJiBvcHRfcGFyYW1zWydvdXRwdXRCdWZmZXInXSBpbnN0YW5jZW9mIEFycmF5KSA/XG4gICAgICAgIG5ldyBVaW50OEFycmF5KG9wdF9wYXJhbXNbJ291dHB1dEJ1ZmZlciddKSA6IG9wdF9wYXJhbXNbJ291dHB1dEJ1ZmZlciddO1xuICAgIH1cbiAgICBpZiAodHlwZW9mIG9wdF9wYXJhbXNbJ291dHB1dEluZGV4J10gPT09ICdudW1iZXInKSB7XG4gICAgICB0aGlzLm9wID0gb3B0X3BhcmFtc1snb3V0cHV0SW5kZXgnXTtcbiAgICB9XG4gIH1cblxuICBpZiAoIXRoaXMub3V0cHV0KSB7XG4gICAgdGhpcy5vdXRwdXQgPSBuZXcgKFVTRV9UWVBFREFSUkFZID8gVWludDhBcnJheSA6IEFycmF5KSgweDgwMDApO1xuICB9XG59O1xuXG4vKipcbiAqIEBlbnVtIHtudW1iZXJ9XG4gKi9cblpsaWIuUmF3RGVmbGF0ZS5Db21wcmVzc2lvblR5cGUgPSB7XG4gIE5PTkU6IDAsXG4gIEZJWEVEOiAxLFxuICBEWU5BTUlDOiAyLFxuICBSRVNFUlZFRDogM1xufTtcblxuXG4vKipcbiAqIExaNzcg44Gu5pyA5bCP44Oe44OD44OB6ZW3XG4gKiBAY29uc3RcbiAqIEB0eXBlIHtudW1iZXJ9XG4gKi9cblpsaWIuUmF3RGVmbGF0ZS5Mejc3TWluTGVuZ3RoID0gMztcblxuLyoqXG4gKiBMWjc3IOOBruacgOWkp+ODnuODg+ODgemVt1xuICogQGNvbnN0XG4gKiBAdHlwZSB7bnVtYmVyfVxuICovXG5abGliLlJhd0RlZmxhdGUuTHo3N01heExlbmd0aCA9IDI1ODtcblxuLyoqXG4gKiBMWjc3IOOBruOCpuOCo+ODs+ODieOCpuOCteOCpOOCulxuICogQGNvbnN0XG4gKiBAdHlwZSB7bnVtYmVyfVxuICovXG5abGliLlJhd0RlZmxhdGUuV2luZG93U2l6ZSA9IDB4ODAwMDtcblxuLyoqXG4gKiDmnIDplbfjga7nrKblj7fplbdcbiAqIEBjb25zdFxuICogQHR5cGUge251bWJlcn1cbiAqL1xuWmxpYi5SYXdEZWZsYXRlLk1heENvZGVMZW5ndGggPSAxNjtcblxuLyoqXG4gKiDjg4/jg5Xjg57jg7PnrKblj7fjga7mnIDlpKfmlbDlgKRcbiAqIEBjb25zdFxuICogQHR5cGUge251bWJlcn1cbiAqL1xuWmxpYi5SYXdEZWZsYXRlLkhVRk1BWCA9IDI4NjtcblxuLyoqXG4gKiDlm7rlrprjg4/jg5Xjg57jg7PnrKblj7fjga7nrKblj7fljJbjg4bjg7zjg5bjg6tcbiAqIEBjb25zdFxuICogQHR5cGUge0FycmF5LjxBcnJheS48bnVtYmVyLCBudW1iZXI+Pn1cbiAqL1xuWmxpYi5SYXdEZWZsYXRlLkZpeGVkSHVmZm1hblRhYmxlID0gKGZ1bmN0aW9uKCkge1xuICB2YXIgdGFibGUgPSBbXSwgaTtcblxuICBmb3IgKGkgPSAwOyBpIDwgMjg4OyBpKyspIHtcbiAgICBzd2l0Y2ggKHRydWUpIHtcbiAgICAgIGNhc2UgKGkgPD0gMTQzKTogdGFibGUucHVzaChbaSAgICAgICArIDB4MDMwLCA4XSk7IGJyZWFrO1xuICAgICAgY2FzZSAoaSA8PSAyNTUpOiB0YWJsZS5wdXNoKFtpIC0gMTQ0ICsgMHgxOTAsIDldKTsgYnJlYWs7XG4gICAgICBjYXNlIChpIDw9IDI3OSk6IHRhYmxlLnB1c2goW2kgLSAyNTYgKyAweDAwMCwgN10pOyBicmVhaztcbiAgICAgIGNhc2UgKGkgPD0gMjg3KTogdGFibGUucHVzaChbaSAtIDI4MCArIDB4MEMwLCA4XSk7IGJyZWFrO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgdGhyb3cgJ2ludmFsaWQgbGl0ZXJhbDogJyArIGk7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHRhYmxlO1xufSkoKTtcblxuLyoqXG4gKiBERUZMQVRFIOODluODreODg+OCr+OBruS9nOaIkFxuICogQHJldHVybiB7IShBcnJheS48bnVtYmVyPnxVaW50OEFycmF5KX0g5Zyn57iu5riI44G/IGJ5dGUgYXJyYXkuXG4gKi9cblpsaWIuUmF3RGVmbGF0ZS5wcm90b3R5cGUuY29tcHJlc3MgPSBmdW5jdGlvbigpIHtcbiAgLyoqIEB0eXBlIHshKEFycmF5LjxudW1iZXI+fFVpbnQ4QXJyYXkpfSAqL1xuICB2YXIgYmxvY2tBcnJheTtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciBwb3NpdGlvbjtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciBsZW5ndGg7XG5cbiAgdmFyIGlucHV0ID0gdGhpcy5pbnB1dDtcblxuICAvLyBjb21wcmVzc2lvblxuICBzd2l0Y2ggKHRoaXMuY29tcHJlc3Npb25UeXBlKSB7XG4gICAgY2FzZSBabGliLlJhd0RlZmxhdGUuQ29tcHJlc3Npb25UeXBlLk5PTkU6XG4gICAgICAvLyBlYWNoIDY1NTM1LUJ5dGUgKGxlbmd0aCBoZWFkZXI6IDE2LWJpdClcbiAgICAgIGZvciAocG9zaXRpb24gPSAwLCBsZW5ndGggPSBpbnB1dC5sZW5ndGg7IHBvc2l0aW9uIDwgbGVuZ3RoOykge1xuICAgICAgICBibG9ja0FycmF5ID0gVVNFX1RZUEVEQVJSQVkgP1xuICAgICAgICAgIGlucHV0LnN1YmFycmF5KHBvc2l0aW9uLCBwb3NpdGlvbiArIDB4ZmZmZikgOlxuICAgICAgICAgIGlucHV0LnNsaWNlKHBvc2l0aW9uLCBwb3NpdGlvbiArIDB4ZmZmZik7XG4gICAgICAgIHBvc2l0aW9uICs9IGJsb2NrQXJyYXkubGVuZ3RoO1xuICAgICAgICB0aGlzLm1ha2VOb2NvbXByZXNzQmxvY2soYmxvY2tBcnJheSwgKHBvc2l0aW9uID09PSBsZW5ndGgpKTtcbiAgICAgIH1cbiAgICAgIGJyZWFrO1xuICAgIGNhc2UgWmxpYi5SYXdEZWZsYXRlLkNvbXByZXNzaW9uVHlwZS5GSVhFRDpcbiAgICAgIHRoaXMub3V0cHV0ID0gdGhpcy5tYWtlRml4ZWRIdWZmbWFuQmxvY2soaW5wdXQsIHRydWUpO1xuICAgICAgdGhpcy5vcCA9IHRoaXMub3V0cHV0Lmxlbmd0aDtcbiAgICAgIGJyZWFrO1xuICAgIGNhc2UgWmxpYi5SYXdEZWZsYXRlLkNvbXByZXNzaW9uVHlwZS5EWU5BTUlDOlxuICAgICAgdGhpcy5vdXRwdXQgPSB0aGlzLm1ha2VEeW5hbWljSHVmZm1hbkJsb2NrKGlucHV0LCB0cnVlKTtcbiAgICAgIHRoaXMub3AgPSB0aGlzLm91dHB1dC5sZW5ndGg7XG4gICAgICBicmVhaztcbiAgICBkZWZhdWx0OlxuICAgICAgdGhyb3cgJ2ludmFsaWQgY29tcHJlc3Npb24gdHlwZSc7XG4gIH1cblxuICByZXR1cm4gdGhpcy5vdXRwdXQ7XG59O1xuXG4vKipcbiAqIOmdnuWcp+e4ruODluODreODg+OCr+OBruS9nOaIkFxuICogQHBhcmFtIHshKEFycmF5LjxudW1iZXI+fFVpbnQ4QXJyYXkpfSBibG9ja0FycmF5IOODluODreODg+OCr+ODh+ODvOOCvyBieXRlIGFycmF5LlxuICogQHBhcmFtIHshYm9vbGVhbn0gaXNGaW5hbEJsb2NrIOacgOW+jOOBruODluODreODg+OCr+OBquOCieOBsHRydWUuXG4gKiBAcmV0dXJuIHshKEFycmF5LjxudW1iZXI+fFVpbnQ4QXJyYXkpfSDpnZ7lnKfnuK7jg5bjg63jg4Pjgq8gYnl0ZSBhcnJheS5cbiAqL1xuWmxpYi5SYXdEZWZsYXRlLnByb3RvdHlwZS5tYWtlTm9jb21wcmVzc0Jsb2NrID1cbmZ1bmN0aW9uKGJsb2NrQXJyYXksIGlzRmluYWxCbG9jaykge1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgdmFyIGJmaW5hbDtcbiAgLyoqIEB0eXBlIHtabGliLlJhd0RlZmxhdGUuQ29tcHJlc3Npb25UeXBlfSAqL1xuICB2YXIgYnR5cGU7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgbGVuO1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgdmFyIG5sZW47XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgaTtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciBpbDtcblxuICB2YXIgb3V0cHV0ID0gdGhpcy5vdXRwdXQ7XG4gIHZhciBvcCA9IHRoaXMub3A7XG5cbiAgLy8gZXhwYW5kIGJ1ZmZlclxuICBpZiAoVVNFX1RZUEVEQVJSQVkpIHtcbiAgICBvdXRwdXQgPSBuZXcgVWludDhBcnJheSh0aGlzLm91dHB1dC5idWZmZXIpO1xuICAgIHdoaWxlIChvdXRwdXQubGVuZ3RoIDw9IG9wICsgYmxvY2tBcnJheS5sZW5ndGggKyA1KSB7XG4gICAgICBvdXRwdXQgPSBuZXcgVWludDhBcnJheShvdXRwdXQubGVuZ3RoIDw8IDEpO1xuICAgIH1cbiAgICBvdXRwdXQuc2V0KHRoaXMub3V0cHV0KTtcbiAgfVxuXG4gIC8vIGhlYWRlclxuICBiZmluYWwgPSBpc0ZpbmFsQmxvY2sgPyAxIDogMDtcbiAgYnR5cGUgPSBabGliLlJhd0RlZmxhdGUuQ29tcHJlc3Npb25UeXBlLk5PTkU7XG4gIG91dHB1dFtvcCsrXSA9IChiZmluYWwpIHwgKGJ0eXBlIDw8IDEpO1xuXG4gIC8vIGxlbmd0aFxuICBsZW4gPSBibG9ja0FycmF5Lmxlbmd0aDtcbiAgbmxlbiA9ICh+bGVuICsgMHgxMDAwMCkgJiAweGZmZmY7XG4gIG91dHB1dFtvcCsrXSA9ICAgICAgICAgIGxlbiAmIDB4ZmY7XG4gIG91dHB1dFtvcCsrXSA9ICAobGVuID4+PiA4KSAmIDB4ZmY7XG4gIG91dHB1dFtvcCsrXSA9ICAgICAgICAgbmxlbiAmIDB4ZmY7XG4gIG91dHB1dFtvcCsrXSA9IChubGVuID4+PiA4KSAmIDB4ZmY7XG5cbiAgLy8gY29weSBidWZmZXJcbiAgaWYgKFVTRV9UWVBFREFSUkFZKSB7XG4gICAgIG91dHB1dC5zZXQoYmxvY2tBcnJheSwgb3ApO1xuICAgICBvcCArPSBibG9ja0FycmF5Lmxlbmd0aDtcbiAgICAgb3V0cHV0ID0gb3V0cHV0LnN1YmFycmF5KDAsIG9wKTtcbiAgfSBlbHNlIHtcbiAgICBmb3IgKGkgPSAwLCBpbCA9IGJsb2NrQXJyYXkubGVuZ3RoOyBpIDwgaWw7ICsraSkge1xuICAgICAgb3V0cHV0W29wKytdID0gYmxvY2tBcnJheVtpXTtcbiAgICB9XG4gICAgb3V0cHV0Lmxlbmd0aCA9IG9wO1xuICB9XG5cbiAgdGhpcy5vcCA9IG9wO1xuICB0aGlzLm91dHB1dCA9IG91dHB1dDtcblxuICByZXR1cm4gb3V0cHV0O1xufTtcblxuLyoqXG4gKiDlm7rlrprjg4/jg5Xjg57jg7Pjg5bjg63jg4Pjgq/jga7kvZzmiJBcbiAqIEBwYXJhbSB7IShBcnJheS48bnVtYmVyPnxVaW50OEFycmF5KX0gYmxvY2tBcnJheSDjg5bjg63jg4Pjgq/jg4fjg7zjgr8gYnl0ZSBhcnJheS5cbiAqIEBwYXJhbSB7IWJvb2xlYW59IGlzRmluYWxCbG9jayDmnIDlvozjga7jg5bjg63jg4Pjgq/jgarjgonjgbB0cnVlLlxuICogQHJldHVybiB7IShBcnJheS48bnVtYmVyPnxVaW50OEFycmF5KX0g5Zu65a6a44OP44OV44Oe44Oz56ym5Y+35YyW44OW44Ot44OD44KvIGJ5dGUgYXJyYXkuXG4gKi9cblpsaWIuUmF3RGVmbGF0ZS5wcm90b3R5cGUubWFrZUZpeGVkSHVmZm1hbkJsb2NrID1cbmZ1bmN0aW9uKGJsb2NrQXJyYXksIGlzRmluYWxCbG9jaykge1xuICAvKiogQHR5cGUge1psaWIuQml0U3RyZWFtfSAqL1xuICB2YXIgc3RyZWFtID0gbmV3IFpsaWIuQml0U3RyZWFtKFVTRV9UWVBFREFSUkFZID9cbiAgICBuZXcgVWludDhBcnJheSh0aGlzLm91dHB1dC5idWZmZXIpIDogdGhpcy5vdXRwdXQsIHRoaXMub3ApO1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgdmFyIGJmaW5hbDtcbiAgLyoqIEB0eXBlIHtabGliLlJhd0RlZmxhdGUuQ29tcHJlc3Npb25UeXBlfSAqL1xuICB2YXIgYnR5cGU7XG4gIC8qKiBAdHlwZSB7IShBcnJheS48bnVtYmVyPnxVaW50MTZBcnJheSl9ICovXG4gIHZhciBkYXRhO1xuXG4gIC8vIGhlYWRlclxuICBiZmluYWwgPSBpc0ZpbmFsQmxvY2sgPyAxIDogMDtcbiAgYnR5cGUgPSBabGliLlJhd0RlZmxhdGUuQ29tcHJlc3Npb25UeXBlLkZJWEVEO1xuXG4gIHN0cmVhbS53cml0ZUJpdHMoYmZpbmFsLCAxLCB0cnVlKTtcbiAgc3RyZWFtLndyaXRlQml0cyhidHlwZSwgMiwgdHJ1ZSk7XG5cbiAgZGF0YSA9IHRoaXMubHo3NyhibG9ja0FycmF5KTtcbiAgdGhpcy5maXhlZEh1ZmZtYW4oZGF0YSwgc3RyZWFtKTtcblxuICByZXR1cm4gc3RyZWFtLmZpbmlzaCgpO1xufTtcblxuLyoqXG4gKiDli5XnmoTjg4/jg5Xjg57jg7Pjg5bjg63jg4Pjgq/jga7kvZzmiJBcbiAqIEBwYXJhbSB7IShBcnJheS48bnVtYmVyPnxVaW50OEFycmF5KX0gYmxvY2tBcnJheSDjg5bjg63jg4Pjgq/jg4fjg7zjgr8gYnl0ZSBhcnJheS5cbiAqIEBwYXJhbSB7IWJvb2xlYW59IGlzRmluYWxCbG9jayDmnIDlvozjga7jg5bjg63jg4Pjgq/jgarjgonjgbB0cnVlLlxuICogQHJldHVybiB7IShBcnJheS48bnVtYmVyPnxVaW50OEFycmF5KX0g5YuV55qE44OP44OV44Oe44Oz56ym5Y+344OW44Ot44OD44KvIGJ5dGUgYXJyYXkuXG4gKi9cblpsaWIuUmF3RGVmbGF0ZS5wcm90b3R5cGUubWFrZUR5bmFtaWNIdWZmbWFuQmxvY2sgPVxuZnVuY3Rpb24oYmxvY2tBcnJheSwgaXNGaW5hbEJsb2NrKSB7XG4gIC8qKiBAdHlwZSB7WmxpYi5CaXRTdHJlYW19ICovXG4gIHZhciBzdHJlYW0gPSBuZXcgWmxpYi5CaXRTdHJlYW0oVVNFX1RZUEVEQVJSQVkgP1xuICAgIG5ldyBVaW50OEFycmF5KHRoaXMub3V0cHV0LmJ1ZmZlcikgOiB0aGlzLm91dHB1dCwgdGhpcy5vcCk7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgYmZpbmFsO1xuICAvKiogQHR5cGUge1psaWIuUmF3RGVmbGF0ZS5Db21wcmVzc2lvblR5cGV9ICovXG4gIHZhciBidHlwZTtcbiAgLyoqIEB0eXBlIHshKEFycmF5LjxudW1iZXI+fFVpbnQxNkFycmF5KX0gKi9cbiAgdmFyIGRhdGE7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgaGxpdDtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciBoZGlzdDtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciBoY2xlbjtcbiAgLyoqIEBjb25zdCBAdHlwZSB7QXJyYXkuPG51bWJlcj59ICovXG4gIHZhciBoY2xlbk9yZGVyID1cbiAgICAgICAgWzE2LCAxNywgMTgsIDAsIDgsIDcsIDksIDYsIDEwLCA1LCAxMSwgNCwgMTIsIDMsIDEzLCAyLCAxNCwgMSwgMTVdO1xuICAvKiogQHR5cGUgeyEoQXJyYXkuPG51bWJlcj58VWludDhBcnJheSl9ICovXG4gIHZhciBsaXRMZW5MZW5ndGhzO1xuICAvKiogQHR5cGUgeyEoQXJyYXkuPG51bWJlcj58VWludDE2QXJyYXkpfSAqL1xuICB2YXIgbGl0TGVuQ29kZXM7XG4gIC8qKiBAdHlwZSB7IShBcnJheS48bnVtYmVyPnxVaW50OEFycmF5KX0gKi9cbiAgdmFyIGRpc3RMZW5ndGhzO1xuICAvKiogQHR5cGUgeyEoQXJyYXkuPG51bWJlcj58VWludDE2QXJyYXkpfSAqL1xuICB2YXIgZGlzdENvZGVzO1xuICAvKiogQHR5cGUge3tcbiAgICogICBjb2RlczogIShBcnJheS48bnVtYmVyPnxVaW50MzJBcnJheSksXG4gICAqICAgZnJlcXM6ICEoQXJyYXkuPG51bWJlcj58VWludDhBcnJheSlcbiAgICogfX0gKi9cbiAgdmFyIHRyZWVTeW1ib2xzO1xuICAvKiogQHR5cGUgeyEoQXJyYXkuPG51bWJlcj58VWludDhBcnJheSl9ICovXG4gIHZhciB0cmVlTGVuZ3RocztcbiAgLyoqIEB0eXBlIHtBcnJheX0gKi9cbiAgdmFyIHRyYW5zTGVuZ3RocyA9IG5ldyBBcnJheSgxOSk7XG4gIC8qKiBAdHlwZSB7IShBcnJheS48bnVtYmVyPnxVaW50MTZBcnJheSl9ICovXG4gIHZhciB0cmVlQ29kZXM7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgY29kZTtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciBiaXRsZW47XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgaTtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciBpbDtcblxuICAvLyBoZWFkZXJcbiAgYmZpbmFsID0gaXNGaW5hbEJsb2NrID8gMSA6IDA7XG4gIGJ0eXBlID0gWmxpYi5SYXdEZWZsYXRlLkNvbXByZXNzaW9uVHlwZS5EWU5BTUlDO1xuXG4gIHN0cmVhbS53cml0ZUJpdHMoYmZpbmFsLCAxLCB0cnVlKTtcbiAgc3RyZWFtLndyaXRlQml0cyhidHlwZSwgMiwgdHJ1ZSk7XG5cbiAgZGF0YSA9IHRoaXMubHo3NyhibG9ja0FycmF5KTtcblxuICAvLyDjg6rjg4bjg6njg6vjg7vplbfjgZUsIOi3nembouOBruODj+ODleODnuODs+espuWPt+OBqOespuWPt+mVt+OBrueul+WHulxuICBsaXRMZW5MZW5ndGhzID0gdGhpcy5nZXRMZW5ndGhzXyh0aGlzLmZyZXFzTGl0TGVuLCAxNSk7XG4gIGxpdExlbkNvZGVzID0gdGhpcy5nZXRDb2Rlc0Zyb21MZW5ndGhzXyhsaXRMZW5MZW5ndGhzKTtcbiAgZGlzdExlbmd0aHMgPSB0aGlzLmdldExlbmd0aHNfKHRoaXMuZnJlcXNEaXN0LCA3KTtcbiAgZGlzdENvZGVzID0gdGhpcy5nZXRDb2Rlc0Zyb21MZW5ndGhzXyhkaXN0TGVuZ3Rocyk7XG5cbiAgLy8gSExJVCwgSERJU1Qg44Gu5rG65a6aXG4gIGZvciAoaGxpdCA9IDI4NjsgaGxpdCA+IDI1NyAmJiBsaXRMZW5MZW5ndGhzW2hsaXQgLSAxXSA9PT0gMDsgaGxpdC0tKSB7fVxuICBmb3IgKGhkaXN0ID0gMzA7IGhkaXN0ID4gMSAmJiBkaXN0TGVuZ3Roc1toZGlzdCAtIDFdID09PSAwOyBoZGlzdC0tKSB7fVxuXG4gIC8vIEhDTEVOXG4gIHRyZWVTeW1ib2xzID1cbiAgICB0aGlzLmdldFRyZWVTeW1ib2xzXyhobGl0LCBsaXRMZW5MZW5ndGhzLCBoZGlzdCwgZGlzdExlbmd0aHMpO1xuICB0cmVlTGVuZ3RocyA9IHRoaXMuZ2V0TGVuZ3Roc18odHJlZVN5bWJvbHMuZnJlcXMsIDcpO1xuICBmb3IgKGkgPSAwOyBpIDwgMTk7IGkrKykge1xuICAgIHRyYW5zTGVuZ3Roc1tpXSA9IHRyZWVMZW5ndGhzW2hjbGVuT3JkZXJbaV1dO1xuICB9XG4gIGZvciAoaGNsZW4gPSAxOTsgaGNsZW4gPiA0ICYmIHRyYW5zTGVuZ3Roc1toY2xlbiAtIDFdID09PSAwOyBoY2xlbi0tKSB7fVxuXG4gIHRyZWVDb2RlcyA9IHRoaXMuZ2V0Q29kZXNGcm9tTGVuZ3Roc18odHJlZUxlbmd0aHMpO1xuXG4gIC8vIOWHuuWKm1xuICBzdHJlYW0ud3JpdGVCaXRzKGhsaXQgLSAyNTcsIDUsIHRydWUpO1xuICBzdHJlYW0ud3JpdGVCaXRzKGhkaXN0IC0gMSwgNSwgdHJ1ZSk7XG4gIHN0cmVhbS53cml0ZUJpdHMoaGNsZW4gLSA0LCA0LCB0cnVlKTtcbiAgZm9yIChpID0gMDsgaSA8IGhjbGVuOyBpKyspIHtcbiAgICBzdHJlYW0ud3JpdGVCaXRzKHRyYW5zTGVuZ3Roc1tpXSwgMywgdHJ1ZSk7XG4gIH1cblxuICAvLyDjg4Tjg6rjg7zjga7lh7rliptcbiAgZm9yIChpID0gMCwgaWwgPSB0cmVlU3ltYm9scy5jb2Rlcy5sZW5ndGg7IGkgPCBpbDsgaSsrKSB7XG4gICAgY29kZSA9IHRyZWVTeW1ib2xzLmNvZGVzW2ldO1xuXG4gICAgc3RyZWFtLndyaXRlQml0cyh0cmVlQ29kZXNbY29kZV0sIHRyZWVMZW5ndGhzW2NvZGVdLCB0cnVlKTtcblxuICAgIC8vIGV4dHJhIGJpdHNcbiAgICBpZiAoY29kZSA+PSAxNikge1xuICAgICAgaSsrO1xuICAgICAgc3dpdGNoIChjb2RlKSB7XG4gICAgICAgIGNhc2UgMTY6IGJpdGxlbiA9IDI7IGJyZWFrO1xuICAgICAgICBjYXNlIDE3OiBiaXRsZW4gPSAzOyBicmVhaztcbiAgICAgICAgY2FzZSAxODogYml0bGVuID0gNzsgYnJlYWs7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgdGhyb3cgJ2ludmFsaWQgY29kZTogJyArIGNvZGU7XG4gICAgICB9XG5cbiAgICAgIHN0cmVhbS53cml0ZUJpdHModHJlZVN5bWJvbHMuY29kZXNbaV0sIGJpdGxlbiwgdHJ1ZSk7XG4gICAgfVxuICB9XG5cbiAgdGhpcy5keW5hbWljSHVmZm1hbihcbiAgICBkYXRhLFxuICAgIFtsaXRMZW5Db2RlcywgbGl0TGVuTGVuZ3Roc10sXG4gICAgW2Rpc3RDb2RlcywgZGlzdExlbmd0aHNdLFxuICAgIHN0cmVhbVxuICApO1xuXG4gIHJldHVybiBzdHJlYW0uZmluaXNoKCk7XG59O1xuXG5cbi8qKlxuICog5YuV55qE44OP44OV44Oe44Oz56ym5Y+35YyWKOOCq+OCueOCv+ODoOODj+ODleODnuODs+ODhuODvOODluODqylcbiAqIEBwYXJhbSB7IShBcnJheS48bnVtYmVyPnxVaW50MTZBcnJheSl9IGRhdGFBcnJheSBMWjc3IOespuWPt+WMlua4iOOBvyBieXRlIGFycmF5LlxuICogQHBhcmFtIHshWmxpYi5CaXRTdHJlYW19IHN0cmVhbSDmm7jjgY3ovrzjgb/nlKjjg5Pjg4Pjg4jjgrnjg4jjg6rjg7zjg6AuXG4gKiBAcmV0dXJuIHshWmxpYi5CaXRTdHJlYW19IOODj+ODleODnuODs+espuWPt+WMlua4iOOBv+ODk+ODg+ODiOOCueODiOODquODvOODoOOCquODluOCuOOCp+OCr+ODiC5cbiAqL1xuWmxpYi5SYXdEZWZsYXRlLnByb3RvdHlwZS5keW5hbWljSHVmZm1hbiA9XG5mdW5jdGlvbihkYXRhQXJyYXksIGxpdExlbiwgZGlzdCwgc3RyZWFtKSB7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgaW5kZXg7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgbGVuZ3RoO1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgdmFyIGxpdGVyYWw7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgY29kZTtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciBsaXRMZW5Db2RlcztcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciBsaXRMZW5MZW5ndGhzO1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgdmFyIGRpc3RDb2RlcztcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciBkaXN0TGVuZ3RocztcblxuICBsaXRMZW5Db2RlcyA9IGxpdExlblswXTtcbiAgbGl0TGVuTGVuZ3RocyA9IGxpdExlblsxXTtcbiAgZGlzdENvZGVzID0gZGlzdFswXTtcbiAgZGlzdExlbmd0aHMgPSBkaXN0WzFdO1xuXG4gIC8vIOespuWPt+OCkiBCaXRTdHJlYW0g44Gr5pu444GN6L6844KT44Gn44GE44GPXG4gIGZvciAoaW5kZXggPSAwLCBsZW5ndGggPSBkYXRhQXJyYXkubGVuZ3RoOyBpbmRleCA8IGxlbmd0aDsgKytpbmRleCkge1xuICAgIGxpdGVyYWwgPSBkYXRhQXJyYXlbaW5kZXhdO1xuXG4gICAgLy8gbGl0ZXJhbCBvciBsZW5ndGhcbiAgICBzdHJlYW0ud3JpdGVCaXRzKGxpdExlbkNvZGVzW2xpdGVyYWxdLCBsaXRMZW5MZW5ndGhzW2xpdGVyYWxdLCB0cnVlKTtcblxuICAgIC8vIOmVt+OBleODu+i3nembouespuWPt1xuICAgIGlmIChsaXRlcmFsID4gMjU2KSB7XG4gICAgICAvLyBsZW5ndGggZXh0cmFcbiAgICAgIHN0cmVhbS53cml0ZUJpdHMoZGF0YUFycmF5WysraW5kZXhdLCBkYXRhQXJyYXlbKytpbmRleF0sIHRydWUpO1xuICAgICAgLy8gZGlzdGFuY2VcbiAgICAgIGNvZGUgPSBkYXRhQXJyYXlbKytpbmRleF07XG4gICAgICBzdHJlYW0ud3JpdGVCaXRzKGRpc3RDb2Rlc1tjb2RlXSwgZGlzdExlbmd0aHNbY29kZV0sIHRydWUpO1xuICAgICAgLy8gZGlzdGFuY2UgZXh0cmFcbiAgICAgIHN0cmVhbS53cml0ZUJpdHMoZGF0YUFycmF5WysraW5kZXhdLCBkYXRhQXJyYXlbKytpbmRleF0sIHRydWUpO1xuICAgIC8vIOe1guerr1xuICAgIH0gZWxzZSBpZiAobGl0ZXJhbCA9PT0gMjU2KSB7XG4gICAgICBicmVhaztcbiAgICB9XG4gIH1cblxuICByZXR1cm4gc3RyZWFtO1xufTtcblxuLyoqXG4gKiDlm7rlrprjg4/jg5Xjg57jg7PnrKblj7fljJZcbiAqIEBwYXJhbSB7IShBcnJheS48bnVtYmVyPnxVaW50MTZBcnJheSl9IGRhdGFBcnJheSBMWjc3IOespuWPt+WMlua4iOOBvyBieXRlIGFycmF5LlxuICogQHBhcmFtIHshWmxpYi5CaXRTdHJlYW19IHN0cmVhbSDmm7jjgY3ovrzjgb/nlKjjg5Pjg4Pjg4jjgrnjg4jjg6rjg7zjg6AuXG4gKiBAcmV0dXJuIHshWmxpYi5CaXRTdHJlYW19IOODj+ODleODnuODs+espuWPt+WMlua4iOOBv+ODk+ODg+ODiOOCueODiOODquODvOODoOOCquODluOCuOOCp+OCr+ODiC5cbiAqL1xuWmxpYi5SYXdEZWZsYXRlLnByb3RvdHlwZS5maXhlZEh1ZmZtYW4gPSBmdW5jdGlvbihkYXRhQXJyYXksIHN0cmVhbSkge1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgdmFyIGluZGV4O1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgdmFyIGxlbmd0aDtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciBsaXRlcmFsO1xuXG4gIC8vIOespuWPt+OCkiBCaXRTdHJlYW0g44Gr5pu444GN6L6844KT44Gn44GE44GPXG4gIGZvciAoaW5kZXggPSAwLCBsZW5ndGggPSBkYXRhQXJyYXkubGVuZ3RoOyBpbmRleCA8IGxlbmd0aDsgaW5kZXgrKykge1xuICAgIGxpdGVyYWwgPSBkYXRhQXJyYXlbaW5kZXhdO1xuXG4gICAgLy8g56ym5Y+344Gu5pu444GN6L6844G/XG4gICAgWmxpYi5CaXRTdHJlYW0ucHJvdG90eXBlLndyaXRlQml0cy5hcHBseShcbiAgICAgIHN0cmVhbSxcbiAgICAgIFpsaWIuUmF3RGVmbGF0ZS5GaXhlZEh1ZmZtYW5UYWJsZVtsaXRlcmFsXVxuICAgICk7XG5cbiAgICAvLyDplbfjgZXjg7vot53pm6LnrKblj7dcbiAgICBpZiAobGl0ZXJhbCA+IDB4MTAwKSB7XG4gICAgICAvLyBsZW5ndGggZXh0cmFcbiAgICAgIHN0cmVhbS53cml0ZUJpdHMoZGF0YUFycmF5WysraW5kZXhdLCBkYXRhQXJyYXlbKytpbmRleF0sIHRydWUpO1xuICAgICAgLy8gZGlzdGFuY2VcbiAgICAgIHN0cmVhbS53cml0ZUJpdHMoZGF0YUFycmF5WysraW5kZXhdLCA1KTtcbiAgICAgIC8vIGRpc3RhbmNlIGV4dHJhXG4gICAgICBzdHJlYW0ud3JpdGVCaXRzKGRhdGFBcnJheVsrK2luZGV4XSwgZGF0YUFycmF5WysraW5kZXhdLCB0cnVlKTtcbiAgICAvLyDntYLnq69cbiAgICB9IGVsc2UgaWYgKGxpdGVyYWwgPT09IDB4MTAwKSB7XG4gICAgICBicmVhaztcbiAgICB9XG4gIH1cblxuICByZXR1cm4gc3RyZWFtO1xufTtcblxuLyoqXG4gKiDjg57jg4Pjg4Hmg4XloLFcbiAqIEBwYXJhbSB7IW51bWJlcn0gbGVuZ3RoIOODnuODg+ODgeOBl+OBn+mVt+OBlS5cbiAqIEBwYXJhbSB7IW51bWJlcn0gYmFja3dhcmREaXN0YW5jZSDjg57jg4Pjg4HkvY3nva7jgajjga7ot53pm6IuXG4gKiBAY29uc3RydWN0b3JcbiAqL1xuWmxpYi5SYXdEZWZsYXRlLkx6NzdNYXRjaCA9IGZ1bmN0aW9uKGxlbmd0aCwgYmFja3dhcmREaXN0YW5jZSkge1xuICAvKiogQHR5cGUge251bWJlcn0gbWF0Y2ggbGVuZ3RoLiAqL1xuICB0aGlzLmxlbmd0aCA9IGxlbmd0aDtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9IGJhY2t3YXJkIGRpc3RhbmNlLiAqL1xuICB0aGlzLmJhY2t3YXJkRGlzdGFuY2UgPSBiYWNrd2FyZERpc3RhbmNlO1xufTtcblxuLyoqXG4gKiDplbfjgZXnrKblj7fjg4bjg7zjg5bjg6suXG4gKiBb44Kz44O844OJLCDmi6HlvLXjg5Pjg4Pjg4gsIOaLoeW8teODk+ODg+ODiOmVt10g44Gu6YWN5YiX44Go44Gq44Gj44Gm44GE44KLLlxuICogQGNvbnN0XG4gKiBAdHlwZSB7IShBcnJheS48bnVtYmVyPnxVaW50MzJBcnJheSl9XG4gKi9cblpsaWIuUmF3RGVmbGF0ZS5Mejc3TWF0Y2guTGVuZ3RoQ29kZVRhYmxlID0gKGZ1bmN0aW9uKHRhYmxlKSB7XG4gIHJldHVybiBVU0VfVFlQRURBUlJBWSA/IG5ldyBVaW50MzJBcnJheSh0YWJsZSkgOiB0YWJsZTtcbn0pKChmdW5jdGlvbigpIHtcbiAgLyoqIEB0eXBlIHshQXJyYXl9ICovXG4gIHZhciB0YWJsZSA9IFtdO1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgdmFyIGk7XG4gIC8qKiBAdHlwZSB7IUFycmF5LjxudW1iZXI+fSAqL1xuICB2YXIgYztcblxuICBmb3IgKGkgPSAzOyBpIDw9IDI1ODsgaSsrKSB7XG4gICAgYyA9IGNvZGUoaSk7XG4gICAgdGFibGVbaV0gPSAoY1syXSA8PCAyNCkgfCAoY1sxXSA8PCAxNikgfCBjWzBdO1xuICB9XG5cbiAgLyoqXG4gICAqIEBwYXJhbSB7bnVtYmVyfSBsZW5ndGggbHo3NyBsZW5ndGguXG4gICAqIEByZXR1cm4geyFBcnJheS48bnVtYmVyPn0gbHo3NyBjb2Rlcy5cbiAgICovXG4gIGZ1bmN0aW9uIGNvZGUobGVuZ3RoKSB7XG4gICAgc3dpdGNoICh0cnVlKSB7XG4gICAgICBjYXNlIChsZW5ndGggPT09IDMpOiByZXR1cm4gWzI1NywgbGVuZ3RoIC0gMywgMF07IGJyZWFrO1xuICAgICAgY2FzZSAobGVuZ3RoID09PSA0KTogcmV0dXJuIFsyNTgsIGxlbmd0aCAtIDQsIDBdOyBicmVhaztcbiAgICAgIGNhc2UgKGxlbmd0aCA9PT0gNSk6IHJldHVybiBbMjU5LCBsZW5ndGggLSA1LCAwXTsgYnJlYWs7XG4gICAgICBjYXNlIChsZW5ndGggPT09IDYpOiByZXR1cm4gWzI2MCwgbGVuZ3RoIC0gNiwgMF07IGJyZWFrO1xuICAgICAgY2FzZSAobGVuZ3RoID09PSA3KTogcmV0dXJuIFsyNjEsIGxlbmd0aCAtIDcsIDBdOyBicmVhaztcbiAgICAgIGNhc2UgKGxlbmd0aCA9PT0gOCk6IHJldHVybiBbMjYyLCBsZW5ndGggLSA4LCAwXTsgYnJlYWs7XG4gICAgICBjYXNlIChsZW5ndGggPT09IDkpOiByZXR1cm4gWzI2MywgbGVuZ3RoIC0gOSwgMF07IGJyZWFrO1xuICAgICAgY2FzZSAobGVuZ3RoID09PSAxMCk6IHJldHVybiBbMjY0LCBsZW5ndGggLSAxMCwgMF07IGJyZWFrO1xuICAgICAgY2FzZSAobGVuZ3RoIDw9IDEyKTogcmV0dXJuIFsyNjUsIGxlbmd0aCAtIDExLCAxXTsgYnJlYWs7XG4gICAgICBjYXNlIChsZW5ndGggPD0gMTQpOiByZXR1cm4gWzI2NiwgbGVuZ3RoIC0gMTMsIDFdOyBicmVhaztcbiAgICAgIGNhc2UgKGxlbmd0aCA8PSAxNik6IHJldHVybiBbMjY3LCBsZW5ndGggLSAxNSwgMV07IGJyZWFrO1xuICAgICAgY2FzZSAobGVuZ3RoIDw9IDE4KTogcmV0dXJuIFsyNjgsIGxlbmd0aCAtIDE3LCAxXTsgYnJlYWs7XG4gICAgICBjYXNlIChsZW5ndGggPD0gMjIpOiByZXR1cm4gWzI2OSwgbGVuZ3RoIC0gMTksIDJdOyBicmVhaztcbiAgICAgIGNhc2UgKGxlbmd0aCA8PSAyNik6IHJldHVybiBbMjcwLCBsZW5ndGggLSAyMywgMl07IGJyZWFrO1xuICAgICAgY2FzZSAobGVuZ3RoIDw9IDMwKTogcmV0dXJuIFsyNzEsIGxlbmd0aCAtIDI3LCAyXTsgYnJlYWs7XG4gICAgICBjYXNlIChsZW5ndGggPD0gMzQpOiByZXR1cm4gWzI3MiwgbGVuZ3RoIC0gMzEsIDJdOyBicmVhaztcbiAgICAgIGNhc2UgKGxlbmd0aCA8PSA0Mik6IHJldHVybiBbMjczLCBsZW5ndGggLSAzNSwgM107IGJyZWFrO1xuICAgICAgY2FzZSAobGVuZ3RoIDw9IDUwKTogcmV0dXJuIFsyNzQsIGxlbmd0aCAtIDQzLCAzXTsgYnJlYWs7XG4gICAgICBjYXNlIChsZW5ndGggPD0gNTgpOiByZXR1cm4gWzI3NSwgbGVuZ3RoIC0gNTEsIDNdOyBicmVhaztcbiAgICAgIGNhc2UgKGxlbmd0aCA8PSA2Nik6IHJldHVybiBbMjc2LCBsZW5ndGggLSA1OSwgM107IGJyZWFrO1xuICAgICAgY2FzZSAobGVuZ3RoIDw9IDgyKTogcmV0dXJuIFsyNzcsIGxlbmd0aCAtIDY3LCA0XTsgYnJlYWs7XG4gICAgICBjYXNlIChsZW5ndGggPD0gOTgpOiByZXR1cm4gWzI3OCwgbGVuZ3RoIC0gODMsIDRdOyBicmVhaztcbiAgICAgIGNhc2UgKGxlbmd0aCA8PSAxMTQpOiByZXR1cm4gWzI3OSwgbGVuZ3RoIC0gOTksIDRdOyBicmVhaztcbiAgICAgIGNhc2UgKGxlbmd0aCA8PSAxMzApOiByZXR1cm4gWzI4MCwgbGVuZ3RoIC0gMTE1LCA0XTsgYnJlYWs7XG4gICAgICBjYXNlIChsZW5ndGggPD0gMTYyKTogcmV0dXJuIFsyODEsIGxlbmd0aCAtIDEzMSwgNV07IGJyZWFrO1xuICAgICAgY2FzZSAobGVuZ3RoIDw9IDE5NCk6IHJldHVybiBbMjgyLCBsZW5ndGggLSAxNjMsIDVdOyBicmVhaztcbiAgICAgIGNhc2UgKGxlbmd0aCA8PSAyMjYpOiByZXR1cm4gWzI4MywgbGVuZ3RoIC0gMTk1LCA1XTsgYnJlYWs7XG4gICAgICBjYXNlIChsZW5ndGggPD0gMjU3KTogcmV0dXJuIFsyODQsIGxlbmd0aCAtIDIyNywgNV07IGJyZWFrO1xuICAgICAgY2FzZSAobGVuZ3RoID09PSAyNTgpOiByZXR1cm4gWzI4NSwgbGVuZ3RoIC0gMjU4LCAwXTsgYnJlYWs7XG4gICAgICBkZWZhdWx0OiB0aHJvdyAnaW52YWxpZCBsZW5ndGg6ICcgKyBsZW5ndGg7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHRhYmxlO1xufSkoKSk7XG5cbi8qKlxuICog6Led6Zui56ym5Y+344OG44O844OW44OrXG4gKiBAcGFyYW0geyFudW1iZXJ9IGRpc3Qg6Led6ZuiLlxuICogQHJldHVybiB7IUFycmF5LjxudW1iZXI+fSDjgrPjg7zjg4njgIHmi6HlvLXjg5Pjg4Pjg4jjgIHmi6HlvLXjg5Pjg4Pjg4jplbfjga7phY3liJcuXG4gKiBAcHJpdmF0ZVxuICovXG5abGliLlJhd0RlZmxhdGUuTHo3N01hdGNoLnByb3RvdHlwZS5nZXREaXN0YW5jZUNvZGVfID0gZnVuY3Rpb24oZGlzdCkge1xuICAvKiogQHR5cGUgeyFBcnJheS48bnVtYmVyPn0gZGlzdGFuY2UgY29kZSB0YWJsZS4gKi9cbiAgdmFyIHI7XG5cbiAgc3dpdGNoICh0cnVlKSB7XG4gICAgY2FzZSAoZGlzdCA9PT0gMSk6IHIgPSBbMCwgZGlzdCAtIDEsIDBdOyBicmVhaztcbiAgICBjYXNlIChkaXN0ID09PSAyKTogciA9IFsxLCBkaXN0IC0gMiwgMF07IGJyZWFrO1xuICAgIGNhc2UgKGRpc3QgPT09IDMpOiByID0gWzIsIGRpc3QgLSAzLCAwXTsgYnJlYWs7XG4gICAgY2FzZSAoZGlzdCA9PT0gNCk6IHIgPSBbMywgZGlzdCAtIDQsIDBdOyBicmVhaztcbiAgICBjYXNlIChkaXN0IDw9IDYpOiByID0gWzQsIGRpc3QgLSA1LCAxXTsgYnJlYWs7XG4gICAgY2FzZSAoZGlzdCA8PSA4KTogciA9IFs1LCBkaXN0IC0gNywgMV07IGJyZWFrO1xuICAgIGNhc2UgKGRpc3QgPD0gMTIpOiByID0gWzYsIGRpc3QgLSA5LCAyXTsgYnJlYWs7XG4gICAgY2FzZSAoZGlzdCA8PSAxNik6IHIgPSBbNywgZGlzdCAtIDEzLCAyXTsgYnJlYWs7XG4gICAgY2FzZSAoZGlzdCA8PSAyNCk6IHIgPSBbOCwgZGlzdCAtIDE3LCAzXTsgYnJlYWs7XG4gICAgY2FzZSAoZGlzdCA8PSAzMik6IHIgPSBbOSwgZGlzdCAtIDI1LCAzXTsgYnJlYWs7XG4gICAgY2FzZSAoZGlzdCA8PSA0OCk6IHIgPSBbMTAsIGRpc3QgLSAzMywgNF07IGJyZWFrO1xuICAgIGNhc2UgKGRpc3QgPD0gNjQpOiByID0gWzExLCBkaXN0IC0gNDksIDRdOyBicmVhaztcbiAgICBjYXNlIChkaXN0IDw9IDk2KTogciA9IFsxMiwgZGlzdCAtIDY1LCA1XTsgYnJlYWs7XG4gICAgY2FzZSAoZGlzdCA8PSAxMjgpOiByID0gWzEzLCBkaXN0IC0gOTcsIDVdOyBicmVhaztcbiAgICBjYXNlIChkaXN0IDw9IDE5Mik6IHIgPSBbMTQsIGRpc3QgLSAxMjksIDZdOyBicmVhaztcbiAgICBjYXNlIChkaXN0IDw9IDI1Nik6IHIgPSBbMTUsIGRpc3QgLSAxOTMsIDZdOyBicmVhaztcbiAgICBjYXNlIChkaXN0IDw9IDM4NCk6IHIgPSBbMTYsIGRpc3QgLSAyNTcsIDddOyBicmVhaztcbiAgICBjYXNlIChkaXN0IDw9IDUxMik6IHIgPSBbMTcsIGRpc3QgLSAzODUsIDddOyBicmVhaztcbiAgICBjYXNlIChkaXN0IDw9IDc2OCk6IHIgPSBbMTgsIGRpc3QgLSA1MTMsIDhdOyBicmVhaztcbiAgICBjYXNlIChkaXN0IDw9IDEwMjQpOiByID0gWzE5LCBkaXN0IC0gNzY5LCA4XTsgYnJlYWs7XG4gICAgY2FzZSAoZGlzdCA8PSAxNTM2KTogciA9IFsyMCwgZGlzdCAtIDEwMjUsIDldOyBicmVhaztcbiAgICBjYXNlIChkaXN0IDw9IDIwNDgpOiByID0gWzIxLCBkaXN0IC0gMTUzNywgOV07IGJyZWFrO1xuICAgIGNhc2UgKGRpc3QgPD0gMzA3Mik6IHIgPSBbMjIsIGRpc3QgLSAyMDQ5LCAxMF07IGJyZWFrO1xuICAgIGNhc2UgKGRpc3QgPD0gNDA5Nik6IHIgPSBbMjMsIGRpc3QgLSAzMDczLCAxMF07IGJyZWFrO1xuICAgIGNhc2UgKGRpc3QgPD0gNjE0NCk6IHIgPSBbMjQsIGRpc3QgLSA0MDk3LCAxMV07IGJyZWFrO1xuICAgIGNhc2UgKGRpc3QgPD0gODE5Mik6IHIgPSBbMjUsIGRpc3QgLSA2MTQ1LCAxMV07IGJyZWFrO1xuICAgIGNhc2UgKGRpc3QgPD0gMTIyODgpOiByID0gWzI2LCBkaXN0IC0gODE5MywgMTJdOyBicmVhaztcbiAgICBjYXNlIChkaXN0IDw9IDE2Mzg0KTogciA9IFsyNywgZGlzdCAtIDEyMjg5LCAxMl07IGJyZWFrO1xuICAgIGNhc2UgKGRpc3QgPD0gMjQ1NzYpOiByID0gWzI4LCBkaXN0IC0gMTYzODUsIDEzXTsgYnJlYWs7XG4gICAgY2FzZSAoZGlzdCA8PSAzMjc2OCk6IHIgPSBbMjksIGRpc3QgLSAyNDU3NywgMTNdOyBicmVhaztcbiAgICBkZWZhdWx0OiB0aHJvdyAnaW52YWxpZCBkaXN0YW5jZSc7XG4gIH1cblxuICByZXR1cm4gcjtcbn07XG5cbi8qKlxuICog44Oe44OD44OB5oOF5aCx44KSIExaNzcg56ym5Y+35YyW6YWN5YiX44Gn6L+U44GZLlxuICog44Gq44GK44CB44GT44GT44Gn44Gv5Lul5LiL44Gu5YaF6YOo5LuV5qeY44Gn56ym5Y+35YyW44GX44Gm44GE44KLXG4gKiBbIENPREUsIEVYVFJBLUJJVC1MRU4sIEVYVFJBLCBDT0RFLCBFWFRSQS1CSVQtTEVOLCBFWFRSQSBdXG4gKiBAcmV0dXJuIHshQXJyYXkuPG51bWJlcj59IExaNzcg56ym5Y+35YyWIGJ5dGUgYXJyYXkuXG4gKi9cblpsaWIuUmF3RGVmbGF0ZS5Mejc3TWF0Y2gucHJvdG90eXBlLnRvTHo3N0FycmF5ID0gZnVuY3Rpb24oKSB7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgbGVuZ3RoID0gdGhpcy5sZW5ndGg7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgZGlzdCA9IHRoaXMuYmFja3dhcmREaXN0YW5jZTtcbiAgLyoqIEB0eXBlIHtBcnJheX0gKi9cbiAgdmFyIGNvZGVBcnJheSA9IFtdO1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgdmFyIHBvcyA9IDA7XG4gIC8qKiBAdHlwZSB7IUFycmF5LjxudW1iZXI+fSAqL1xuICB2YXIgY29kZTtcblxuICAvLyBsZW5ndGhcbiAgY29kZSA9IFpsaWIuUmF3RGVmbGF0ZS5Mejc3TWF0Y2guTGVuZ3RoQ29kZVRhYmxlW2xlbmd0aF07XG4gIGNvZGVBcnJheVtwb3MrK10gPSBjb2RlICYgMHhmZmZmO1xuICBjb2RlQXJyYXlbcG9zKytdID0gKGNvZGUgPj4gMTYpICYgMHhmZjtcbiAgY29kZUFycmF5W3BvcysrXSA9IGNvZGUgPj4gMjQ7XG5cbiAgLy8gZGlzdGFuY2VcbiAgY29kZSA9IHRoaXMuZ2V0RGlzdGFuY2VDb2RlXyhkaXN0KTtcbiAgY29kZUFycmF5W3BvcysrXSA9IGNvZGVbMF07XG4gIGNvZGVBcnJheVtwb3MrK10gPSBjb2RlWzFdO1xuICBjb2RlQXJyYXlbcG9zKytdID0gY29kZVsyXTtcblxuICByZXR1cm4gY29kZUFycmF5O1xufTtcblxuLyoqXG4gKiBMWjc3IOWun+ijhVxuICogQHBhcmFtIHshKEFycmF5LjxudW1iZXI+fFVpbnQ4QXJyYXkpfSBkYXRhQXJyYXkgTFo3NyDnrKblj7fljJbjgZnjgovjg5DjgqTjg4jphY3liJcuXG4gKiBAcmV0dXJuIHshKEFycmF5LjxudW1iZXI+fFVpbnQxNkFycmF5KX0gTFo3NyDnrKblj7fljJbjgZfjgZ/phY3liJcuXG4gKi9cblpsaWIuUmF3RGVmbGF0ZS5wcm90b3R5cGUubHo3NyA9IGZ1bmN0aW9uKGRhdGFBcnJheSkge1xuICAvKiogQHR5cGUge251bWJlcn0gaW5wdXQgcG9zaXRpb24gKi9cbiAgdmFyIHBvc2l0aW9uO1xuICAvKiogQHR5cGUge251bWJlcn0gaW5wdXQgbGVuZ3RoICovXG4gIHZhciBsZW5ndGg7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSBsb29wIGNvdW50ZXIgKi9cbiAgdmFyIGk7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSBsb29wIGxpbWl0ZXIgKi9cbiAgdmFyIGlsO1xuICAvKiogQHR5cGUge251bWJlcn0gY2hhaW5lZC1oYXNoLXRhYmxlIGtleSAqL1xuICB2YXIgbWF0Y2hLZXk7XG4gIC8qKiBAdHlwZSB7T2JqZWN0LjxudW1iZXIsIEFycmF5LjxudW1iZXI+Pn0gY2hhaW5lZC1oYXNoLXRhYmxlICovXG4gIHZhciB0YWJsZSA9IHt9O1xuICAvKiogQGNvbnN0IEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciB3aW5kb3dTaXplID0gWmxpYi5SYXdEZWZsYXRlLldpbmRvd1NpemU7XG4gIC8qKiBAdHlwZSB7QXJyYXkuPG51bWJlcj59IG1hdGNoIGxpc3QgKi9cbiAgdmFyIG1hdGNoTGlzdDtcbiAgLyoqIEB0eXBlIHtabGliLlJhd0RlZmxhdGUuTHo3N01hdGNofSBsb25nZXN0IG1hdGNoICovXG4gIHZhciBsb25nZXN0TWF0Y2g7XG4gIC8qKiBAdHlwZSB7WmxpYi5SYXdEZWZsYXRlLkx6NzdNYXRjaH0gcHJldmlvdXMgbG9uZ2VzdCBtYXRjaCAqL1xuICB2YXIgcHJldk1hdGNoO1xuICAvKiogQHR5cGUgeyEoQXJyYXkuPG51bWJlcj58VWludDE2QXJyYXkpfSBsejc3IGJ1ZmZlciAqL1xuICB2YXIgbHo3N2J1ZiA9IFVTRV9UWVBFREFSUkFZID9cbiAgICBuZXcgVWludDE2QXJyYXkoZGF0YUFycmF5Lmxlbmd0aCAqIDIpIDogW107XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSBsejc3IG91dHB1dCBidWZmZXIgcG9pbnRlciAqL1xuICB2YXIgcG9zID0gMDtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9IGx6Nzcgc2tpcCBsZW5ndGggKi9cbiAgdmFyIHNraXBMZW5ndGggPSAwO1xuICAvKiogQHR5cGUgeyEoQXJyYXkuPG51bWJlcj58VWludDMyQXJyYXkpfSAqL1xuICB2YXIgZnJlcXNMaXRMZW4gPSBuZXcgKFVTRV9UWVBFREFSUkFZID8gVWludDMyQXJyYXkgOiBBcnJheSkoMjg2KTtcbiAgLyoqIEB0eXBlIHshKEFycmF5LjxudW1iZXI+fFVpbnQzMkFycmF5KX0gKi9cbiAgdmFyIGZyZXFzRGlzdCA9IG5ldyAoVVNFX1RZUEVEQVJSQVkgPyBVaW50MzJBcnJheSA6IEFycmF5KSgzMCk7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgbGF6eSA9IHRoaXMubGF6eTtcbiAgLyoqIEB0eXBlIHsqfSB0ZW1wb3JhcnkgdmFyaWFibGUgKi9cbiAgdmFyIHRtcDtcblxuICAvLyDliJ3mnJ/ljJZcbiAgaWYgKCFVU0VfVFlQRURBUlJBWSkge1xuICAgIGZvciAoaSA9IDA7IGkgPD0gMjg1OykgeyBmcmVxc0xpdExlbltpKytdID0gMDsgfVxuICAgIGZvciAoaSA9IDA7IGkgPD0gMjk7KSB7IGZyZXFzRGlzdFtpKytdID0gMDsgfVxuICB9XG4gIGZyZXFzTGl0TGVuWzI1Nl0gPSAxOyAvLyBFT0Ig44Gu5pyA5L2O5Ye654++5Zue5pWw44GvIDFcblxuICAvKipcbiAgICog44Oe44OD44OB44OH44O844K/44Gu5pu444GN6L6844G/XG4gICAqIEBwYXJhbSB7WmxpYi5SYXdEZWZsYXRlLkx6NzdNYXRjaH0gbWF0Y2ggTFo3NyBNYXRjaCBkYXRhLlxuICAgKiBAcGFyYW0geyFudW1iZXJ9IG9mZnNldCDjgrnjgq3jg4Pjg5fplovlp4vkvY3nva4o55u45a++5oyH5a6aKS5cbiAgICogQHByaXZhdGVcbiAgICovXG4gIGZ1bmN0aW9uIHdyaXRlTWF0Y2gobWF0Y2gsIG9mZnNldCkge1xuICAgIC8qKiBAdHlwZSB7QXJyYXkuPG51bWJlcj59ICovXG4gICAgdmFyIGx6NzdBcnJheSA9IG1hdGNoLnRvTHo3N0FycmF5KCk7XG4gICAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gICAgdmFyIGk7XG4gICAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gICAgdmFyIGlsO1xuXG4gICAgZm9yIChpID0gMCwgaWwgPSBsejc3QXJyYXkubGVuZ3RoOyBpIDwgaWw7ICsraSkge1xuICAgICAgbHo3N2J1Zltwb3MrK10gPSBsejc3QXJyYXlbaV07XG4gICAgfVxuICAgIGZyZXFzTGl0TGVuW2x6NzdBcnJheVswXV0rKztcbiAgICBmcmVxc0Rpc3RbbHo3N0FycmF5WzNdXSsrO1xuICAgIHNraXBMZW5ndGggPSBtYXRjaC5sZW5ndGggKyBvZmZzZXQgLSAxO1xuICAgIHByZXZNYXRjaCA9IG51bGw7XG4gIH1cblxuICAvLyBMWjc3IOespuWPt+WMllxuICBmb3IgKHBvc2l0aW9uID0gMCwgbGVuZ3RoID0gZGF0YUFycmF5Lmxlbmd0aDsgcG9zaXRpb24gPCBsZW5ndGg7ICsrcG9zaXRpb24pIHtcbiAgICAvLyDjg4/jg4Pjgrfjg6Xjgq3jg7zjga7kvZzmiJBcbiAgICBmb3IgKG1hdGNoS2V5ID0gMCwgaSA9IDAsIGlsID0gWmxpYi5SYXdEZWZsYXRlLkx6NzdNaW5MZW5ndGg7IGkgPCBpbDsgKytpKSB7XG4gICAgICBpZiAocG9zaXRpb24gKyBpID09PSBsZW5ndGgpIHtcbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgICBtYXRjaEtleSA9IChtYXRjaEtleSA8PCA4KSB8IGRhdGFBcnJheVtwb3NpdGlvbiArIGldO1xuICAgIH1cblxuICAgIC8vIOODhuODvOODluODq+OBjOacquWumue+qeOBoOOBo+OBn+OCieS9nOaIkOOBmeOCi1xuICAgIGlmICh0YWJsZVttYXRjaEtleV0gPT09IHZvaWQgMCkgeyB0YWJsZVttYXRjaEtleV0gPSBbXTsgfVxuICAgIG1hdGNoTGlzdCA9IHRhYmxlW21hdGNoS2V5XTtcblxuICAgIC8vIHNraXBcbiAgICBpZiAoc2tpcExlbmd0aC0tID4gMCkge1xuICAgICAgbWF0Y2hMaXN0LnB1c2gocG9zaXRpb24pO1xuICAgICAgY29udGludWU7XG4gICAgfVxuXG4gICAgLy8g44Oe44OD44OB44OG44O844OW44Or44Gu5pu05pawICjmnIDlpKfmiLvjgorot53pm6LjgpLotoXjgYjjgabjgYTjgovjgoLjga7jgpLliYrpmaTjgZnjgospXG4gICAgd2hpbGUgKG1hdGNoTGlzdC5sZW5ndGggPiAwICYmIHBvc2l0aW9uIC0gbWF0Y2hMaXN0WzBdID4gd2luZG93U2l6ZSkge1xuICAgICAgbWF0Y2hMaXN0LnNoaWZ0KCk7XG4gICAgfVxuXG4gICAgLy8g44OH44O844K/5pyr5bC+44Gn44Oe44OD44OB44GX44KI44GG44GM44Gq44GE5aC05ZCI44Gv44Gd44Gu44G+44G+5rWB44GX44GT44KAXG4gICAgaWYgKHBvc2l0aW9uICsgWmxpYi5SYXdEZWZsYXRlLkx6NzdNaW5MZW5ndGggPj0gbGVuZ3RoKSB7XG4gICAgICBpZiAocHJldk1hdGNoKSB7XG4gICAgICAgIHdyaXRlTWF0Y2gocHJldk1hdGNoLCAtMSk7XG4gICAgICB9XG5cbiAgICAgIGZvciAoaSA9IDAsIGlsID0gbGVuZ3RoIC0gcG9zaXRpb247IGkgPCBpbDsgKytpKSB7XG4gICAgICAgIHRtcCA9IGRhdGFBcnJheVtwb3NpdGlvbiArIGldO1xuICAgICAgICBsejc3YnVmW3BvcysrXSA9IHRtcDtcbiAgICAgICAgKytmcmVxc0xpdExlblt0bXBdO1xuICAgICAgfVxuICAgICAgYnJlYWs7XG4gICAgfVxuXG4gICAgLy8g44Oe44OD44OB5YCZ6KOc44GL44KJ5pyA6ZW344Gu44KC44Gu44KS5o6i44GZXG4gICAgaWYgKG1hdGNoTGlzdC5sZW5ndGggPiAwKSB7XG4gICAgICBsb25nZXN0TWF0Y2ggPSB0aGlzLnNlYXJjaExvbmdlc3RNYXRjaF8oZGF0YUFycmF5LCBwb3NpdGlvbiwgbWF0Y2hMaXN0KTtcblxuICAgICAgaWYgKHByZXZNYXRjaCkge1xuICAgICAgICAvLyDnj77lnKjjga7jg57jg4Pjg4Hjga7mlrnjgYzliY3lm57jga7jg57jg4Pjg4HjgojjgorjgoLplbfjgYRcbiAgICAgICAgaWYgKHByZXZNYXRjaC5sZW5ndGggPCBsb25nZXN0TWF0Y2gubGVuZ3RoKSB7XG4gICAgICAgICAgLy8gd3JpdGUgcHJldmlvdXMgbGl0ZXJhbFxuICAgICAgICAgIHRtcCA9IGRhdGFBcnJheVtwb3NpdGlvbiAtIDFdO1xuICAgICAgICAgIGx6NzdidWZbcG9zKytdID0gdG1wO1xuICAgICAgICAgICsrZnJlcXNMaXRMZW5bdG1wXTtcblxuICAgICAgICAgIC8vIHdyaXRlIGN1cnJlbnQgbWF0Y2hcbiAgICAgICAgICB3cml0ZU1hdGNoKGxvbmdlc3RNYXRjaCwgMCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gd3JpdGUgcHJldmlvdXMgbWF0Y2hcbiAgICAgICAgICB3cml0ZU1hdGNoKHByZXZNYXRjaCwgLTEpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKGxvbmdlc3RNYXRjaC5sZW5ndGggPCBsYXp5KSB7XG4gICAgICAgIHByZXZNYXRjaCA9IGxvbmdlc3RNYXRjaDtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHdyaXRlTWF0Y2gobG9uZ2VzdE1hdGNoLCAwKTtcbiAgICAgIH1cbiAgICAvLyDliY3lm57jg57jg4Pjg4HjgZfjgabjgYTjgabku4rlm57jg57jg4Pjg4HjgYzjgarjgYvjgaPjgZ/jgonliY3lm57jga7jgpLmjqHnlKhcbiAgICB9IGVsc2UgaWYgKHByZXZNYXRjaCkge1xuICAgICAgd3JpdGVNYXRjaChwcmV2TWF0Y2gsIC0xKTtcbiAgICB9IGVsc2Uge1xuICAgICAgdG1wID0gZGF0YUFycmF5W3Bvc2l0aW9uXTtcbiAgICAgIGx6NzdidWZbcG9zKytdID0gdG1wO1xuICAgICAgKytmcmVxc0xpdExlblt0bXBdO1xuICAgIH1cblxuICAgIG1hdGNoTGlzdC5wdXNoKHBvc2l0aW9uKTsgLy8g44Oe44OD44OB44OG44O844OW44Or44Gr54++5Zyo44Gu5L2N572u44KS5L+d5a2YXG4gIH1cblxuICAvLyDntYLnq6/lh6bnkIZcbiAgbHo3N2J1Zltwb3MrK10gPSAyNTY7XG4gIGZyZXFzTGl0TGVuWzI1Nl0rKztcbiAgdGhpcy5mcmVxc0xpdExlbiA9IGZyZXFzTGl0TGVuO1xuICB0aGlzLmZyZXFzRGlzdCA9IGZyZXFzRGlzdDtcblxuICByZXR1cm4gLyoqIEB0eXBlIHshKFVpbnQxNkFycmF5fEFycmF5LjxudW1iZXI+KX0gKi8gKFxuICAgIFVTRV9UWVBFREFSUkFZID8gIGx6NzdidWYuc3ViYXJyYXkoMCwgcG9zKSA6IGx6NzdidWZcbiAgKTtcbn07XG5cbi8qKlxuICog44Oe44OD44OB44GX44Gf5YCZ6KOc44Gu5Lit44GL44KJ5pyA6ZW35LiA6Ie044KS5o6i44GZXG4gKiBAcGFyYW0geyFPYmplY3R9IGRhdGEgcGxhaW4gZGF0YSBieXRlIGFycmF5LlxuICogQHBhcmFtIHshbnVtYmVyfSBwb3NpdGlvbiBwbGFpbiBkYXRhIGJ5dGUgYXJyYXkgcG9zaXRpb24uXG4gKiBAcGFyYW0geyFBcnJheS48bnVtYmVyPn0gbWF0Y2hMaXN0IOWAmeijnOOBqOOBquOCi+S9jee9ruOBrumFjeWIly5cbiAqIEByZXR1cm4geyFabGliLlJhd0RlZmxhdGUuTHo3N01hdGNofSDmnIDplbfjgYvjgaTmnIDnn63ot53pm6Ljga7jg57jg4Pjg4Hjgqrjg5bjgrjjgqfjgq/jg4guXG4gKiBAcHJpdmF0ZVxuICovXG5abGliLlJhd0RlZmxhdGUucHJvdG90eXBlLnNlYXJjaExvbmdlc3RNYXRjaF8gPVxuZnVuY3Rpb24oZGF0YSwgcG9zaXRpb24sIG1hdGNoTGlzdCkge1xuICB2YXIgbWF0Y2gsXG4gICAgICBjdXJyZW50TWF0Y2gsXG4gICAgICBtYXRjaE1heCA9IDAsIG1hdGNoTGVuZ3RoLFxuICAgICAgaSwgaiwgbCwgZGwgPSBkYXRhLmxlbmd0aDtcblxuICAvLyDlgJnoo5zjgpLlvozjgo3jgYvjgokgMSDjgaTjgZrjgaTntZ7jgorovrzjgpPjgafjgobjgY9cbiAgcGVybWF0Y2g6XG4gIGZvciAoaSA9IDAsIGwgPSBtYXRjaExpc3QubGVuZ3RoOyBpIDwgbDsgaSsrKSB7XG4gICAgbWF0Y2ggPSBtYXRjaExpc3RbbCAtIGkgLSAxXTtcbiAgICBtYXRjaExlbmd0aCA9IFpsaWIuUmF3RGVmbGF0ZS5Mejc3TWluTGVuZ3RoO1xuXG4gICAgLy8g5YmN5Zue44G+44Gn44Gu5pyA6ZW35LiA6Ie044KS5pyr5bC+44GL44KJ5LiA6Ie05qSc57Si44GZ44KLXG4gICAgaWYgKG1hdGNoTWF4ID4gWmxpYi5SYXdEZWZsYXRlLkx6NzdNaW5MZW5ndGgpIHtcbiAgICAgIGZvciAoaiA9IG1hdGNoTWF4OyBqID4gWmxpYi5SYXdEZWZsYXRlLkx6NzdNaW5MZW5ndGg7IGotLSkge1xuICAgICAgICBpZiAoZGF0YVttYXRjaCArIGogLSAxXSAhPT0gZGF0YVtwb3NpdGlvbiArIGogLSAxXSkge1xuICAgICAgICAgIGNvbnRpbnVlIHBlcm1hdGNoO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBtYXRjaExlbmd0aCA9IG1hdGNoTWF4O1xuICAgIH1cblxuICAgIC8vIOacgOmVt+S4gOiHtOaOoue0olxuICAgIHdoaWxlIChtYXRjaExlbmd0aCA8IFpsaWIuUmF3RGVmbGF0ZS5Mejc3TWF4TGVuZ3RoICYmXG4gICAgICAgICAgIHBvc2l0aW9uICsgbWF0Y2hMZW5ndGggPCBkbCAmJlxuICAgICAgICAgICBkYXRhW21hdGNoICsgbWF0Y2hMZW5ndGhdID09PSBkYXRhW3Bvc2l0aW9uICsgbWF0Y2hMZW5ndGhdKSB7XG4gICAgICArK21hdGNoTGVuZ3RoO1xuICAgIH1cblxuICAgIC8vIOODnuODg+ODgemVt+OBjOWQjOOBmOWgtOWQiOOBr+W+jOaWueOCkuWEquWFiFxuICAgIGlmIChtYXRjaExlbmd0aCA+IG1hdGNoTWF4KSB7XG4gICAgICBjdXJyZW50TWF0Y2ggPSBtYXRjaDtcbiAgICAgIG1hdGNoTWF4ID0gbWF0Y2hMZW5ndGg7XG4gICAgfVxuXG4gICAgLy8g5pyA6ZW344GM56K65a6a44GX44Gf44KJ5b6M44Gu5Yem55CG44Gv55yB55WlXG4gICAgaWYgKG1hdGNoTGVuZ3RoID09PSBabGliLlJhd0RlZmxhdGUuTHo3N01heExlbmd0aCkge1xuICAgICAgYnJlYWs7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIG5ldyBabGliLlJhd0RlZmxhdGUuTHo3N01hdGNoKG1hdGNoTWF4LCBwb3NpdGlvbiAtIGN1cnJlbnRNYXRjaCk7XG59O1xuXG4vKipcbiAqIFRyZWUtVHJhbnNtaXQgU3ltYm9scyDjga7nrpflh7pcbiAqIHJlZmVyZW5jZTogUHVUVFkgRGVmbGF0ZSBpbXBsZW1lbnRhdGlvblxuICogQHBhcmFtIHtudW1iZXJ9IGhsaXQgSExJVC5cbiAqIEBwYXJhbSB7IShBcnJheS48bnVtYmVyPnxVaW50OEFycmF5KX0gbGl0bGVuTGVuZ3RocyDjg6rjg4bjg6njg6vjgajplbfjgZXnrKblj7fjga7nrKblj7fplbfphY3liJcuXG4gKiBAcGFyYW0ge251bWJlcn0gaGRpc3QgSERJU1QuXG4gKiBAcGFyYW0geyEoQXJyYXkuPG51bWJlcj58VWludDhBcnJheSl9IGRpc3RMZW5ndGhzIOi3nembouespuWPt+OBruespuWPt+mVt+mFjeWIly5cbiAqIEByZXR1cm4ge3tcbiAqICAgY29kZXM6ICEoQXJyYXkuPG51bWJlcj58VWludDMyQXJyYXkpLFxuICogICBmcmVxczogIShBcnJheS48bnVtYmVyPnxVaW50OEFycmF5KVxuICogfX0gVHJlZS1UcmFuc21pdCBTeW1ib2xzLlxuICovXG5abGliLlJhd0RlZmxhdGUucHJvdG90eXBlLmdldFRyZWVTeW1ib2xzXyA9XG5mdW5jdGlvbihobGl0LCBsaXRsZW5MZW5ndGhzLCBoZGlzdCwgZGlzdExlbmd0aHMpIHtcbiAgdmFyIHNyYyA9IG5ldyAoVVNFX1RZUEVEQVJSQVkgPyBVaW50MzJBcnJheSA6IEFycmF5KShobGl0ICsgaGRpc3QpLFxuICAgICAgaSwgaiwgcnVuTGVuZ3RoLCBsLFxuICAgICAgcmVzdWx0ID0gbmV3IChVU0VfVFlQRURBUlJBWSA/IFVpbnQzMkFycmF5IDogQXJyYXkpKDI4NiArIDMwKSxcbiAgICAgIG5SZXN1bHQsXG4gICAgICBycHQsXG4gICAgICBmcmVxcyA9IG5ldyAoVVNFX1RZUEVEQVJSQVkgPyBVaW50OEFycmF5IDogQXJyYXkpKDE5KTtcblxuICBqID0gMDtcbiAgZm9yIChpID0gMDsgaSA8IGhsaXQ7IGkrKykge1xuICAgIHNyY1tqKytdID0gbGl0bGVuTGVuZ3Roc1tpXTtcbiAgfVxuICBmb3IgKGkgPSAwOyBpIDwgaGRpc3Q7IGkrKykge1xuICAgIHNyY1tqKytdID0gZGlzdExlbmd0aHNbaV07XG4gIH1cblxuICAvLyDliJ3mnJ/ljJZcbiAgaWYgKCFVU0VfVFlQRURBUlJBWSkge1xuICAgIGZvciAoaSA9IDAsIGwgPSBmcmVxcy5sZW5ndGg7IGkgPCBsOyArK2kpIHtcbiAgICAgIGZyZXFzW2ldID0gMDtcbiAgICB9XG4gIH1cblxuICAvLyDnrKblj7fljJZcbiAgblJlc3VsdCA9IDA7XG4gIGZvciAoaSA9IDAsIGwgPSBzcmMubGVuZ3RoOyBpIDwgbDsgaSArPSBqKSB7XG4gICAgLy8gUnVuIExlbmd0aCBFbmNvZGluZ1xuICAgIGZvciAoaiA9IDE7IGkgKyBqIDwgbCAmJiBzcmNbaSArIGpdID09PSBzcmNbaV07ICsraikge31cblxuICAgIHJ1bkxlbmd0aCA9IGo7XG5cbiAgICBpZiAoc3JjW2ldID09PSAwKSB7XG4gICAgICAvLyAwIOOBrue5sOOCiui/lOOBl+OBjCAzIOWbnuacqua6gOOBquOCieOBsOOBneOBruOBvuOBvlxuICAgICAgaWYgKHJ1bkxlbmd0aCA8IDMpIHtcbiAgICAgICAgd2hpbGUgKHJ1bkxlbmd0aC0tID4gMCkge1xuICAgICAgICAgIHJlc3VsdFtuUmVzdWx0KytdID0gMDtcbiAgICAgICAgICBmcmVxc1swXSsrO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB3aGlsZSAocnVuTGVuZ3RoID4gMCkge1xuICAgICAgICAgIC8vIOe5sOOCiui/lOOBl+OBr+acgOWkpyAxMzgg44G+44Gn44Gq44Gu44Gn5YiH44KK6Kmw44KB44KLXG4gICAgICAgICAgcnB0ID0gKHJ1bkxlbmd0aCA8IDEzOCA/IHJ1bkxlbmd0aCA6IDEzOCk7XG5cbiAgICAgICAgICBpZiAocnB0ID4gcnVuTGVuZ3RoIC0gMyAmJiBycHQgPCBydW5MZW5ndGgpIHtcbiAgICAgICAgICAgIHJwdCA9IHJ1bkxlbmd0aCAtIDM7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gMy0xMCDlm54gLT4gMTdcbiAgICAgICAgICBpZiAocnB0IDw9IDEwKSB7XG4gICAgICAgICAgICByZXN1bHRbblJlc3VsdCsrXSA9IDE3O1xuICAgICAgICAgICAgcmVzdWx0W25SZXN1bHQrK10gPSBycHQgLSAzO1xuICAgICAgICAgICAgZnJlcXNbMTddKys7XG4gICAgICAgICAgLy8gMTEtMTM4IOWbniAtPiAxOFxuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICByZXN1bHRbblJlc3VsdCsrXSA9IDE4O1xuICAgICAgICAgICAgcmVzdWx0W25SZXN1bHQrK10gPSBycHQgLSAxMTtcbiAgICAgICAgICAgIGZyZXFzWzE4XSsrO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIHJ1bkxlbmd0aCAtPSBycHQ7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgcmVzdWx0W25SZXN1bHQrK10gPSBzcmNbaV07XG4gICAgICBmcmVxc1tzcmNbaV1dKys7XG4gICAgICBydW5MZW5ndGgtLTtcblxuICAgICAgLy8g57mw44KK6L+U44GX5Zue5pWw44GMM+Wbnuacqua6gOOBquOCieOBsOODqeODs+ODrOODs+OCsOOCueespuWPt+OBr+imgeOCieOBquOBhFxuICAgICAgaWYgKHJ1bkxlbmd0aCA8IDMpIHtcbiAgICAgICAgd2hpbGUgKHJ1bkxlbmd0aC0tID4gMCkge1xuICAgICAgICAgIHJlc3VsdFtuUmVzdWx0KytdID0gc3JjW2ldO1xuICAgICAgICAgIGZyZXFzW3NyY1tpXV0rKztcbiAgICAgICAgfVxuICAgICAgLy8gMyDlm57ku6XkuIrjgarjgonjgbDjg6njg7Pjg6zjg7PjgrDjgrnnrKblj7fljJZcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHdoaWxlIChydW5MZW5ndGggPiAwKSB7XG4gICAgICAgICAgLy8gcnVuTGVuZ3Ro44KSIDMtNiDjgafliIblibJcbiAgICAgICAgICBycHQgPSAocnVuTGVuZ3RoIDwgNiA/IHJ1bkxlbmd0aCA6IDYpO1xuXG4gICAgICAgICAgaWYgKHJwdCA+IHJ1bkxlbmd0aCAtIDMgJiYgcnB0IDwgcnVuTGVuZ3RoKSB7XG4gICAgICAgICAgICBycHQgPSBydW5MZW5ndGggLSAzO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIHJlc3VsdFtuUmVzdWx0KytdID0gMTY7XG4gICAgICAgICAgcmVzdWx0W25SZXN1bHQrK10gPSBycHQgLSAzO1xuICAgICAgICAgIGZyZXFzWzE2XSsrO1xuXG4gICAgICAgICAgcnVuTGVuZ3RoIC09IHJwdDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiB7XG4gICAgY29kZXM6XG4gICAgICBVU0VfVFlQRURBUlJBWSA/IHJlc3VsdC5zdWJhcnJheSgwLCBuUmVzdWx0KSA6IHJlc3VsdC5zbGljZSgwLCBuUmVzdWx0KSxcbiAgICBmcmVxczogZnJlcXNcbiAgfTtcbn07XG5cbi8qKlxuICog44OP44OV44Oe44Oz56ym5Y+344Gu6ZW344GV44KS5Y+W5b6X44GZ44KLXG4gKiBAcGFyYW0geyEoQXJyYXkuPG51bWJlcj58VWludDhBcnJheXxVaW50MzJBcnJheSl9IGZyZXFzIOWHuuePvuOCq+OCpuODs+ODiC5cbiAqIEBwYXJhbSB7bnVtYmVyfSBsaW1pdCDnrKblj7fplbfjga7liLbpmZAuXG4gKiBAcmV0dXJuIHshKEFycmF5LjxudW1iZXI+fFVpbnQ4QXJyYXkpfSDnrKblj7fplbfphY3liJcuXG4gKiBAcHJpdmF0ZVxuICovXG5abGliLlJhd0RlZmxhdGUucHJvdG90eXBlLmdldExlbmd0aHNfID0gZnVuY3Rpb24oZnJlcXMsIGxpbWl0KSB7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgblN5bWJvbHMgPSBmcmVxcy5sZW5ndGg7XG4gIC8qKiBAdHlwZSB7WmxpYi5IZWFwfSAqL1xuICB2YXIgaGVhcCA9IG5ldyBabGliLkhlYXAoMiAqIFpsaWIuUmF3RGVmbGF0ZS5IVUZNQVgpO1xuICAvKiogQHR5cGUgeyEoQXJyYXkuPG51bWJlcj58VWludDhBcnJheSl9ICovXG4gIHZhciBsZW5ndGggPSBuZXcgKFVTRV9UWVBFREFSUkFZID8gVWludDhBcnJheSA6IEFycmF5KShuU3ltYm9scyk7XG4gIC8qKiBAdHlwZSB7QXJyYXl9ICovXG4gIHZhciBub2RlcztcbiAgLyoqIEB0eXBlIHshKEFycmF5LjxudW1iZXI+fFVpbnQzMkFycmF5KX0gKi9cbiAgdmFyIHZhbHVlcztcbiAgLyoqIEB0eXBlIHshKEFycmF5LjxudW1iZXI+fFVpbnQ4QXJyYXkpfSAqL1xuICB2YXIgY29kZUxlbmd0aDtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciBpO1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgdmFyIGlsO1xuXG4gIC8vIOmFjeWIl+OBruWIneacn+WMllxuICBpZiAoIVVTRV9UWVBFREFSUkFZKSB7XG4gICAgZm9yIChpID0gMDsgaSA8IG5TeW1ib2xzOyBpKyspIHtcbiAgICAgIGxlbmd0aFtpXSA9IDA7XG4gICAgfVxuICB9XG5cbiAgLy8g44OS44O844OX44Gu5qeL56+JXG4gIGZvciAoaSA9IDA7IGkgPCBuU3ltYm9sczsgKytpKSB7XG4gICAgaWYgKGZyZXFzW2ldID4gMCkge1xuICAgICAgaGVhcC5wdXNoKGksIGZyZXFzW2ldKTtcbiAgICB9XG4gIH1cbiAgbm9kZXMgPSBuZXcgQXJyYXkoaGVhcC5sZW5ndGggLyAyKTtcbiAgdmFsdWVzID0gbmV3IChVU0VfVFlQRURBUlJBWSA/IFVpbnQzMkFycmF5IDogQXJyYXkpKGhlYXAubGVuZ3RoIC8gMik7XG5cbiAgLy8g6Z2eIDAg44Gu6KaB57Sg44GM5LiA44Gk44Gg44GR44Gg44Gj44Gf5aC05ZCI44Gv44CB44Gd44Gu44K344Oz44Oc44Or44Gr56ym5Y+36ZW3IDEg44KS5Ymy44KK5b2T44Gm44Gm57WC5LqGXG4gIGlmIChub2Rlcy5sZW5ndGggPT09IDEpIHtcbiAgICBsZW5ndGhbaGVhcC5wb3AoKS5pbmRleF0gPSAxO1xuICAgIHJldHVybiBsZW5ndGg7XG4gIH1cblxuICAvLyBSZXZlcnNlIFBhY2thZ2UgTWVyZ2UgQWxnb3JpdGhtIOOBq+OCiOOCiyBDYW5vbmljYWwgSHVmZm1hbiBDb2RlIOOBruespuWPt+mVt+axuuWumlxuICBmb3IgKGkgPSAwLCBpbCA9IGhlYXAubGVuZ3RoIC8gMjsgaSA8IGlsOyArK2kpIHtcbiAgICBub2Rlc1tpXSA9IGhlYXAucG9wKCk7XG4gICAgdmFsdWVzW2ldID0gbm9kZXNbaV0udmFsdWU7XG4gIH1cbiAgY29kZUxlbmd0aCA9IHRoaXMucmV2ZXJzZVBhY2thZ2VNZXJnZV8odmFsdWVzLCB2YWx1ZXMubGVuZ3RoLCBsaW1pdCk7XG5cbiAgZm9yIChpID0gMCwgaWwgPSBub2Rlcy5sZW5ndGg7IGkgPCBpbDsgKytpKSB7XG4gICAgbGVuZ3RoW25vZGVzW2ldLmluZGV4XSA9IGNvZGVMZW5ndGhbaV07XG4gIH1cblxuICByZXR1cm4gbGVuZ3RoO1xufTtcblxuLyoqXG4gKiBSZXZlcnNlIFBhY2thZ2UgTWVyZ2UgQWxnb3JpdGhtLlxuICogQHBhcmFtIHshKEFycmF5LjxudW1iZXI+fFVpbnQzMkFycmF5KX0gZnJlcXMgc29ydGVkIHByb2JhYmlsaXR5LlxuICogQHBhcmFtIHtudW1iZXJ9IHN5bWJvbHMgbnVtYmVyIG9mIHN5bWJvbHMuXG4gKiBAcGFyYW0ge251bWJlcn0gbGltaXQgY29kZSBsZW5ndGggbGltaXQuXG4gKiBAcmV0dXJuIHshKEFycmF5LjxudW1iZXI+fFVpbnQ4QXJyYXkpfSBjb2RlIGxlbmd0aHMuXG4gKi9cblpsaWIuUmF3RGVmbGF0ZS5wcm90b3R5cGUucmV2ZXJzZVBhY2thZ2VNZXJnZV8gPSBmdW5jdGlvbihmcmVxcywgc3ltYm9scywgbGltaXQpIHtcbiAgLyoqIEB0eXBlIHshKEFycmF5LjxudW1iZXI+fFVpbnQxNkFycmF5KX0gKi9cbiAgdmFyIG1pbmltdW1Db3N0ID0gbmV3IChVU0VfVFlQRURBUlJBWSA/IFVpbnQxNkFycmF5IDogQXJyYXkpKGxpbWl0KTtcbiAgLyoqIEB0eXBlIHshKEFycmF5LjxudW1iZXI+fFVpbnQ4QXJyYXkpfSAqL1xuICB2YXIgZmxhZyA9IG5ldyAoVVNFX1RZUEVEQVJSQVkgPyBVaW50OEFycmF5IDogQXJyYXkpKGxpbWl0KTtcbiAgLyoqIEB0eXBlIHshKEFycmF5LjxudW1iZXI+fFVpbnQ4QXJyYXkpfSAqL1xuICB2YXIgY29kZUxlbmd0aCA9IG5ldyAoVVNFX1RZUEVEQVJSQVkgPyBVaW50OEFycmF5IDogQXJyYXkpKHN5bWJvbHMpO1xuICAvKiogQHR5cGUge0FycmF5fSAqL1xuICB2YXIgdmFsdWUgPSBuZXcgQXJyYXkobGltaXQpO1xuICAvKiogQHR5cGUge0FycmF5fSAqL1xuICB2YXIgdHlwZSAgPSBuZXcgQXJyYXkobGltaXQpO1xuICAvKiogQHR5cGUge0FycmF5LjxudW1iZXI+fSAqL1xuICB2YXIgY3VycmVudFBvc2l0aW9uID0gbmV3IEFycmF5KGxpbWl0KTtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciBleGNlc3MgPSAoMSA8PCBsaW1pdCkgLSBzeW1ib2xzO1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgdmFyIGhhbGYgPSAoMSA8PCAobGltaXQgLSAxKSk7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgaTtcbiAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gIHZhciBqO1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgdmFyIHQ7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSAqL1xuICB2YXIgd2VpZ2h0O1xuICAvKiogQHR5cGUge251bWJlcn0gKi9cbiAgdmFyIG5leHQ7XG5cbiAgLyoqXG4gICAqIEBwYXJhbSB7bnVtYmVyfSBqXG4gICAqL1xuICBmdW5jdGlvbiB0YWtlUGFja2FnZShqKSB7XG4gICAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gICAgdmFyIHggPSB0eXBlW2pdW2N1cnJlbnRQb3NpdGlvbltqXV07XG5cbiAgICBpZiAoeCA9PT0gc3ltYm9scykge1xuICAgICAgdGFrZVBhY2thZ2UoaisxKTtcbiAgICAgIHRha2VQYWNrYWdlKGorMSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC0tY29kZUxlbmd0aFt4XTtcbiAgICB9XG5cbiAgICArK2N1cnJlbnRQb3NpdGlvbltqXTtcbiAgfVxuXG4gIG1pbmltdW1Db3N0W2xpbWl0LTFdID0gc3ltYm9scztcblxuICBmb3IgKGogPSAwOyBqIDwgbGltaXQ7ICsraikge1xuICAgIGlmIChleGNlc3MgPCBoYWxmKSB7XG4gICAgICBmbGFnW2pdID0gMDtcbiAgICB9IGVsc2Uge1xuICAgICAgZmxhZ1tqXSA9IDE7XG4gICAgICBleGNlc3MgLT0gaGFsZjtcbiAgICB9XG4gICAgZXhjZXNzIDw8PSAxO1xuICAgIG1pbmltdW1Db3N0W2xpbWl0LTItal0gPSAobWluaW11bUNvc3RbbGltaXQtMS1qXSAvIDIgfCAwKSArIHN5bWJvbHM7XG4gIH1cbiAgbWluaW11bUNvc3RbMF0gPSBmbGFnWzBdO1xuXG4gIHZhbHVlWzBdID0gbmV3IEFycmF5KG1pbmltdW1Db3N0WzBdKTtcbiAgdHlwZVswXSAgPSBuZXcgQXJyYXkobWluaW11bUNvc3RbMF0pO1xuICBmb3IgKGogPSAxOyBqIDwgbGltaXQ7ICsraikge1xuICAgIGlmIChtaW5pbXVtQ29zdFtqXSA+IDIgKiBtaW5pbXVtQ29zdFtqLTFdICsgZmxhZ1tqXSkge1xuICAgICAgbWluaW11bUNvc3Rbal0gPSAyICogbWluaW11bUNvc3Rbai0xXSArIGZsYWdbal07XG4gICAgfVxuICAgIHZhbHVlW2pdID0gbmV3IEFycmF5KG1pbmltdW1Db3N0W2pdKTtcbiAgICB0eXBlW2pdICA9IG5ldyBBcnJheShtaW5pbXVtQ29zdFtqXSk7XG4gIH1cblxuICBmb3IgKGkgPSAwOyBpIDwgc3ltYm9sczsgKytpKSB7XG4gICAgY29kZUxlbmd0aFtpXSA9IGxpbWl0O1xuICB9XG5cbiAgZm9yICh0ID0gMDsgdCA8IG1pbmltdW1Db3N0W2xpbWl0LTFdOyArK3QpIHtcbiAgICB2YWx1ZVtsaW1pdC0xXVt0XSA9IGZyZXFzW3RdO1xuICAgIHR5cGVbbGltaXQtMV1bdF0gID0gdDtcbiAgfVxuXG4gIGZvciAoaSA9IDA7IGkgPCBsaW1pdDsgKytpKSB7XG4gICAgY3VycmVudFBvc2l0aW9uW2ldID0gMDtcbiAgfVxuICBpZiAoZmxhZ1tsaW1pdC0xXSA9PT0gMSkge1xuICAgIC0tY29kZUxlbmd0aFswXTtcbiAgICArK2N1cnJlbnRQb3NpdGlvbltsaW1pdC0xXTtcbiAgfVxuXG4gIGZvciAoaiA9IGxpbWl0LTI7IGogPj0gMDsgLS1qKSB7XG4gICAgaSA9IDA7XG4gICAgd2VpZ2h0ID0gMDtcbiAgICBuZXh0ID0gY3VycmVudFBvc2l0aW9uW2orMV07XG5cbiAgICBmb3IgKHQgPSAwOyB0IDwgbWluaW11bUNvc3Rbal07IHQrKykge1xuICAgICAgd2VpZ2h0ID0gdmFsdWVbaisxXVtuZXh0XSArIHZhbHVlW2orMV1bbmV4dCsxXTtcblxuICAgICAgaWYgKHdlaWdodCA+IGZyZXFzW2ldKSB7XG4gICAgICAgIHZhbHVlW2pdW3RdID0gd2VpZ2h0O1xuICAgICAgICB0eXBlW2pdW3RdID0gc3ltYm9scztcbiAgICAgICAgbmV4dCArPSAyO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdmFsdWVbal1bdF0gPSBmcmVxc1tpXTtcbiAgICAgICAgdHlwZVtqXVt0XSA9IGk7XG4gICAgICAgICsraTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjdXJyZW50UG9zaXRpb25bal0gPSAwO1xuICAgIGlmIChmbGFnW2pdID09PSAxKSB7XG4gICAgICB0YWtlUGFja2FnZShqKTtcbiAgICB9XG4gIH1cblxuICByZXR1cm4gY29kZUxlbmd0aDtcbn07XG5cbi8qKlxuICog56ym5Y+36ZW36YWN5YiX44GL44KJ44OP44OV44Oe44Oz56ym5Y+344KS5Y+W5b6X44GZ44KLXG4gKiByZWZlcmVuY2U6IFB1VFRZIERlZmxhdGUgaW1wbGVtZW50YXRpb25cbiAqIEBwYXJhbSB7IShBcnJheS48bnVtYmVyPnxVaW50OEFycmF5KX0gbGVuZ3RocyDnrKblj7fplbfphY3liJcuXG4gKiBAcmV0dXJuIHshKEFycmF5LjxudW1iZXI+fFVpbnQxNkFycmF5KX0g44OP44OV44Oe44Oz56ym5Y+36YWN5YiXLlxuICogQHByaXZhdGVcbiAqL1xuWmxpYi5SYXdEZWZsYXRlLnByb3RvdHlwZS5nZXRDb2Rlc0Zyb21MZW5ndGhzXyA9IGZ1bmN0aW9uKGxlbmd0aHMpIHtcbiAgdmFyIGNvZGVzID0gbmV3IChVU0VfVFlQRURBUlJBWSA/IFVpbnQxNkFycmF5IDogQXJyYXkpKGxlbmd0aHMubGVuZ3RoKSxcbiAgICAgIGNvdW50ID0gW10sXG4gICAgICBzdGFydENvZGUgPSBbXSxcbiAgICAgIGNvZGUgPSAwLCBpLCBpbCwgaiwgbTtcblxuICAvLyBDb3VudCB0aGUgY29kZXMgb2YgZWFjaCBsZW5ndGguXG4gIGZvciAoaSA9IDAsIGlsID0gbGVuZ3Rocy5sZW5ndGg7IGkgPCBpbDsgaSsrKSB7XG4gICAgY291bnRbbGVuZ3Roc1tpXV0gPSAoY291bnRbbGVuZ3Roc1tpXV0gfCAwKSArIDE7XG4gIH1cblxuICAvLyBEZXRlcm1pbmUgdGhlIHN0YXJ0aW5nIGNvZGUgZm9yIGVhY2ggbGVuZ3RoIGJsb2NrLlxuICBmb3IgKGkgPSAxLCBpbCA9IFpsaWIuUmF3RGVmbGF0ZS5NYXhDb2RlTGVuZ3RoOyBpIDw9IGlsOyBpKyspIHtcbiAgICBzdGFydENvZGVbaV0gPSBjb2RlO1xuICAgIGNvZGUgKz0gY291bnRbaV0gfCAwO1xuICAgIGNvZGUgPDw9IDE7XG4gIH1cblxuICAvLyBEZXRlcm1pbmUgdGhlIGNvZGUgZm9yIGVhY2ggc3ltYm9sLiBNaXJyb3JlZCwgb2YgY291cnNlLlxuICBmb3IgKGkgPSAwLCBpbCA9IGxlbmd0aHMubGVuZ3RoOyBpIDwgaWw7IGkrKykge1xuICAgIGNvZGUgPSBzdGFydENvZGVbbGVuZ3Roc1tpXV07XG4gICAgc3RhcnRDb2RlW2xlbmd0aHNbaV1dICs9IDE7XG4gICAgY29kZXNbaV0gPSAwO1xuXG4gICAgZm9yIChqID0gMCwgbSA9IGxlbmd0aHNbaV07IGogPCBtOyBqKyspIHtcbiAgICAgIGNvZGVzW2ldID0gKGNvZGVzW2ldIDw8IDEpIHwgKGNvZGUgJiAxKTtcbiAgICAgIGNvZGUgPj4+PSAxO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBjb2Rlcztcbn07XG5cbi8vIGVuZCBvZiBzY29wZVxufSk7XG5cbi8qIHZpbTpzZXQgZXhwYW5kdGFiIHRzPTIgc3c9MiB0dz04MDogKi9cbiIsIi8qKlxuICogQGZpbGVvdmVydmlldyBHWklQIChSRkMxOTUyKSDlrp/oo4UuXG4gKi9cbmdvb2cucHJvdmlkZSgnWmxpYi5HemlwJyk7XG5cbmdvb2cucmVxdWlyZSgnVVNFX1RZUEVEQVJSQVknKTtcbmdvb2cucmVxdWlyZSgnWmxpYi5DUkMzMicpO1xuZ29vZy5yZXF1aXJlKCdabGliLlJhd0RlZmxhdGUnKTtcblxuZ29vZy5zY29wZShmdW5jdGlvbigpIHtcblxuLyoqXG4gKiBAY29uc3RydWN0b3JcbiAqIEBwYXJhbSB7IShBcnJheXxVaW50OEFycmF5KX0gaW5wdXQgaW5wdXQgYnVmZmVyLlxuICogQHBhcmFtIHtPYmplY3Q9fSBvcHRfcGFyYW1zIG9wdGlvbiBwYXJhbWV0ZXJzLlxuICovXG5abGliLkd6aXAgPSBmdW5jdGlvbihpbnB1dCwgb3B0X3BhcmFtcykge1xuICAvKiogQHR5cGUgeyEoQXJyYXkuPG51bWJlcj58VWludDhBcnJheSl9IGlucHV0IGJ1ZmZlci4gKi9cbiAgdGhpcy5pbnB1dCA9IGlucHV0O1xuICAvKiogQHR5cGUge251bWJlcn0gaW5wdXQgYnVmZmVyIHBvaW50ZXIuICovXG4gIHRoaXMuaXAgPSAwO1xuICAvKiogQHR5cGUgeyEoQXJyYXkuPG51bWJlcj58VWludDhBcnJheSl9IG91dHB1dCBidWZmZXIuICovXG4gIHRoaXMub3V0cHV0O1xuICAvKiogQHR5cGUge251bWJlcn0gb3V0cHV0IGJ1ZmZlci4gKi9cbiAgdGhpcy5vcCA9IDA7XG4gIC8qKiBAdHlwZSB7IU9iamVjdH0gZmxhZ3Mgb3B0aW9uIGZsYWdzLiAqL1xuICB0aGlzLmZsYWdzID0ge307XG4gIC8qKiBAdHlwZSB7IXN0cmluZ30gZmlsZW5hbWUuICovXG4gIHRoaXMuZmlsZW5hbWU7XG4gIC8qKiBAdHlwZSB7IXN0cmluZ30gY29tbWVudC4gKi9cbiAgdGhpcy5jb21tZW50O1xuICAvKiogQHR5cGUgeyFPYmplY3R9IGRlZmxhdGUgb3B0aW9ucy4gKi9cbiAgdGhpcy5kZWZsYXRlT3B0aW9ucztcblxuICAvLyBvcHRpb24gcGFyYW1ldGVyc1xuICBpZiAob3B0X3BhcmFtcykge1xuICAgIGlmIChvcHRfcGFyYW1zWydmbGFncyddKSB7XG4gICAgICB0aGlzLmZsYWdzID0gb3B0X3BhcmFtc1snZmxhZ3MnXTtcbiAgICB9XG4gICAgaWYgKHR5cGVvZiBvcHRfcGFyYW1zWydmaWxlbmFtZSddID09PSAnc3RyaW5nJykge1xuICAgICAgdGhpcy5maWxlbmFtZSA9IG9wdF9wYXJhbXNbJ2ZpbGVuYW1lJ107XG4gICAgfVxuICAgIGlmICh0eXBlb2Ygb3B0X3BhcmFtc1snY29tbWVudCddID09PSAnc3RyaW5nJykge1xuICAgICAgdGhpcy5jb21tZW50ID0gb3B0X3BhcmFtc1snY29tbWVudCddO1xuICAgIH1cbiAgICBpZiAob3B0X3BhcmFtc1snZGVmbGF0ZU9wdGlvbnMnXSkge1xuICAgICAgdGhpcy5kZWZsYXRlT3B0aW9ucyA9IG9wdF9wYXJhbXNbJ2RlZmxhdGVPcHRpb25zJ107XG4gICAgfVxuICB9XG5cbiAgaWYgKCF0aGlzLmRlZmxhdGVPcHRpb25zKSB7XG4gICAgdGhpcy5kZWZsYXRlT3B0aW9ucyA9IHt9O1xuICB9XG59O1xuXG4vKipcbiAqIEB0eXBlIHtudW1iZXJ9XG4gKiBAY29uc3RcbiAqL1xuWmxpYi5HemlwLkRlZmF1bHRCdWZmZXJTaXplID0gMHg4MDAwO1xuXG4vKipcbiAqIGVuY29kZSBnemlwIG1lbWJlcnMuXG4gKiBAcmV0dXJuIHshKEFycmF5fFVpbnQ4QXJyYXkpfSBnemlwIGJpbmFyeSBhcnJheS5cbiAqL1xuWmxpYi5HemlwLnByb3RvdHlwZS5jb21wcmVzcyA9IGZ1bmN0aW9uKCkge1xuICAvKiogQHR5cGUge251bWJlcn0gZmxhZ3MuICovXG4gIHZhciBmbGc7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSBtb2RpZmljYXRpb24gdGltZS4gKi9cbiAgdmFyIG10aW1lO1xuICAvKiogQHR5cGUge251bWJlcn0gQ1JDLTE2IHZhbHVlIGZvciBGSENSQyBmbGFnLiAqL1xuICB2YXIgY3JjMTY7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSBDUkMtMzIgdmFsdWUgZm9yIHZlcmlmaWNhdGlvbi4gKi9cbiAgdmFyIGNyYzMyO1xuICAvKiogQHR5cGUgeyFabGliLlJhd0RlZmxhdGV9IHJhdyBkZWZsYXRlIG9iamVjdC4gKi9cbiAgdmFyIHJhd2RlZmxhdGU7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSBjaGFyYWN0ZXIgY29kZSAqL1xuICB2YXIgYztcbiAgLyoqIEB0eXBlIHtudW1iZXJ9IGxvb3AgY291bnRlci4gKi9cbiAgdmFyIGk7XG4gIC8qKiBAdHlwZSB7bnVtYmVyfSBsb29wIGxpbWl0ZXIuICovXG4gIHZhciBpbDtcbiAgLyoqIEB0eXBlIHshKEFycmF5fFVpbnQ4QXJyYXkpfSBvdXRwdXQgYnVmZmVyLiAqL1xuICB2YXIgb3V0cHV0ID1cbiAgICBuZXcgKFVTRV9UWVBFREFSUkFZID8gVWludDhBcnJheSA6IEFycmF5KShabGliLkd6aXAuRGVmYXVsdEJ1ZmZlclNpemUpO1xuICAvKiogQHR5cGUge251bWJlcn0gb3V0cHV0IGJ1ZmZlciBwb2ludGVyLiAqL1xuICB2YXIgb3AgPSAwO1xuXG4gIHZhciBpbnB1dCA9IHRoaXMuaW5wdXQ7XG4gIHZhciBpcCA9IHRoaXMuaXA7XG4gIHZhciBmaWxlbmFtZSA9IHRoaXMuZmlsZW5hbWU7XG4gIHZhciBjb21tZW50ID0gdGhpcy5jb21tZW50O1xuXG4gIC8vIGNoZWNrIHNpZ25hdHVyZVxuICBvdXRwdXRbb3ArK10gPSAweDFmO1xuICBvdXRwdXRbb3ArK10gPSAweDhiO1xuXG4gIC8vIGNoZWNrIGNvbXByZXNzaW9uIG1ldGhvZFxuICBvdXRwdXRbb3ArK10gPSA4OyAvKiBYWFg6IHVzZSBabGliIGNvbnN0ICovXG5cbiAgLy8gZmxhZ3NcbiAgZmxnID0gMDtcbiAgaWYgKHRoaXMuZmxhZ3NbJ2ZuYW1lJ10pICAgIGZsZyB8PSBabGliLkd6aXAuRmxhZ3NNYXNrLkZOQU1FO1xuICBpZiAodGhpcy5mbGFnc1snZmNvbW1lbnQnXSkgZmxnIHw9IFpsaWIuR3ppcC5GbGFnc01hc2suRkNPTU1FTlQ7XG4gIGlmICh0aGlzLmZsYWdzWydmaGNyYyddKSAgICBmbGcgfD0gWmxpYi5HemlwLkZsYWdzTWFzay5GSENSQztcbiAgLy8gWFhYOiBGVEVYVFxuICAvLyBYWFg6IEZFWFRSQVxuICBvdXRwdXRbb3ArK10gPSBmbGc7XG5cbiAgLy8gbW9kaWZpY2F0aW9uIHRpbWVcbiAgbXRpbWUgPSAoRGF0ZS5ub3cgPyBEYXRlLm5vdygpIDogK25ldyBEYXRlKCkpIC8gMTAwMCB8IDA7XG4gIG91dHB1dFtvcCsrXSA9IG10aW1lICAgICAgICAmIDB4ZmY7XG4gIG91dHB1dFtvcCsrXSA9IG10aW1lID4+PiAgOCAmIDB4ZmY7XG4gIG91dHB1dFtvcCsrXSA9IG10aW1lID4+PiAxNiAmIDB4ZmY7XG4gIG91dHB1dFtvcCsrXSA9IG10aW1lID4+PiAyNCAmIDB4ZmY7XG5cbiAgLy8gZXh0cmEgZmxhZ3NcbiAgb3V0cHV0W29wKytdID0gMDtcblxuICAvLyBvcGVyYXRpbmcgc3lzdGVtXG4gIG91dHB1dFtvcCsrXSA9IFpsaWIuR3ppcC5PcGVyYXRpbmdTeXN0ZW0uVU5LTk9XTjtcblxuICAvLyBleHRyYVxuICAvKiBOT1AgKi9cblxuICAvLyBmbmFtZVxuICBpZiAodGhpcy5mbGFnc1snZm5hbWUnXSAhPT0gdm9pZCAwKSB7XG4gICAgZm9yIChpID0gMCwgaWwgPSBmaWxlbmFtZS5sZW5ndGg7IGkgPCBpbDsgKytpKSB7XG4gICAgICBjID0gZmlsZW5hbWUuY2hhckNvZGVBdChpKTtcbiAgICAgIGlmIChjID4gMHhmZikgeyBvdXRwdXRbb3ArK10gPSAoYyA+Pj4gOCkgJiAweGZmOyB9XG4gICAgICBvdXRwdXRbb3ArK10gPSBjICYgMHhmZjtcbiAgICB9XG4gICAgb3V0cHV0W29wKytdID0gMDsgLy8gbnVsbCB0ZXJtaW5hdGlvblxuICB9XG5cbiAgLy8gZmNvbW1lbnRcbiAgaWYgKHRoaXMuZmxhZ3NbJ2NvbW1lbnQnXSkge1xuICAgIGZvciAoaSA9IDAsIGlsID0gY29tbWVudC5sZW5ndGg7IGkgPCBpbDsgKytpKSB7XG4gICAgICBjID0gY29tbWVudC5jaGFyQ29kZUF0KGkpO1xuICAgICAgaWYgKGMgPiAweGZmKSB7IG91dHB1dFtvcCsrXSA9IChjID4+PiA4KSAmIDB4ZmY7IH1cbiAgICAgIG91dHB1dFtvcCsrXSA9IGMgJiAweGZmO1xuICAgIH1cbiAgICBvdXRwdXRbb3ArK10gPSAwOyAvLyBudWxsIHRlcm1pbmF0aW9uXG4gIH1cblxuICAvLyBmaGNyY1xuICBpZiAodGhpcy5mbGFnc1snZmhjcmMnXSkge1xuICAgIGNyYzE2ID0gWmxpYi5DUkMzMi5jYWxjKG91dHB1dCwgMCwgb3ApICYgMHhmZmZmO1xuICAgIG91dHB1dFtvcCsrXSA9IChjcmMxNiAgICAgICkgJiAweGZmO1xuICAgIG91dHB1dFtvcCsrXSA9IChjcmMxNiA+Pj4gOCkgJiAweGZmO1xuICB9XG5cbiAgLy8gYWRkIGNvbXByZXNzIG9wdGlvblxuICB0aGlzLmRlZmxhdGVPcHRpb25zWydvdXRwdXRCdWZmZXInXSA9IG91dHB1dDtcbiAgdGhpcy5kZWZsYXRlT3B0aW9uc1snb3V0cHV0SW5kZXgnXSA9IG9wO1xuXG4gIC8vIGNvbXByZXNzXG4gIHJhd2RlZmxhdGUgPSBuZXcgWmxpYi5SYXdEZWZsYXRlKGlucHV0LCB0aGlzLmRlZmxhdGVPcHRpb25zKTtcbiAgb3V0cHV0ID0gcmF3ZGVmbGF0ZS5jb21wcmVzcygpO1xuICBvcCA9IHJhd2RlZmxhdGUub3A7XG5cbiAgLy8gZXhwYW5kIGJ1ZmZlclxuICBpZiAoVVNFX1RZUEVEQVJSQVkpIHtcbiAgICBpZiAob3AgKyA4ID4gb3V0cHV0LmJ1ZmZlci5ieXRlTGVuZ3RoKSB7XG4gICAgICB0aGlzLm91dHB1dCA9IG5ldyBVaW50OEFycmF5KG9wICsgOCk7XG4gICAgICB0aGlzLm91dHB1dC5zZXQobmV3IFVpbnQ4QXJyYXkob3V0cHV0LmJ1ZmZlcikpO1xuICAgICAgb3V0cHV0ID0gdGhpcy5vdXRwdXQ7XG4gICAgfSBlbHNlIHtcbiAgICAgIG91dHB1dCA9IG5ldyBVaW50OEFycmF5KG91dHB1dC5idWZmZXIpO1xuICAgIH1cbiAgfVxuXG4gIC8vIGNyYzMyXG4gIGNyYzMyID0gWmxpYi5DUkMzMi5jYWxjKGlucHV0KTtcbiAgb3V0cHV0W29wKytdID0gKGNyYzMyICAgICAgICkgJiAweGZmO1xuICBvdXRwdXRbb3ArK10gPSAoY3JjMzIgPj4+ICA4KSAmIDB4ZmY7XG4gIG91dHB1dFtvcCsrXSA9IChjcmMzMiA+Pj4gMTYpICYgMHhmZjtcbiAgb3V0cHV0W29wKytdID0gKGNyYzMyID4+PiAyNCkgJiAweGZmO1xuXG4gIC8vIGlucHV0IHNpemVcbiAgaWwgPSBpbnB1dC5sZW5ndGg7XG4gIG91dHB1dFtvcCsrXSA9IChpbCAgICAgICApICYgMHhmZjtcbiAgb3V0cHV0W29wKytdID0gKGlsID4+PiAgOCkgJiAweGZmO1xuICBvdXRwdXRbb3ArK10gPSAoaWwgPj4+IDE2KSAmIDB4ZmY7XG4gIG91dHB1dFtvcCsrXSA9IChpbCA+Pj4gMjQpICYgMHhmZjtcblxuICB0aGlzLmlwID0gaXA7XG5cbiAgaWYgKFVTRV9UWVBFREFSUkFZICYmIG9wIDwgb3V0cHV0Lmxlbmd0aCkge1xuICAgIHRoaXMub3V0cHV0ID0gb3V0cHV0ID0gb3V0cHV0LnN1YmFycmF5KDAsIG9wKTtcbiAgfVxuXG4gIHJldHVybiBvdXRwdXQ7XG59O1xuXG4vKiogQGVudW0ge251bWJlcn0gKi9cblpsaWIuR3ppcC5PcGVyYXRpbmdTeXN0ZW0gPSB7XG4gIEZBVDogMCxcbiAgQU1JR0E6IDEsXG4gIFZNUzogMixcbiAgVU5JWDogMyxcbiAgVk1fQ01TOiA0LFxuICBBVEFSSV9UT1M6IDUsXG4gIEhQRlM6IDYsXG4gIE1BQ0lOVE9TSDogNyxcbiAgWl9TWVNURU06IDgsXG4gIENQX006IDksXG4gIFRPUFNfMjA6IDEwLFxuICBOVEZTOiAxMSxcbiAgUURPUzogMTIsXG4gIEFDT1JOX1JJU0NPUzogMTMsXG4gIFVOS05PV046IDI1NVxufTtcblxuLyoqIEBlbnVtIHtudW1iZXJ9ICovXG5abGliLkd6aXAuRmxhZ3NNYXNrID0ge1xuICBGVEVYVDogMHgwMSxcbiAgRkhDUkM6IDB4MDIsXG4gIEZFWFRSQTogMHgwNCxcbiAgRk5BTUU6IDB4MDgsXG4gIEZDT01NRU5UOiAweDEwXG59O1xuXG59KTtcbi8qIHZpbTpzZXQgZXhwYW5kdGFiIHRzPTIgc3c9MiB0dz04MDogKi9cbiIsImdvb2cucmVxdWlyZSgnWmxpYi5HemlwJyk7XG5cbmdvb2cuZXhwb3J0U3ltYm9sKCdabGliLkd6aXAnLCBabGliLkd6aXApO1xuZ29vZy5leHBvcnRTeW1ib2woXG4gICdabGliLkd6aXAucHJvdG90eXBlLmNvbXByZXNzJyxcbiAgWmxpYi5HemlwLnByb3RvdHlwZS5jb21wcmVzc1xuKTsiXX0=