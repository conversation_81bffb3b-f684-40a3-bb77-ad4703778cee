<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechMan API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔧 TechMan API Configuration Test</h1>
    
    <div id="config-info" class="test-result info">
        <h3>Current Configuration</h3>
        <p><strong>Host:</strong> <span id="current-host"></span></p>
        <p><strong>Protocol:</strong> <span id="current-protocol"></span></p>
        <p><strong>Is Public IP:</strong> <span id="is-public-ip"></span></p>
    </div>

    <div class="test-result">
        <h3>API Tests</h3>
        <button onclick="testNodeAPI()">Test Node.js API</button>
        <button onclick="testPythonAPI()">Test Python API</button>
        <button onclick="testOldURLs()">Test Old URL Override</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div id="test-results"></div>

    <!-- Load the API override script -->
    <script src="/api-config-override.js"></script>
    
    <script>
        // Display current configuration
        document.getElementById('current-host').textContent = window.location.host;
        document.getElementById('current-protocol').textContent = window.location.protocol;
        document.getElementById('is-public-ip').textContent = window.location.host.includes('**************') ? 'Yes' : 'No';

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<p><strong>${new Date().toLocaleTimeString()}:</strong> ${message}</p>`;
            document.getElementById('test-results').appendChild(div);
        }

        async function testNodeAPI() {
            try {
                addResult('Testing Node.js API...', 'info');
                const response = await fetch('/api/health');
                const data = await response.json();
                addResult(`✅ Node.js API Success: ${data.message}`, 'success');
                addResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
            } catch (error) {
                addResult(`❌ Node.js API Error: ${error.message}`, 'error');
            }
        }

        async function testPythonAPI() {
            try {
                addResult('Testing Python API...', 'info');
                const response = await fetch('/pyapi/health');
                const data = await response.json();
                addResult(`✅ Python API Success: Status ${data.status}`, 'success');
                addResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
            } catch (error) {
                addResult(`❌ Python API Error: ${error.message}`, 'error');
            }
        }

        async function testOldURLs() {
            try {
                addResult('Testing URL Override with old localhost URL...', 'info');
                // This should be automatically converted to /api/health
                const response = await fetch('http://localhost:5017/api/health');
                const data = await response.json();
                addResult(`✅ URL Override Success: Old URL was automatically converted`, 'success');
                addResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
            } catch (error) {
                addResult(`❌ URL Override Error: ${error.message}`, 'error');
            }
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('🚀 Auto-testing API configuration...', 'info');
                testNodeAPI();
                setTimeout(() => testPythonAPI(), 1000);
            }, 500);
        });
    </script>
</body>
</html>
