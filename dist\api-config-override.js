// Runtime API Configuration Override
// This script dynamically configures API URLs based on the current domain

(function() {
    'use strict';
    
    console.log('🔧 TechMan API Configuration Override Loading...');
    
    // Get current host information
    const currentHost = window.location.host;
    const currentProtocol = window.location.protocol;
    const isPublicIP = currentHost.includes('**************');
    const isPort5174 = currentHost.includes(':5174');
    
    console.log('🌐 Current Host:', currentHost);
    console.log('🔍 Is Public IP:', isPublicIP);
    console.log('🔍 Is Port 5174:', isPort5174);
    
    // Define API configurations
    let apiConfig;
    
    if (isPublicIP && isPort5174) {
        // Public IP access via port 5174 - use relative URLs through nginx proxy
        apiConfig = {
            NODE_API_BASE: '/api',
            PYTHON_API_BASE: '/pyapi',
            NODE_SERVER_URL: '',
            PYTHON_SERVER_URL: '',
            description: 'Public IP via Nginx Proxy (Port 5174)'
        };
    } else if (currentHost.includes('********')) {
        // Internal network access
        apiConfig = {
            NODE_API_BASE: '/api',
            PYTHON_API_BASE: '/pyapi', 
            NODE_SERVER_URL: '',
            PYTHON_SERVER_URL: '',
            description: 'Internal Network via Nginx Proxy'
        };
    } else {
        // Localhost or other - use relative URLs
        apiConfig = {
            NODE_API_BASE: '/api',
            PYTHON_API_BASE: '/pyapi',
            NODE_SERVER_URL: '',
            PYTHON_SERVER_URL: '',
            description: 'Local Development'
        };
    }
    
    console.log('⚙️ API Configuration:', apiConfig.description);
    console.log('📡 Node API Base:', apiConfig.NODE_API_BASE);
    console.log('🐍 Python API Base:', apiConfig.PYTHON_API_BASE);
    
    // Override global API configuration
    window.TECHMAN_API_CONFIG = {
        NODE: {
            BASE: apiConfig.NODE_API_BASE,
            PROJECTS: apiConfig.NODE_API_BASE + '/projects',
            ACTIVITY_LOG: apiConfig.NODE_API_BASE + '/activity-log',
            NOTIFICATIONS: apiConfig.NODE_API_BASE + '/notifications',
            TRANSFERS: apiConfig.NODE_API_BASE + '/transfers-new',
            EQUIPMENT: apiConfig.NODE_API_BASE + '/equipment-utilization',
            PERMISSIONS: apiConfig.NODE_API_BASE + '/permissions',
            DAILY_TRACKS: apiConfig.NODE_API_BASE + '/daily-tracks',
            PUMPHOUSE: apiConfig.NODE_API_BASE + '/pumphouse',
            AI_MEASUREMENT: apiConfig.NODE_API_BASE + '/ai-measurement',
            PRODUCT: apiConfig.NODE_API_BASE + '/product',
            INVENTORY: apiConfig.NODE_API_BASE + '/inventory',
            STORE: apiConfig.NODE_API_BASE + '/store',
            PIPELINE: apiConfig.NODE_API_BASE,
            AUTH: apiConfig.NODE_API_BASE + '/auth'
        },
        PYTHON: {
            BASE: apiConfig.PYTHON_API_BASE,
            PROCUREMENT: apiConfig.PYTHON_API_BASE + '/procurement',
            CHATBOT: apiConfig.PYTHON_API_BASE + '/chatbot',
            AI: apiConfig.PYTHON_API_BASE + '/ai',
            MEASUREMENT: apiConfig.PYTHON_API_BASE + '/measurement'
        }
    };
    
    // Override environment variables if they exist
    if (window.import && window.import.meta && window.import.meta.env) {
        window.import.meta.env.VITE_API_BASE_URL = apiConfig.NODE_API_BASE;
        window.import.meta.env.VITE_PYTHON_API_URL = apiConfig.PYTHON_API_BASE;
        window.import.meta.env.VITE_NODE_SERVER_URL = apiConfig.NODE_SERVER_URL;
        window.import.meta.env.VITE_PYTHON_SERVER_URL = apiConfig.PYTHON_SERVER_URL;
    }
    
    // Create a global function to get API URLs
    window.getApiUrl = function(endpoint, type = 'node') {
        const base = type === 'python' ? apiConfig.PYTHON_API_BASE : apiConfig.NODE_API_BASE;
        return base + (endpoint.startsWith('/') ? endpoint : '/' + endpoint);
    };
    
    // Override fetch to automatically use correct API URLs
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
        let modifiedUrl = url;
        
        // Check if URL needs to be modified
        if (typeof url === 'string') {
            // Replace localhost URLs with relative URLs
            if (url.includes('localhost:5017')) {
                modifiedUrl = url.replace(/https?:\/\/localhost:5017/, '');
                if (!modifiedUrl.startsWith('/api')) {
                    modifiedUrl = '/api' + (modifiedUrl.startsWith('/') ? modifiedUrl : '/' + modifiedUrl);
                }
            } else if (url.includes('localhost:5001')) {
                modifiedUrl = url.replace(/https?:\/\/localhost:5001/, '');
                if (!modifiedUrl.startsWith('/pyapi')) {
                    modifiedUrl = '/pyapi' + (modifiedUrl.startsWith('/api') ? modifiedUrl.substring(4) : modifiedUrl);
                }
            } else if (url.includes('********:5017')) {
                modifiedUrl = url.replace(/https?:\/\/10\.0\.5\.4:5017/, '');
                if (!modifiedUrl.startsWith('/api')) {
                    modifiedUrl = '/api' + (modifiedUrl.startsWith('/') ? modifiedUrl : '/' + modifiedUrl);
                }
            } else if (url.includes('********:5001')) {
                modifiedUrl = url.replace(/https?:\/\/10\.0\.5\.4:5001/, '');
                if (!modifiedUrl.startsWith('/pyapi')) {
                    modifiedUrl = '/pyapi' + (modifiedUrl.startsWith('/api') ? modifiedUrl.substring(4) : modifiedUrl);
                }
            } else if (url.includes('**************:5017')) {
                modifiedUrl = url.replace(/https?:\/\/103\.255\.144\.12:5017/, '');
                if (!modifiedUrl.startsWith('/api')) {
                    modifiedUrl = '/api' + (modifiedUrl.startsWith('/') ? modifiedUrl : '/' + modifiedUrl);
                }
            } else if (url.includes('**************:5001')) {
                modifiedUrl = url.replace(/https?:\/\/103\.255\.144\.12:5001/, '');
                if (!modifiedUrl.startsWith('/pyapi')) {
                    modifiedUrl = '/pyapi' + (modifiedUrl.startsWith('/api') ? modifiedUrl.substring(4) : modifiedUrl);
                }
            }
        }
        
        if (modifiedUrl !== url) {
            console.log('🔄 API URL Override:', url, '→', modifiedUrl);
        }
        
        return originalFetch.call(this, modifiedUrl, options);
    };
    
    console.log('✅ TechMan API Configuration Override Loaded Successfully!');
    console.log('🔗 API URLs will be automatically routed through nginx proxy');
    
})();
