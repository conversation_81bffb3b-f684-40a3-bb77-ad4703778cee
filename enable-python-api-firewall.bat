@echo off
echo ========================================
echo Enable Python API Firewall Access
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: This script must be run as Administrator!
    echo.
    echo 📋 HOW TO RUN AS ADMINISTRATOR:
    echo 1. Right-click on this file: enable-python-api-firewall.bat
    echo 2. Select "Run as administrator"
    echo 3. Click "Yes" when prompted
    echo.
    pause
    exit /b 1
)

echo ✅ Running with administrator privileges
echo.

echo Adding firewall rule for Python API (port 5001)...
netsh advfirewall firewall add rule name="TechMan Python API" dir=in action=allow protocol=TCP localport=5001

if %errorlevel% equ 0 (
    echo ✅ SUCCESS: Firewall rule added!
    echo.
    echo 🌐 Your Python API is now accessible via:
    echo   Local:  http://localhost:5001/api/health
    echo   Public: http://**************:5001/api/health
    echo.
) else (
    echo ❌ FAILED: Could not add firewall rule
    echo.
)

echo Testing connectivity...
echo.
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://localhost:5001/api/health' -Method Get; Write-Host '✅ Local API: Working'; Write-Host 'Status:' $result.status } catch { Write-Host '❌ Local API: Failed' }"

echo.
echo ========================================
echo Configuration Complete!
echo ========================================
pause
