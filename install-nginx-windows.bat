@echo off
echo ========================================
echo TechMan Nginx Installation for Windows
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo ✅ Running with administrator privileges
echo.

REM Set variables
set NGINX_DIR=C:\nginx
set NGINX_VERSION=1.24.0
set NGINX_URL=http://nginx.org/download/nginx-%NGINX_VERSION%.zip
set TEMP_DIR=%TEMP%\nginx-install

echo ========================================
echo Step 1: Download Nginx
echo ========================================
echo.

REM Create temp directory
if not exist "%TEMP_DIR%" mkdir "%TEMP_DIR%"

echo Downloading nginx %NGINX_VERSION%...
echo From: %NGINX_URL%
echo.

REM Download nginx using PowerShell
powershell -Command "try { Invoke-WebRequest -Uri '%NGINX_URL%' -OutFile '%TEMP_DIR%\nginx.zip'; Write-Host '✅ Download completed' } catch { Write-Host '❌ Download failed:' $_.Exception.Message; exit 1 }"

if not exist "%TEMP_DIR%\nginx.zip" (
    echo ❌ Download failed. Please check your internet connection.
    pause
    exit /b 1
)

echo ========================================
echo Step 2: Extract Nginx
echo ========================================
echo.

echo Extracting nginx...
powershell -Command "Expand-Archive -Path '%TEMP_DIR%\nginx.zip' -DestinationPath '%TEMP_DIR%' -Force"

REM Move nginx to C:\nginx
if exist "%NGINX_DIR%" (
    echo Removing existing nginx directory...
    rmdir /s /q "%NGINX_DIR%"
)

echo Creating nginx directory at %NGINX_DIR%...
move "%TEMP_DIR%\nginx-%NGINX_VERSION%" "%NGINX_DIR%"

if not exist "%NGINX_DIR%\nginx.exe" (
    echo ❌ Installation failed. nginx.exe not found.
    pause
    exit /b 1
)

echo ✅ Nginx extracted successfully

echo ========================================
echo Step 3: Configure Environment
echo ========================================
echo.

REM Add nginx to PATH (optional)
echo Adding nginx to system PATH...
setx PATH "%PATH%;%NGINX_DIR%" /M

REM Create logs directory
if not exist "%NGINX_DIR%\logs" mkdir "%NGINX_DIR%\logs"

REM Create temp directory for nginx
if not exist "%NGINX_DIR%\temp" mkdir "%NGINX_DIR%\temp"

echo ========================================
echo Step 4: Test Installation
echo ========================================
echo.

cd /d "%NGINX_DIR%"
echo Testing nginx installation...
nginx.exe -v

if %errorlevel% equ 0 (
    echo ✅ Nginx installed successfully!
) else (
    echo ❌ Nginx installation test failed
    pause
    exit /b 1
)

echo ========================================
echo Step 5: Configure Firewall
echo ========================================
echo.

echo Adding firewall rules for nginx (port 80)...
netsh advfirewall firewall delete rule name="Nginx HTTP" >nul 2>&1
netsh advfirewall firewall add rule name="Nginx HTTP" dir=in action=allow protocol=TCP localport=80

echo ✅ Firewall configured for port 80

echo ========================================
echo ✅ Nginx Installation Complete!
echo ========================================
echo.
echo 📁 Installation Directory: %NGINX_DIR%
echo 🔧 Version: %NGINX_VERSION%
echo 🌐 Default Port: 80
echo.
echo 📋 Next Steps:
echo 1. Configure nginx for TechMan application
echo 2. Start all TechMan services
echo 3. Start nginx
echo.
echo 🛠️ Management Commands:
echo   Start:  cd %NGINX_DIR% && nginx.exe
echo   Stop:   cd %NGINX_DIR% && nginx.exe -s stop
echo   Reload: cd %NGINX_DIR% && nginx.exe -s reload
echo   Test:   cd %NGINX_DIR% && nginx.exe -t
echo.

REM Cleanup
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"

pause
