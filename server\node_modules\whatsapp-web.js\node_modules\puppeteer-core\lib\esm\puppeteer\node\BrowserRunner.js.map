{"version": 3, "file": "BrowserRunner.js", "sourceRoot": "", "sources": ["../../../../src/node/BrowserRunner.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;AAEH,OAAO,KAAK,YAAY,MAAM,eAAe,CAAC;AAC9C,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,KAAK,QAAQ,MAAM,UAAU,CAAC;AACrC,OAAO,YAAY,MAAM,QAAQ,CAAC;AAClC,OAAO,EAAC,SAAS,EAAC,MAAM,MAAM,CAAC;AAC/B,OAAO,EAAC,UAAU,IAAI,cAAc,EAAC,MAAM,8BAA8B,CAAC;AAC1E,OAAO,EAAC,UAAU,EAAC,MAAM,yBAAyB,CAAC;AACnD,OAAO,EAAC,KAAK,EAAC,MAAM,oBAAoB,CAAC;AACzC,OAAO,EAAC,YAAY,EAAC,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAC,sBAAsB,IAAI,kBAAkB,EAAC,MAAM,qCAAqC,CAAC;AAEjG,OAAO,EACL,gBAAgB,EAChB,UAAU,EAEV,oBAAoB,GACrB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,gBAAgB,EAAE,WAAW,EAAC,MAAM,sBAAsB,CAAC;AAEnE,OAAO,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AAEjD,MAAM,iBAAiB,GAAG,SAAS,CAAC,YAAY,CAAC,CAAC;AAClD,MAAM,WAAW,GAAG,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AACzC,MAAM,WAAW,GAAG,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AAEzC,MAAM,aAAa,GAAG,KAAK,CAAC,oBAAoB,CAAC,CAAC;AAElD,MAAM,yBAAyB,GAAG;;;6EAG2C,CAAC;AAE9E;;GAEG;AACH,MAAM,OAAO,aAAa;IAaxB,YACE,OAAgB,EAChB,cAAsB,EACtB,gBAA0B,EAC1B,WAAmB,EACnB,iBAA2B;QAjB7B,yCAAkB;QAClB,gDAAwB;QACxB,kDAA4B;QAC5B,6CAAqB;QACrB,mDAA6B;QAC7B,gCAAU,IAAI,EAAC;QACf,mCAAuC,EAAE,EAAC;QAC1C,gDAAgC;QAY9B,uBAAA,IAAI,0BAAY,OAAO,MAAA,CAAC;QACxB,uBAAA,IAAI,iCAAmB,cAAc,MAAA,CAAC;QACtC,uBAAA,IAAI,mCAAqB,gBAAgB,MAAA,CAAC;QAC1C,uBAAA,IAAI,8BAAgB,WAAW,MAAA,CAAC;QAChC,uBAAA,IAAI,oCAAsB,iBAAiB,MAAA,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,OAAsB;;QAC1B,MAAM,EAAC,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAC,GAClE,OAAO,CAAC;QACV,IAAI,KAA+B,CAAC;QACpC,IAAI,IAAI,EAAE;YACR,IAAI,MAAM,EAAE;gBACV,KAAK,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;aACpD;iBAAM;gBACL,KAAK,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;aACxD;SACF;aAAM;YACL,IAAI,MAAM,EAAE;gBACV,KAAK,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;aAClC;iBAAM;gBACL,KAAK,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;aACpC;SACF;QACD,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,2CAA2C,CAAC,CAAC;QAChE,aAAa,CACX,WAAW,uBAAA,IAAI,qCAAgB,IAAI,uBAAA,IAAI,uCAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CACtE,CAAC;QACF,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,KAAK,CAC5B,uBAAA,IAAI,qCAAgB,EACpB,uBAAA,IAAI,uCAAkB,EACtB;YACE,mEAAmE;YACnE,kEAAkE;YAClE,gDAAgD;YAChD,2EAA2E;YAC3E,QAAQ,EAAE,OAAO,CAAC,QAAQ,KAAK,OAAO;YACtC,GAAG;YACH,KAAK;SACN,CACF,CAAC;QACF,IAAI,MAAM,EAAE;YACV,MAAA,IAAI,CAAC,IAAI,CAAC,MAAM,0CAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvC,MAAA,IAAI,CAAC,IAAI,CAAC,MAAM,0CAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACxC;QACD,uBAAA,IAAI,yBAAW,KAAK,MAAA,CAAC;QACrB,uBAAA,IAAI,iCAAmB,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrD,IAAI,CAAC,IAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;gBACjC,uBAAA,IAAI,yBAAW,IAAI,MAAA,CAAC;gBACpB,6BAA6B;gBAC7B,IAAI,uBAAA,IAAI,wCAAmB,EAAE;oBAC3B,IAAI;wBACF,MAAM,iBAAiB,CAAC,uBAAA,IAAI,kCAAa,CAAC,CAAC;wBAC3C,OAAO,EAAE,CAAC;qBACX;oBAAC,OAAO,KAAK,EAAE;wBACd,UAAU,CAAC,KAAK,CAAC,CAAC;wBAClB,MAAM,CAAC,KAAK,CAAC,CAAC;qBACf;iBACF;qBAAM;oBACL,IAAI,uBAAA,IAAI,8BAAS,KAAK,SAAS,EAAE;wBAC/B,IAAI;4BACF,8DAA8D;4BAC9D,8DAA8D;4BAC9D,MAAM,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAA,IAAI,kCAAa,EAAE,SAAS,CAAC,CAAC,CAAC;4BAE3D,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAC/B,uBAAA,IAAI,kCAAa,EACjB,oBAAoB,CACrB,CAAC;4BACF,IAAI,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE;gCAClC,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,uBAAA,IAAI,kCAAa,EAAE,UAAU,CAAC,CAAC;gCAC3D,MAAM,WAAW,CAAC,SAAS,CAAC,CAAC;gCAC7B,MAAM,WAAW,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;6BAC/C;yBACF;wBAAC,OAAO,KAAK,EAAE;4BACd,UAAU,CAAC,KAAK,CAAC,CAAC;4BAClB,MAAM,CAAC,KAAK,CAAC,CAAC;yBACf;qBACF;oBAED,OAAO,EAAE,CAAC;iBACX;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,MAAA,CAAC;QACH,uBAAA,IAAI,4BAAc,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAA,CAAC;QAC5E,IAAI,YAAY,EAAE;YAChB,uBAAA,IAAI,gCAAW,CAAC,IAAI,CAClB,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE;gBACvC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpB,CAAC,CAAC,CACH,CAAC;SACH;QACD,IAAI,aAAa,EAAE;YACjB,uBAAA,IAAI,gCAAW,CAAC,IAAI,CAClB,gBAAgB,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC5D,CAAC;SACH;QACD,IAAI,YAAY,EAAE;YAChB,uBAAA,IAAI,gCAAW,CAAC,IAAI,CAClB,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC3D,CAAC;SACH;IACH,CAAC;IAED,KAAK;QACH,IAAI,uBAAA,IAAI,6BAAQ,EAAE;YAChB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QACD,IAAI,uBAAA,IAAI,wCAAmB,EAAE;YAC3B,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;aAAM,IAAI,IAAI,CAAC,UAAU,EAAE;YAC1B,0CAA0C;YAC1C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBAClD,UAAU,CAAC,KAAK,CAAC,CAAC;gBAClB,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;SACJ;QACD,+EAA+E;QAC/E,2EAA2E;QAC3E,oBAAoB,CAAC,uBAAA,IAAI,gCAAW,CAAC,CAAC;QACtC,OAAO,uBAAA,IAAI,qCAAgB,CAAC;IAC9B,CAAC;IAED,IAAI;QACF,8EAA8E;QAC9E,uEAAuE;QACvE,uEAAuE;QACvE,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAC1D,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACvB,IAAI;gBACF,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;oBAChC,YAAY,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,EAAE,KAAK,CAAC,EAAE;wBAChE,IAAI,KAAK,EAAE;4BACT,yEAAyE;4BACzE,wEAAwE;4BACxE,gEAAgE;4BAChE,IAAI,CAAC,IAAI,EAAE,CAAC;yBACb;oBACH,CAAC,CAAC,CAAC;iBACJ;qBAAM;oBACL,2EAA2E;oBAC3E,gEAAgE;oBAChE,MAAM,cAAc,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;oBAEtC,IAAI;wBACF,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;qBACzC;oBAAC,OAAO,KAAK,EAAE;wBACd,sEAAsE;wBACtE,wEAAwE;wBACxE,gEAAgE;wBAChE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;qBACtB;iBACF;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,MAAM,IAAI,KAAK,CACb,GAAG,yBAAyB,kBAC1B,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KACrC,EAAE,CACH,CAAC;aACH;SACF;QAED,oEAAoE;QACpE,IAAI;YACF,IAAI,uBAAA,IAAI,wCAAmB,EAAE;gBAC3B,YAAY,CAAC,IAAI,CAAC,uBAAA,IAAI,kCAAa,CAAC,CAAC;aACtC;SACF;QAAC,OAAO,KAAK,EAAE,GAAE;QAElB,+EAA+E;QAC/E,2EAA2E;QAC3E,oBAAoB,CAAC,uBAAA,IAAI,gCAAW,CAAC,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,4BAA4B,CAAC,OAIlC;QACC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,4BAA4B,CAAC,CAAC;QAEhD,MAAM,EAAC,OAAO,EAAE,MAAM,EAAE,iBAAiB,EAAC,GAAG,OAAO,CAAC;QACrD,IAAI,iBAAiB,GAAG,MAAM,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,OAAO,EACP,iBAAiB,EACjB,2CAA2C,CAC5C,CAAC;QACF,iBAAiB,IAAI,UAAU,CAAC;QAChC,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QACrE,OAAO,IAAI,cAAc,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAKrB;QACC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,4BAA4B,CAAC,CAAC;QAEhD,MAAM,EAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,iBAAiB,EAAC,GAAG,OAAO,CAAC;QAC9D,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,iBAAiB,GAAG,MAAM,iBAAiB,CAC/C,IAAI,CAAC,IAAI,EACT,OAAO,EACP,iBAAiB,CAClB,CAAC;YACF,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACrE,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC,iBAAiB,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;SACxE;aAAM;YACL,0EAA0E;YAC1E,mCAAmC;YACnC,MAAM,EAAC,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;YACpD,MAAM,SAAS,GAAG,IAAI,aAAa,CACjC,SAAkC,EAClC,QAAiC,CAClC,CAAC;YACF,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;SACzD;QACD,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;CACF;;AAED,SAAS,iBAAiB,CACxB,cAAyC,EACzC,OAAe,EACf,iBAAyB,EACzB,KAAK,GAAG,qCAAqC;IAE7C,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,wCAAwC,CAAC,CAAC;IACxE,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IAC3D,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,SAAS,GAAG;YAChB,gBAAgB,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC;YACpC,gBAAgB,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE;gBACjC,OAAO,OAAO,EAAE,CAAC;YACnB,CAAC,CAAC;YACF,gBAAgB,CAAC,cAAc,EAAE,MAAM,EAAE,GAAG,EAAE;gBAC5C,OAAO,OAAO,EAAE,CAAC;YACnB,CAAC,CAAC;YACF,gBAAgB,CAAC,cAAc,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE;gBAChD,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC,CAAC;SACH,CAAC;QACF,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/D,SAAS,OAAO,CAAC,KAAa;YAC5B,OAAO,EAAE,CAAC;YACV,MAAM,CACJ,IAAI,KAAK,CACP;gBACE,uCAAuC;oBACrC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpC,MAAM;gBACN,EAAE;gBACF,2FAA2F;gBAC3F,EAAE;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CACF,CAAC;QACJ,CAAC;QAED,SAAS,SAAS;YAChB,OAAO,EAAE,CAAC;YACV,MAAM,CACJ,IAAI,YAAY,CACd,mBAAmB,OAAO,wEAAwE,iBAAiB,yBAAyB,CAC7I,CACF,CAAC;QACJ,CAAC;QAED,SAAS,MAAM,CAAC,IAAY;YAC1B,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAChC,IAAI,CAAC,KAAK,EAAE;gBACV,OAAO;aACR;YACD,OAAO,EAAE,CAAC;YACV,oDAAoD;YACpD,OAAO,CAAC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC;QACrB,CAAC;QAED,SAAS,OAAO;YACd,IAAI,SAAS,EAAE;gBACb,YAAY,CAAC,SAAS,CAAC,CAAC;aACzB;YACD,oBAAoB,CAAC,SAAS,CAAC,CAAC;QAClC,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,SAAS,CAAC,GAAW;IAC5B,IAAI;QACF,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;KAC7B;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;YAC3B,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;gBACxC,OAAO,KAAK,CAAC;aACd;SACF;QACD,MAAM,KAAK,CAAC;KACb;AACH,CAAC"}