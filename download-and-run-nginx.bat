@echo off
echo ========================================
echo Download and Run Nginx (Portable)
echo ========================================
echo.

set PROJECT_DIR=%~dp0
set NGINX_LOCAL=%PROJECT_DIR%nginx-portable
set NGINX_VERSION=1.24.0

echo ✅ Project Directory: %PROJECT_DIR%
echo ✅ Nginx Local Directory: %NGINX_LOCAL%
echo.

echo ========================================
echo Step 1: Download Nginx (Portable)
echo ========================================
echo.

if exist "%NGINX_LOCAL%\nginx.exe" (
    echo ✅ Nginx already downloaded
    goto :configure
)

echo Creating nginx directory...
if not exist "%NGINX_LOCAL%" mkdir "%NGINX_LOCAL%"

echo Downloading nginx %NGINX_VERSION% (this may take a moment)...
powershell -Command "try { Invoke-WebRequest -Uri 'http://nginx.org/download/nginx-%NGINX_VERSION%.zip' -OutFile '%PROJECT_DIR%nginx.zip'; Write-Host '✅ Download completed' } catch { Write-Host '❌ Download failed:' $_.Exception.Message; exit 1 }"

if not exist "%PROJECT_DIR%nginx.zip" (
    echo ❌ Download failed. Please check your internet connection.
    pause
    exit /b 1
)

echo Extracting nginx...
powershell -Command "Expand-Archive -Path '%PROJECT_DIR%nginx.zip' -DestinationPath '%PROJECT_DIR%' -Force"

echo Moving nginx files...
move "%PROJECT_DIR%nginx-%NGINX_VERSION%\*" "%NGINX_LOCAL%\"
rmdir "%PROJECT_DIR%nginx-%NGINX_VERSION%"
del "%PROJECT_DIR%nginx.zip"

if not exist "%NGINX_LOCAL%\nginx.exe" (
    echo ❌ Extraction failed
    pause
    exit /b 1
)

echo ✅ Nginx downloaded and extracted

:configure
echo ========================================
echo Step 2: Configure Nginx
echo ========================================
echo.

REM Create necessary directories
if not exist "%NGINX_LOCAL%\logs" mkdir "%NGINX_LOCAL%\logs"
if not exist "%NGINX_LOCAL%\temp" mkdir "%NGINX_LOCAL%\temp"

REM Copy our configuration
echo Copying TechMan nginx configuration...
copy "%PROJECT_DIR%nginx-techman.conf" "%NGINX_LOCAL%\conf\nginx.conf" /Y

REM Test configuration
echo Testing nginx configuration...
cd /d "%NGINX_LOCAL%"
nginx.exe -t
if %errorlevel% neq 0 (
    echo ❌ Configuration test failed
    pause
    exit /b 1
)

echo ✅ Configuration test passed

echo ========================================
echo Step 3: Start All Services
echo ========================================
echo.

REM Stop any existing nginx
echo Stopping any existing nginx processes...
taskkill /f /im nginx.exe >nul 2>&1

echo Starting MongoDB (if not running)...
net start MongoDB >nul 2>&1

echo Starting Python API...
cd /d "%PROJECT_DIR%python-model"
call venv\Scripts\activate.bat
start "TechMan Python API" cmd /k "python single_api.py"

echo Waiting for Python API to start...
timeout /t 5 /nobreak >nul

echo Starting Node.js Backend...
cd /d "%PROJECT_DIR%server"
start "TechMan Backend" cmd /k "npm run start"

echo Waiting for Backend to start...
timeout /t 5 /nobreak >nul

echo Starting Nginx...
cd /d "%NGINX_LOCAL%"
start "TechMan Nginx" cmd /k "nginx.exe"

echo ========================================
echo ✅ TechMan Application Started!
echo ========================================
echo.
echo 🌐 Your application is now accessible at:
echo.
echo   Main Application:
echo     http://localhost
echo     http://************** (if firewall configured)
echo.
echo   Health Checks:
echo     Frontend:    http://localhost/
echo     Backend API: http://localhost/api/health
echo     Python API:  http://localhost/pyapi/health
echo.
echo   Direct Access:
echo     Python API:  http://localhost:5001/api/health
echo     Backend API: http://localhost:5017/api/health
echo.
echo 📊 Service Status:
echo   ✅ MongoDB: Running on port 27017
echo   ✅ Python API: Running on port 5001
echo   ✅ Node.js Backend: Running on port 5017
echo   ✅ Nginx: Running on port 80
echo.
echo 🛠️ Management:
echo   Stop all: .\stop-production-services.bat
echo   Reload nginx: cd nginx-portable && nginx.exe -s reload
echo.
pause
