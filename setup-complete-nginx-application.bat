@echo off
echo ========================================
echo TechMan Complete Application Setup
echo ========================================
echo.
echo This script will guide you through setting up the complete TechMan application with nginx
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo ✅ Running with administrator privileges
echo.

set PROJECT_DIR=%~dp0

echo ========================================
echo Step 1: Install Nginx (if not installed)
echo ========================================
echo.

if exist "C:\nginx\nginx.exe" (
    echo ✅ Nginx already installed at C:\nginx
) else (
    echo Installing nginx...
    call "%PROJECT_DIR%install-nginx-windows.bat"
    if %errorlevel% neq 0 (
        echo ❌ Nginx installation failed
        pause
        exit /b 1
    )
)

echo ========================================
echo Step 2: Configure Firewall for All Services
echo ========================================
echo.

echo Configuring firewall for all required ports...

REM Port 80 for nginx
netsh advfirewall firewall delete rule name="Nginx HTTP" >nul 2>&1
netsh advfirewall firewall add rule name="Nginx HTTP" dir=in action=allow protocol=TCP localport=80
echo ✅ Port 80 (Nginx) configured

REM Port 5001 for Python API
netsh advfirewall firewall delete rule name="TechMan Python API" >nul 2>&1
netsh advfirewall firewall add rule name="TechMan Python API" dir=in action=allow protocol=TCP localport=5001
echo ✅ Port 5001 (Python API) configured

REM Port 5017 for Node.js Backend
netsh advfirewall firewall delete rule name="TechMan Backend" >nul 2>&1
netsh advfirewall firewall add rule name="TechMan Backend" dir=in action=allow protocol=TCP localport=5017
echo ✅ Port 5017 (Node.js Backend) configured

echo ========================================
echo Step 3: Check MongoDB Service
echo ========================================
echo.

sc query MongoDB >nul 2>&1
if %errorlevel% neq 0 (
    echo Starting MongoDB service...
    net start MongoDB
    timeout /t 5 /nobreak >nul
) else (
    echo ✅ MongoDB is already running
)

echo ========================================
echo Step 4: Build Frontend (if needed)
echo ========================================
echo.

if exist "%PROJECT_DIR%client\dist\index.html" (
    echo ✅ Frontend build found at client\dist
) else (
    echo Building frontend...
    cd /d "%PROJECT_DIR%client"
    npm run build
    if %errorlevel% neq 0 (
        echo ⚠️  Frontend build failed, but continuing...
    )
)

echo ========================================
echo Step 5: Start All Services
echo ========================================
echo.

echo Starting TechMan application with nginx...
call "%PROJECT_DIR%start-production-with-nginx.bat"

echo ========================================
echo ✅ Setup Complete!
echo ========================================
echo.
echo 🌐 Your TechMan application should now be accessible at:
echo.
echo   Main Application:
echo     http://**************
echo     http://localhost
echo.
echo   Direct API Access:
echo     Node.js Backend: http://**************:5017/api/health
echo     Python API:      http://**************:5001/api/health
echo.
echo   Through Nginx Proxy:
echo     Backend API:     http://**************/api/
echo     Python API:      http://**************/pyapi/
echo.
echo 📊 Service Architecture:
echo   Nginx (Port 80) → Reverse Proxy
echo   ├── Frontend: Static files from client/dist
echo   ├── /api/* → Node.js Backend (Port 5017)
echo   └── /pyapi/* → Python API (Port 5001)
echo.
echo 🔧 Management Commands:
echo   Stop all: .\stop-production-services.bat
echo   Health:   .\check-services-health.bat
echo   Nginx:    cd C:\nginx && nginx.exe -s reload
echo.
pause
