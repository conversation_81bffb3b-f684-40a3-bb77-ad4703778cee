<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechMan Mobile Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .login-form {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            border-color: #007bff;
            outline: none;
        }
        button {
            width: 100%;
            padding: 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .message {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            font-weight: bold;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #bee5eb;
        }
        .debug {
            margin-top: 20px;
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="login-form">
        <h1>📱 TechMan Mobile Login</h1>
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="GopiTanneeru" required>
            </div>
            <button type="submit" id="loginBtn">🚀 Login</button>
        </form>
        <div id="message"></div>
        <div id="debug" class="debug" style="display: none;"></div>
    </div>

    <script>
        const debugDiv = document.getElementById('debug');
        
        function log(message) {
            console.log(message);
            debugDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            debugDiv.style.display = 'block';
        }
        
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const messageDiv = document.getElementById('message');
            const loginBtn = document.getElementById('loginBtn');
            
            loginBtn.disabled = true;
            loginBtn.textContent = '⏳ Logging in...';
            
            // Clear previous messages
            messageDiv.innerHTML = '';
            debugDiv.textContent = '';
            debugDiv.style.display = 'none';
            
            try {
                const payload = {
                    email: email,
                    password: password
                };
                
                log('Attempting login with payload: ' + JSON.stringify(payload));
                
                // Try nginx proxy first
                messageDiv.innerHTML = '<div class="info">🔄 Trying nginx proxy...</div>';
                
                let response;
                try {
                    response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(payload)
                    });
                    log('Nginx proxy response status: ' + response.status);
                } catch (proxyError) {
                    log('Nginx proxy failed: ' + proxyError.message);
                    
                    // Try direct backend connection
                    messageDiv.innerHTML = '<div class="info">🔄 Trying direct backend...</div>';
                    
                    response = await fetch('http://103.255.144.12:5017/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(payload)
                    });
                    log('Direct backend response status: ' + response.status);
                }
                
                const data = await response.json();
                log('Response data: ' + JSON.stringify(data));
                
                if (data.success) {
                    messageDiv.innerHTML = '<div class="success">✅ Login successful! Redirecting...</div>';
                    
                    // Store the token
                    if (data.token) {
                        localStorage.setItem('token', data.token);
                        log('Token stored in localStorage');
                    }
                    
                    // Redirect to main app
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                } else {
                    messageDiv.innerHTML = '<div class="error">❌ ' + (data.message || 'Login failed') + '</div>';
                }
            } catch (error) {
                log('Final error: ' + error.message);
                messageDiv.innerHTML = '<div class="error">❌ Network error: ' + error.message + '</div>';
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = '🚀 Login';
            }
        });
    </script>
</body>
</html>
