@echo off
echo ========================================
echo Configure Firewall for Port 5174
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ This script must be run as Administrator!
    echo.
    echo 📋 HOW TO RUN AS ADMINISTRATOR:
    echo 1. Right-click on this file: configure-port-5174-firewall.bat
    echo 2. Select "Run as administrator"
    echo 3. Click "Yes" when prompted
    echo.
    pause
    exit /b 1
)

echo ✅ Running with administrator privileges
echo.

echo ========================================
echo Adding Firewall Rules for Port 5174
echo ========================================
echo.

echo Adding inbound rule for port 5174...
netsh advfirewall firewall delete rule name="TechMan Port 5174" >nul 2>&1
netsh advfirewall firewall add rule name="TechMan Port 5174" dir=in action=allow protocol=TCP localport=5174

if %errorlevel% equ 0 (
    echo ✅ Inbound rule added successfully
) else (
    echo ❌ Failed to add inbound rule
    pause
    exit /b 1
)

echo.
echo Adding outbound rule for port 5174...
netsh advfirewall firewall add rule name="TechMan Port 5174 Outbound" dir=out action=allow protocol=TCP localport=5174

if %errorlevel% equ 0 (
    echo ✅ Outbound rule added successfully
) else (
    echo ❌ Failed to add outbound rule
)

echo.
echo ========================================
echo Testing Port 5174 Access
echo ========================================
echo.

echo Testing local access...
powershell -Command "try { $result = Invoke-WebRequest -Uri 'http://localhost:5174/health' -UseBasicParsing; Write-Host '✅ Local Port 5174:' $result.StatusCode } catch { Write-Host '❌ Local Port 5174: Failed' }"

echo.
echo Testing internal network access...
powershell -Command "try { $result = Invoke-WebRequest -Uri 'http://********:5174/health' -UseBasicParsing; Write-Host '✅ Internal Port 5174:' $result.StatusCode } catch { Write-Host '❌ Internal Port 5174: Failed' }"

echo.
echo Testing public IP access...
powershell -Command "try { $result = Invoke-WebRequest -Uri 'http://**************:5174/health' -UseBasicParsing -TimeoutSec 10; Write-Host '✅ Public Port 5174:' $result.StatusCode } catch { Write-Host '⚠️  Public Port 5174: May need router port forwarding' }"

echo.
echo ========================================
echo ✅ Firewall Configuration Complete!
echo ========================================
echo.
echo 🔥 Firewall Rules Added:
echo   ✅ Inbound: TCP Port 5174 (TechMan Port 5174)
echo   ✅ Outbound: TCP Port 5174 (TechMan Port 5174 Outbound)
echo.
echo 🌐 Your TechMan Application URLs (Port 5174):
echo.
echo   ✅ Local Access:
echo      http://localhost:5174
echo      http://localhost:5174/api/health
echo      http://localhost:5174/pyapi/health
echo.
echo   ✅ Internal Network:
echo      http://********:5174
echo      http://********:5174/api/health
echo      http://********:5174/pyapi/health
echo.
echo   🌍 Public IP (requires router port forwarding):
echo      http://**************:5174
echo      http://**************:5174/api/health
echo      http://**************:5174/pyapi/health
echo.
echo 📋 Router Configuration:
echo   Forward external port 5174 to internal ********:5174
echo.
echo 🎉 Your TechMan application is now running on port 5174!
echo.
pause
