/** @license zlib.js 2012 - imaya [ https://github.com/imaya/zlib.js ] The MIT License */(function() {'use strict';var f=this;function h(c,a){var b=c.split("."),e=f;!(b[0]in e)&&e.execScript&&e.execScript("var "+b[0]);for(var d;b.length&&(d=b.shift());)!b.length&&void 0!==a?e[d]=a:e=e[d]?e[d]:e[d]={}};var l={c:function(c,a,b){return l.update(c,0,a,b)},update:function(c,a,b,e){var d=l.a,g="number"===typeof b?b:b=0,k="number"===typeof e?e:c.length;a^=**********;for(g=k&7;g--;++b)a=a>>>8^d[(a^c[b])&255];for(g=k>>3;g--;b+=8)a=a>>>8^d[(a^c[b])&255],a=a>>>8^d[(a^c[b+1])&255],a=a>>>8^d[(a^c[b+2])&255],a=a>>>8^d[(a^c[b+3])&255],a=a>>>8^d[(a^c[b+4])&255],a=a>>>8^d[(a^c[b+5])&255],a=a>>>8^d[(a^c[b+6])&255],a=a>>>8^d[(a^c[b+7])&255];return(a^**********)>>>0},d:function(c,a){return(l.a[(c^a)&255]^c>>>8)>>>
0},b:[0,**********,**********,**********,124634137,**********,**********,**********,249268274,**********,**********,**********,162941995,**********,**********,**********,498536548,**********,**********,**********,450548861,**********,**********,**********,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,
2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,
2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,
2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,
3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,
936918E3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117]};l.a="undefined"!==typeof Uint8Array&&"undefined"!==typeof Uint16Array&&"undefined"!==typeof Uint32Array&&"undefined"!==typeof DataView?new Uint32Array(l.b):l.b;h("Zlib.CRC32",l);h("Zlib.CRC32.calc",l.c);h("Zlib.CRC32.update",l.update);}).call(this);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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