/** @license zlib.js 2012 - imaya [ https://github.com/imaya/zlib.js ] The MIT License */(function() {'use strict';var k=void 0,l=!0,aa=this;function s(a,d){var b=a.split("."),c=aa;!(b[0]in c)&&c.execScript&&c.execScript("var "+b[0]);for(var e;b.length&&(e=b.shift());)!b.length&&d!==k?c[e]=d:c=c[e]?c[e]:c[e]={}};var x="undefined"!==typeof Uint8Array&&"undefined"!==typeof Uint16Array&&"undefined"!==typeof Uint32Array&&"undefined"!==typeof DataView;function y(a){var d=a.length,b=0,c=Number.POSITIVE_INFINITY,e,g,f,p,h,q,m,n,r,B;for(n=0;n<d;++n)a[n]>b&&(b=a[n]),a[n]<c&&(c=a[n]);e=1<<b;g=new (x?Uint32Array:Array)(e);f=1;p=0;for(h=2;f<=b;){for(n=0;n<d;++n)if(a[n]===f){q=0;m=p;for(r=0;r<f;++r)q=q<<1|m&1,m>>=1;B=f<<16|n;for(r=q;r<e;r+=h)g[r]=B;++p}++f;p<<=1;h<<=1}return[g,b,c]};function z(a,d,b){this.u=[];this.i=b?b:32768;this.v=0;this.a=d===k?0:d;this.d=this.e=0;this.input=x?new Uint8Array(a):a;this.b=new (x?Uint8Array:Array)(this.i);this.c=0;this.t=this.l=!1;this.f=0;this.status=A}var A=0;
z.prototype.j=function(a,d){var b=!1;a!==k&&(this.input=a);d!==k&&(this.a=d);for(;!b;)switch(this.status){case A:case 1:var c;var e=k;this.status=1;H(this);if(0>(e=I(this,3)))J(this),c=-1;else{e&1&&(this.l=l);e>>>=1;switch(e){case 0:this.h=0;break;case 1:this.h=1;break;case 2:this.h=2;break;default:throw Error("unknown BTYPE: "+e);}this.status=2;c=k}0>c&&(b=l);break;case 2:case 3:switch(this.h){case 0:var g;var f=k,p=k,h=this.input,q=this.a;this.status=3;if(q+4>=h.length)g=-1;else{f=h[q++]|h[q++]<<
8;p=h[q++]|h[q++]<<8;if(f===~p)throw Error("invalid uncompressed block header: length verify");this.d=this.e=0;this.a=q;this.m=f;this.status=4;g=k}0>g&&(b=l);break;case 1:this.status=3;this.k=ba;this.n=ca;this.status=4;break;case 2:var m;a:{var n=k,r=k,B=k,V=new (x?Uint8Array:Array)(K.length),W=k;this.status=3;H(this);n=I(this,5)+257;r=I(this,5)+1;B=I(this,4)+4;if(0>n||0>r||0>B)J(this),m=-1;else{try{for(var w=k,D=k,E=0,C=k,u=k,t=k,X=k,t=0;t<B;++t){if(0>(w=I(this,3)))throw Error("not enough input");
V[K[t]]=w}W=y(V);u=new (x?Uint8Array:Array)(n+r);t=0;for(X=n+r;t<X;){D=O(this,W);if(0>D)throw Error("not enough input");switch(D){case 16:if(0>(w=I(this,2)))throw Error("not enough input");for(C=3+w;C--;)u[t++]=E;break;case 17:if(0>(w=I(this,3)))throw Error("not enough input");for(C=3+w;C--;)u[t++]=0;E=0;break;case 18:if(0>(w=I(this,7)))throw Error("not enough input");for(C=11+w;C--;)u[t++]=0;E=0;break;default:E=u[t++]=D}}new (x?Uint8Array:Array)(n);new (x?Uint8Array:Array)(r);this.k=x?y(u.subarray(0,
n)):y(u.slice(0,n));this.n=x?y(u.subarray(n)):y(u.slice(n))}catch(pa){J(this);m=-1;break a}this.status=4;m=0}}0>m&&(b=l)}break;case 4:case 5:switch(this.h){case 0:var L;a:{var Y=this.input,F=this.a,M=this.b,G=this.c,N=this.m;for(this.status=5;N--;){G===M.length&&(M=P(this,{o:2}));if(F>=Y.length){this.a=F;this.c=G;this.m=N+1;L=-1;break a}M[G++]=Y[F++]}0>N&&(this.status=6);this.a=F;this.c=G;L=0}0>L&&(b=l);break;case 1:case 2:0>da(this)&&(b=l)}break;case 6:this.l?b=l:this.status=A}var Z,v=this.c,$;Z=
this.t?x?new Uint8Array(this.b.subarray(this.f,v)):this.b.slice(this.f,v):x?this.b.subarray(this.f,v):this.b.slice(this.f,v);this.f=v;v>32768+this.i&&(this.c=this.f=32768,x?($=this.b,this.b=new Uint8Array(this.i+32768),this.b.set($.subarray(v-32768,v))):this.b=this.b.slice(v-32768));return Z};
var ea=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],K=x?new Uint16Array(ea):ea,fa=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258],ga=x?new Uint16Array(fa):fa,ha=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],ia=x?new Uint8Array(ha):ha,ja=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],ka=x?new Uint16Array(ja):ja,la=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,
11,11,12,12,13,13],ma=x?new Uint8Array(la):la,Q=new (x?Uint8Array:Array)(288),R,na;R=0;for(na=Q.length;R<na;++R)Q[R]=143>=R?8:255>=R?9:279>=R?7:8;var ba=y(Q),S=new (x?Uint8Array:Array)(30),T,oa;T=0;for(oa=S.length;T<oa;++T)S[T]=5;var ca=y(S);function I(a,d){for(var b=a.e,c=a.d,e=a.input,g=a.a,f;c<d;){if(e.length<=g)return-1;f=e[g++];b|=f<<c;c+=8}f=b&(1<<d)-1;a.e=b>>>d;a.d=c-d;a.a=g;return f}
function O(a,d){for(var b=a.e,c=a.d,e=a.input,g=a.a,f=d[0],p=d[1],h,q,m;c<p;){if(e.length<=g)return-1;h=e[g++];b|=h<<c;c+=8}q=f[b&(1<<p)-1];m=q>>>16;if(m>c)throw Error("invalid code length: "+m);a.e=b>>m;a.d=c-m;a.a=g;return q&65535}function H(a){a.s=a.a;a.r=a.d;a.q=a.e}function J(a){a.a=a.s;a.d=a.r;a.e=a.q}
function da(a){var d=a.b,b=a.c,c,e,g,f,p=a.k,h=a.n,q=d.length,m;for(a.status=5;;){H(a);c=O(a,p);if(0>c)return a.c=b,J(a),-1;if(256===c)break;if(256>c)b===q&&(d=P(a),q=d.length),d[b++]=c;else{e=c-257;f=ga[e];if(0<ia[e]){m=I(a,ia[e]);if(0>m)return a.c=b,J(a),-1;f+=m}c=O(a,h);if(0>c)return a.c=b,J(a),-1;g=ka[c];if(0<ma[c]){m=I(a,ma[c]);if(0>m)return a.c=b,J(a),-1;g+=m}b+f>=q&&(d=P(a),q=d.length);for(;f--;)d[b]=d[b++-g];if(a.a===a.input.length)return a.c=b,-1}}for(;8<=a.d;)a.d-=8,a.a--;a.c=b;a.status=
6}function P(a,d){var b,c=a.input.length/a.a+1|0,e,g,f,p=a.input,h=a.b;d&&("number"===typeof d.o&&(c=d.o),"number"===typeof d.p&&(c+=d.p));2>c?(e=(p.length-a.a)/a.k[2],f=258*(e/2)|0,g=f<h.length?h.length+f:h.length<<1):g=h.length*c;x?(b=new Uint8Array(g),b.set(h)):b=h;a.b=b;return a.b};function U(a){this.input=a===k?new (x?Uint8Array:Array):a;this.a=0;this.g=new z(this.input,this.a);this.b=this.g.b}
U.prototype.j=function(a){var d;if(a!==k)if(x){var b=new Uint8Array(this.input.length+a.length);b.set(this.input,0);b.set(a,this.input.length);this.input=b}else this.input=this.input.concat(a);var c;if(c=this.method===k){var e;var g=this.a,f=this.input,p=f[g++],h=f[g++];if(p===k||h===k)e=-1;else{switch(p&15){case 8:this.method=8;break;default:throw Error("unsupported compression method");}if(0!==((p<<8)+h)%31)throw Error("invalid fcheck flag:"+((p<<8)+h)%31);if(h&32)throw Error("fdict flag is not supported");
this.a=g;e=k}c=0>e}if(c)return new (x?Uint8Array:Array);d=this.g.j(this.input,this.a);0!==this.g.a&&(this.input=x?this.input.subarray(this.g.a):this.input.slice(this.g.a),this.a=0);return d};s("Zlib.InflateStream",U);s("Zlib.InflateStream.prototype.decompress",U.prototype.j);}).call(this);
