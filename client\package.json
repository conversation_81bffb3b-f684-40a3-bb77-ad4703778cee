{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:staging": "VITE_APP_ENV=staging vite", "dev:prod": "VITE_APP_ENV=production vite", "build": "vite build", "build:staging": "VITE_APP_ENV=staging vite build", "build:prod": "VITE_APP_ENV=production vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@date-io/date-fns": "^3.2.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^6.3.0", "@mui/material": "^6.4.12", "@mui/styled-engine": "^6.3.0", "@mui/system": "^7.2.0", "@mui/x-date-pickers": "^7.29.4", "@tensorflow/tfjs": "^4.22.0", "archiver": "^7.0.1", "axios": "^1.10.0", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "face-api.js": "^0.22.2", "file-saver": "^2.0.5", "framer-motion": "^12.18.1", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "idb": "^8.0.3", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "localforage": "^1.10.0", "lucide-react": "^0.469.0", "motion-utils": "^12.23.6", "node-cron": "^3.0.3", "prop-types": "^15.8.1", "react": "^18.3.1", "react-csv": "^2.2.2", "react-dom": "^18.3.1", "react-i18next": "^15.5.2", "react-router-dom": "^7.1.1", "react-webcam": "^7.2.0", "recharts": "^2.15.4", "socket.io-client": "^4.8.1", "twilio": "^5.4.2", "vite-plugin-pwa": "^1.0.1", "workbox-window": "^7.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "vite": "^6.0.5"}}