/**
 * Environment-specific configurations
 */

// Dynamic API URL detection based on current host
const getCurrentApiUrl = () => {
  if (typeof window !== 'undefined') {
    const currentHost = window.location.host;
    const isPublicIP = currentHost.includes('**************');
    const isPort5174 = currentHost.includes(':5174');

    if (isPublicIP && isPort5174) {
      // Public IP access via port 5174 - use relative URLs through nginx proxy
      return '';
    }
  }
  // Default to localhost for development
  return 'http://127.0.0.1:5017';
};

const getCurrentMlApiUrl = () => {
  if (typeof window !== 'undefined') {
    const currentHost = window.location.host;
    const isPublicIP = currentHost.includes('**************');
    const isPort5174 = currentHost.includes(':5174');

    if (isPublicIP && isPort5174) {
      // Public IP access via port 5174 - use relative URLs through nginx proxy
      return '';
    }
  }
  // Default to localhost for development
  return 'http://127.0.0.1:5008';
};

export const environments = {
  development: {
    apiUrl: getCurrentApiUrl(),
    mlApiUrl: getCurrentMlApiUrl(),
    name: 'Development'
  },
  staging: {
    apiUrl: 'http://********:5017', // Internal network staging
    mlApiUrl: 'http://********:5001', // Internal network ML API
    name: 'Staging'
  },
  production: {
    apiUrl: 'http://********:5017', // Internal network production
    mlApiUrl: 'http://********:5001', // Internal network ML API
    name: 'Production'
  }
};