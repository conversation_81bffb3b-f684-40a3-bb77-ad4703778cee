<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test & Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 TechMan Login Test & Fix</h1>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <select id="email">
                <option value="<EMAIL>"><EMAIL></option>
                <option value="<EMAIL>"><EMAIL></option>
                <option value="<EMAIL>"><EMAIL></option>
                <option value="<EMAIL>"><EMAIL></option>
                <option value="<EMAIL>"><EMAIL></option>
                <option value="<EMAIL>"><EMAIL></option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="password123">
        </div>
        
        <button onclick="testLogin()">🧪 Test Login API</button>
        <button onclick="testAndRedirect()">🚀 Login & Redirect to Main App</button>
        <button onclick="clearStorage()">🗑️ Clear Browser Storage</button>
        
        <div id="result"></div>
    </div>

    <script>
        const resultDiv = document.getElementById('result');
        
        function showResult(message, type = 'info') {
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }
        
        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            showResult('🔄 Testing login...', 'info');
            
            try {
                const response = await fetch('http://localhost:5017/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok && (data.success || data.token)) {
                    showResult(`✅ LOGIN SUCCESS!\n\nResponse:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(`❌ LOGIN FAILED!\n\nStatus: ${response.status}\nResponse:\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(`❌ NETWORK ERROR!\n\nError: ${error.message}\n\nPossible causes:\n- Backend not running on port 5017\n- CORS issues\n- Network connectivity`, 'error');
            }
        }
        
        async function testAndRedirect() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            showResult('🔄 Logging in and redirecting...', 'info');
            
            try {
                const response = await fetch('http://localhost:5017/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok && (data.success || data.token)) {
                    // Store token
                    if (data.token) {
                        localStorage.setItem('token', data.token);
                        localStorage.setItem('user', JSON.stringify({
                            email: data.email,
                            role: data.role,
                            userId: data.userId,
                            userName: data.userName
                        }));
                    }
                    
                    showResult(`✅ LOGIN SUCCESS! Redirecting to main app...`, 'success');
                    
                    // Redirect to main app
                    setTimeout(() => {
                        window.location.href = 'http://localhost:5174/';
                    }, 2000);
                } else {
                    showResult(`❌ LOGIN FAILED!\n\nResponse:\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(`❌ ERROR: ${error.message}`, 'error');
            }
        }
        
        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            showResult('🗑️ Browser storage cleared!', 'info');
        }
        
        // Check current storage
        window.addEventListener('load', function() {
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            
            if (token) {
                showResult(`ℹ️ Current Storage:\nToken: ${token.substring(0, 50)}...\nUser: ${user}`, 'info');
            } else {
                showResult('ℹ️ No login token found in storage', 'info');
            }
        });
    </script>
</body>
</html>
