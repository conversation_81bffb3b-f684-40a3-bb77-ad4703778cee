<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Main App</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="debug-panel">
        <h1>🔧 TechMan Main App Debug</h1>
        
        <h2>Current Configuration</h2>
        <div id="config-info"></div>
        
        <h2>API Tests</h2>
        <button onclick="testRelativeAPI()">Test Relative API (/api/health)</button>
        <button onclick="testMainAppLogin()">Test Main App Login</button>
        <button onclick="checkMainAppFiles()">Check Main App Files</button>
        
        <div id="test-results"></div>
        
        <h2>Console Log</h2>
        <div id="console-log" class="log"></div>
    </div>

    <!-- Load the API override script -->
    <script src="/api-config-override.js"></script>

    <script>
        const logDiv = document.getElementById('console-log');
        const resultsDiv = document.getElementById('test-results');
        const configDiv = document.getElementById('config-info');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `${timestamp}: ${message}`;
            console.log(logMessage);
            logDiv.textContent += logMessage + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function showResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            resultsDiv.appendChild(div);
        }
        
        // Show current configuration
        function showConfig() {
            const config = {
                host: window.location.host,
                protocol: window.location.protocol,
                pathname: window.location.pathname,
                userAgent: navigator.userAgent.includes('Mobile') ? 'Mobile' : 'Desktop'
            };
            
            configDiv.innerHTML = `
                <div class="info">
                    <strong>Host:</strong> ${config.host}<br>
                    <strong>Protocol:</strong> ${config.protocol}<br>
                    <strong>Path:</strong> ${config.pathname}<br>
                    <strong>Device:</strong> ${config.userAgent}<br>
                    <strong>Token:</strong> ${localStorage.getItem('token') ? 'Present' : 'Missing'}
                </div>
            `;
        }
        
        async function testRelativeAPI() {
            log('Testing relative API call to /api/health');
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                log(`API Health Response: ${JSON.stringify(data)}`);
                showResult(`✅ Relative API works: ${data.message || 'OK'}`, 'success');
            } catch (error) {
                log(`API Health Error: ${error.message}`);
                showResult(`❌ Relative API failed: ${error.message}`, 'error');
            }
        }
        
        async function testMainAppLogin() {
            log('Testing main app login flow');
            try {
                const payload = {
                    email: '<EMAIL>',
                    password: 'GopiTanneeru'
                };
                
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(payload)
                });
                
                const data = await response.json();
                log(`Main App Login Response: ${JSON.stringify(data)}`);
                
                if (data.success || data.token) {
                    localStorage.setItem('token', data.token);
                    showResult(`✅ Main app login works!`, 'success');
                } else {
                    showResult(`❌ Main app login failed: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`Main App Login Error: ${error.message}`);
                showResult(`❌ Main app login error: ${error.message}`, 'error');
            }
        }
        
        async function checkMainAppFiles() {
            log('Checking main app files');
            try {
                // Check if main app files exist
                const files = ['/', '/login', '/dashboard'];
                
                for (const file of files) {
                    try {
                        const response = await fetch(file);
                        log(`File ${file}: ${response.status} ${response.statusText}`);
                        if (response.ok) {
                            showResult(`✅ ${file} accessible`, 'success');
                        } else {
                            showResult(`❌ ${file} not accessible (${response.status})`, 'error');
                        }
                    } catch (error) {
                        log(`File ${file} error: ${error.message}`);
                        showResult(`❌ ${file} error: ${error.message}`, 'error');
                    }
                }
            } catch (error) {
                log(`File check error: ${error.message}`);
                showResult(`❌ File check failed: ${error.message}`, 'error');
            }
        }
        
        // Initialize
        showConfig();
        log('Debug page loaded');
        
        // Test API override is working
        setTimeout(() => {
            log('Checking if API override is active...');
            if (window.fetch.toString().includes('modifiedUrl')) {
                log('✅ API override is active');
                showResult('✅ API override script loaded', 'success');
            } else {
                log('❌ API override not detected');
                showResult('❌ API override script not working', 'error');
            }
        }, 1000);
    </script>
</body>
</html>
