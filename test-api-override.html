<!DOCTYPE html>
<html>
<head>
    <title>API Override Test</title>
    <script src="/api-config-override.js"></script>
</head>
<body>
    <h1>API Override Test</h1>
    <button onclick="testLogin()">Test Login API</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                console.log('Testing login API call...');
                
                const response = await fetch('http://127.0.0.1:5017/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'GopiTanneeru'
                    })
                });
                
                const data = await response.json();
                console.log('Response:', data);
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <h3>✅ Login Successful!</h3>
                        <p><strong>Role:</strong> ${data.role}</p>
                        <p><strong>Email:</strong> ${data.email}</p>
                        <p><strong>Token:</strong> ${data.token ? data.token.substring(0, 50) + '...' : 'None'}</p>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3>❌ Login Failed</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Message:</strong> ${data.message || 'Unknown error'}</p>
                    `;
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <h3>❌ Network Error</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
