@echo off
echo ========================================
echo TechMan Network Access Fix
echo ========================================
echo.

echo ✅ Diagnosis Complete - Services are running locally!
echo.
echo 📊 Current Status:
echo   - All services running: ✅
echo   - Local access working: ✅  
echo   - Public IP access: ❌ (Router configuration needed)
echo.

echo ========================================
echo Network Configuration Options
echo ========================================
echo.

echo Your current network setup:
echo   Internal IP: ********
echo   Public IP:   **************
echo.

echo 🌐 Access URLs that WORK RIGHT NOW:
echo.
echo   ✅ Local Machine Access:
echo      http://localhost
echo      http://localhost/api/health
echo      http://localhost/pyapi/health
echo.
echo   ✅ Internal Network Access:
echo      http://********
echo      http://********/api/health  
echo      http://********/pyapi/health
echo.

echo Testing internal network access...
powershell -Command "try { $result = Invoke-WebRequest -Uri 'http://********/health' -UseBasicParsing; Write-Host '✅ Internal Network:' $result.StatusCode } catch { Write-Host '❌ Internal Network: Failed' }"

echo.
echo ========================================
echo For Public Internet Access
echo ========================================
echo.

echo To make your application accessible from the internet via **************:
echo.
echo 🔧 Router Configuration Required:
echo   1. Access your router admin panel (usually http://***********)
echo   2. Find "Port Forwarding" or "Virtual Server" settings
echo   3. Add these port forwarding rules:
echo.
echo      External Port → Internal IP:Port
echo      80 → ********:80     (Main application)
echo      5001 → ********:5001 (Python API)
echo      5017 → ********:5017 (Backend API)
echo.
echo 📋 Alternative: Use Internal IP for Network Access
echo.
echo   If you only need access from your local network:
echo   ✅ Use: http://********
echo   ✅ This works immediately without router changes
echo.

echo ========================================
echo Quick Network Test
echo ========================================
echo.

echo Testing all working URLs...
echo.

echo 1. Testing localhost...
powershell -Command "try { $result = Invoke-WebRequest -Uri 'http://localhost/' -UseBasicParsing; Write-Host '✅ Localhost Frontend:' $result.StatusCode } catch { Write-Host '❌ Localhost Frontend: Failed' }"

echo.
echo 2. Testing internal IP...
powershell -Command "try { $result = Invoke-WebRequest -Uri 'http://********/' -UseBasicParsing; Write-Host '✅ Internal IP Frontend:' $result.StatusCode } catch { Write-Host '❌ Internal IP Frontend: Failed' }"

echo.
echo 3. Testing API endpoints...
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://********/api/health' -Method Get; Write-Host '✅ Internal Backend API:' $result.status } catch { Write-Host '❌ Internal Backend API: Failed' }"

powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://********/pyapi/health' -Method Get; Write-Host '✅ Internal Python API:' $result.status } catch { Write-Host '❌ Internal Python API: Failed' }"

echo.
echo ========================================
echo ✅ Summary
echo ========================================
echo.
echo 🎉 Your TechMan application is WORKING!
echo.
echo 📱 Access your application now:
echo   http://********        (Full application)
echo   http://localhost       (Local access)
echo.
echo 🔧 For public internet access (**************):
echo   Configure router port forwarding as shown above
echo.
echo 🌐 Your application includes:
echo   ✅ React Frontend
echo   ✅ Node.js Backend API  
echo   ✅ Python Model API
echo   ✅ MongoDB Database
echo   ✅ Nginx Reverse Proxy
echo.

pause

echo.
echo Opening your application in browser...
start http://********
