@echo off
echo ========================================
echo Configure Python API for Public Access
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    echo.
    echo This is required to configure Windows Firewall rules.
    pause
    exit /b 1
)

echo ✅ Running with administrator privileges
echo.

echo ========================================
echo Configuring Windows Firewall
echo ========================================
echo.

REM Remove existing rule if it exists
echo Removing any existing firewall rules for port 5001...
netsh advfirewall firewall delete rule name="Python API Port 5001" >nul 2>&1
netsh advfirewall firewall delete rule name="TechMan Python API" >nul 2>&1

REM Add new firewall rule for Python API
echo Adding firewall rule for Python API (port 5001)...
netsh advfirewall firewall add rule name="TechMan Python API" dir=in action=allow protocol=TCP localport=5001
if %errorlevel% equ 0 (
    echo ✅ Firewall rule added successfully
) else (
    echo ❌ Failed to add firewall rule
    pause
    exit /b 1
)

REM Also add outbound rule for completeness
echo Adding outbound firewall rule...
netsh advfirewall firewall add rule name="TechMan Python API Outbound" dir=out action=allow protocol=TCP localport=5001

echo.
echo ========================================
echo Network Configuration Check
echo ========================================
echo.

echo Checking current network configuration...
ipconfig | findstr "IPv4"
echo.

echo Checking if port 5001 is in use...
netstat -an | findstr ":5001"
echo.

echo ========================================
echo Environment Configuration
echo ========================================
echo.

REM Set environment variables for public access
set API_HOST=0.0.0.0
set PYTHON_API_PORT=5001
set CORS_ORIGINS=http://localhost:5175,http://localhost:5174,http://localhost:3000,http://localhost:5173,http://**************,http://**************:80,http://**************:5174

echo ✅ API_HOST=%API_HOST%
echo ✅ PYTHON_API_PORT=%PYTHON_API_PORT%
echo ✅ CORS_ORIGINS=%CORS_ORIGINS%
echo.

echo ========================================
echo Testing Network Connectivity
echo ========================================
echo.

echo Testing local connectivity...
powershell -Command "try { Invoke-RestMethod -Uri 'http://localhost:5001/api/health' -Method Get -TimeoutSec 5 | Out-Null; Write-Host '✅ Local access working' } catch { Write-Host '❌ Local access failed' }"

echo.
echo Testing public IP connectivity (if API is running)...
powershell -Command "try { Invoke-RestMethod -Uri 'http://**************:5001/api/health' -Method Get -TimeoutSec 10 | Out-Null; Write-Host '✅ Public IP access working' } catch { Write-Host '⚠️  Public IP access not yet available (API may need restart)' }"

echo.
echo ========================================
echo ✅ Configuration Complete!
echo ========================================
echo.
echo 🔥 Firewall Rules Added:
echo   - Inbound: TCP Port 5001 (TechMan Python API)
echo   - Outbound: TCP Port 5001 (TechMan Python API Outbound)
echo.
echo 🌐 Your Python API should now be accessible at:
echo   Local:  http://localhost:5001/api/health
echo   Public: http://**************:5001/api/health
echo.
echo 📋 Next Steps:
echo   1. Restart the Python API if it's currently running
echo   2. Use: .\start-single-api.bat
echo   3. Test public access: .\test-python-api.bat
echo.
echo 🔧 If public access still doesn't work, check:
echo   - Router/ISP firewall settings
echo   - Network adapter configuration
echo   - Antivirus software blocking connections
echo.
pause
