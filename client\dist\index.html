<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <!-- The preconnect link will be set dynamically by JavaScript based on the environment -->
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
    <meta name="description" content="Mantena - Inventory Management System" />
    <meta name="theme-color" content="#1976d2" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Mantena IMS" />
    <title>Mantena - Inventory Management System</title>

    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Responsive design optimization -->
    <style>
      /* Prevent layout shift during loading */
      #root {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }

      /* Improve touch targets for mobile */
      @media (max-width: 768px) {
        button, a, [role="button"] {
          min-height: 44px;
          min-width: 44px;
        }
      }
    </style>
    <!-- TechMan API Configuration Override - Load before main app -->
    <script src="/api-config-override.js"></script>
    <script type="module" crossorigin src="/assets/index-CKnKIjUf.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-7pczKVeg.css">
  <link rel="manifest" href="/manifest.webmanifest"><script id="vite-plugin-pwa:register-sw" src="/registerSW.js"></script></head>
  <body>
    <div id="root"></div>
  </body>
</html>
