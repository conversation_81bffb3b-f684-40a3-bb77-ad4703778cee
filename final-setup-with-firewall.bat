@echo off
echo ========================================
echo TechMan Final Setup with Firewall
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ This script must be run as Administrator!
    echo.
    echo 📋 HOW TO RUN AS ADMINISTRATOR:
    echo 1. Right-click on this file: final-setup-with-firewall.bat
    echo 2. Select "Run as administrator"
    echo 3. Click "Yes" when prompted
    echo.
    echo ⚠️  This is REQUIRED to configure firewall for public IP access
    pause
    exit /b 1
)

echo ✅ Running with administrator privileges
echo.

echo ========================================
echo Step 1: Configure Firewall Rules
echo ========================================
echo.

echo Adding firewall rules for all TechMan services...

REM Port 80 for nginx
netsh advfirewall firewall delete rule name="TechMan Nginx" >nul 2>&1
netsh advfirewall firewall add rule name="TechMan Nginx" dir=in action=allow protocol=TCP localport=80
echo ✅ Port 80 (Nginx) configured

REM Port 5001 for Python API
netsh advfirewall firewall delete rule name="TechMan Python API" >nul 2>&1
netsh advfirewall firewall add rule name="TechMan Python API" dir=in action=allow protocol=TCP localport=5001
echo ✅ Port 5001 (Python API) configured

REM Port 5017 for Node.js Backend
netsh advfirewall firewall delete rule name="TechMan Backend" >nul 2>&1
netsh advfirewall firewall add rule name="TechMan Backend" dir=in action=allow protocol=TCP localport=5017
echo ✅ Port 5017 (Node.js Backend) configured

echo.
echo ========================================
echo Step 2: Stop Existing Nginx Processes
echo ========================================
echo.

echo Stopping any existing nginx processes...
taskkill /f /im nginx.exe >nul 2>&1
echo ✅ Nginx processes stopped

echo.
echo ========================================
echo Step 3: Start System Nginx
echo ========================================
echo.

echo Starting system nginx with TechMan configuration...
cd /d C:\nginx
start "TechMan Nginx" nginx.exe

echo Waiting for nginx to start...
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo Step 4: Test All Services
echo ========================================
echo.

echo Testing nginx health...
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://localhost/health' -Method Get; Write-Host '✅ Nginx Health:' $result } catch { Write-Host '❌ Nginx Health: Failed' }"

echo.
echo Testing backend API through nginx...
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://localhost/api/health' -Method Get; Write-Host '✅ Backend API:' $result.status } catch { Write-Host '❌ Backend API: Failed' }"

echo.
echo Testing Python API through nginx...
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://localhost/pyapi/health' -Method Get; Write-Host '✅ Python API:' $result.status } catch { Write-Host '❌ Python API: Failed' }"

echo.
echo Testing Python API direct access (public IP)...
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://**************:5001/api/health' -Method Get -TimeoutSec 10; Write-Host '✅ Python API Public:' $result.status } catch { Write-Host '⚠️  Python API Public: May need router configuration' }"

echo.
echo ========================================
echo ✅ Setup Complete!
echo ========================================
echo.
echo 🌐 Your TechMan application is now accessible at:
echo.
echo   📱 Main Application:
echo     http://localhost
echo     http://************** (public IP)
echo.
echo   🔧 API Endpoints:
echo     Backend API:  http://localhost/api/health
echo     Python API:   http://localhost/pyapi/health
echo.
echo   🌍 Direct API Access:
echo     Backend:      http://**************:5017/api/health
echo     Python API:   http://**************:5001/api/health
echo.
echo 📊 Service Architecture:
echo   ✅ Nginx (Port 80) → Reverse Proxy
echo   ├── Frontend: React app from client/dist
echo   ├── /api/* → Node.js Backend (Port 5017)
echo   └── /pyapi/* → Python API (Port 5001)
echo.
echo 🔥 Firewall Rules Added:
echo   ✅ Port 80 (Nginx)
echo   ✅ Port 5001 (Python API)
echo   ✅ Port 5017 (Node.js Backend)
echo.
echo 🛠️ Management Commands:
echo   Stop nginx:   taskkill /f /im nginx.exe
echo   Start nginx:  cd C:\nginx && nginx.exe
echo   Reload nginx: cd C:\nginx && nginx.exe -s reload
echo   Test config:  cd C:\nginx && nginx.exe -t
echo.
pause
