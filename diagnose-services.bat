@echo off
echo ========================================
echo TechMan Service Diagnostics
echo ========================================
echo.

echo ========================================
echo 1. Port Status Check
echo ========================================
echo.

echo Checking which ports are listening...
netstat -an | findstr ":80 :5001 :5017 :27017" | findstr LISTENING

echo.
echo ========================================
echo 2. Process Status Check
echo ========================================
echo.

echo Python processes:
tasklist | findstr python

echo.
echo Node.js processes:
tasklist | findstr node

echo.
echo Nginx processes:
tasklist | findstr nginx

echo.
echo ========================================
echo 3. Local Service Tests
echo ========================================
echo.

echo Testing Nginx locally...
powershell -Command "try { $result = Invoke-WebRequest -Uri 'http://localhost/health' -UseBasicParsing; Write-Host '✅ Nginx Local:' $result.StatusCode } catch { Write-Host '❌ Nginx Local: Failed -' $_.Exception.Message }"

echo.
echo Testing Python API locally...
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://localhost:5001/api/health' -Method Get; Write-Host '✅ Python Local:' $result.status } catch { Write-Host '❌ Python Local: Failed -' $_.Exception.Message }"

echo.
echo Testing Backend API locally...
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://localhost:5017/api/health' -Method Get; Write-Host '✅ Backend Local:' $result.status } catch { Write-Host '❌ Backend Local: Failed -' $_.Exception.Message }"

echo.
echo Testing Frontend locally...
powershell -Command "try { $result = Invoke-WebRequest -Uri 'http://localhost/' -UseBasicParsing; Write-Host '✅ Frontend Local:' $result.StatusCode } catch { Write-Host '❌ Frontend Local: Failed -' $_.Exception.Message }"

echo.
echo ========================================
echo 4. Public IP Tests
echo ========================================
echo.

echo Testing Nginx on public IP...
powershell -Command "try { $result = Invoke-WebRequest -Uri 'http://**************/health' -UseBasicParsing -TimeoutSec 5; Write-Host '✅ Nginx Public:' $result.StatusCode } catch { Write-Host '❌ Nginx Public: Failed -' $_.Exception.Message }"

echo.
echo Testing Python API on public IP...
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://**************:5001/api/health' -Method Get -TimeoutSec 5; Write-Host '✅ Python Public:' $result.status } catch { Write-Host '❌ Python Public: Failed -' $_.Exception.Message }"

echo.
echo Testing Backend API on public IP...
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://**************:5017/api/health' -Method Get -TimeoutSec 5; Write-Host '✅ Backend Public:' $result.status } catch { Write-Host '❌ Backend Public: Failed -' $_.Exception.Message }"

echo.
echo Testing Frontend on public IP...
powershell -Command "try { $result = Invoke-WebRequest -Uri 'http://**************/' -UseBasicParsing -TimeoutSec 5; Write-Host '✅ Frontend Public:' $result.StatusCode } catch { Write-Host '❌ Frontend Public: Failed -' $_.Exception.Message }"

echo.
echo ========================================
echo 5. Firewall Rules Check
echo ========================================
echo.

echo Checking firewall rules...
netsh advfirewall firewall show rule name="TechMan Python API" | findstr "Enabled"
netsh advfirewall firewall show rule name="TechMan Nginx" | findstr "Enabled"
netsh advfirewall firewall show rule name="TechMan Backend" | findstr "Enabled"

echo.
echo ========================================
echo 6. Network Interface Check
echo ========================================
echo.

echo Your IP addresses:
ipconfig | findstr "IPv4"

echo.
echo ========================================
echo Diagnosis Summary
echo ========================================
echo.

echo If LOCAL tests pass but PUBLIC tests fail:
echo   → Router needs port forwarding configuration
echo   → ISP may be blocking incoming connections
echo   → Check router admin panel for port forwarding
echo.

echo If LOCAL tests fail:
echo   → Services not running properly
echo   → Configuration issues
echo   → Run the setup script again as Administrator
echo.

echo If FRONTEND fails but APIs work:
echo   → Frontend build missing or corrupted
echo   → Nginx serving wrong directory
echo   → Check client/dist folder exists
echo.

pause
