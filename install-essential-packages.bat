@echo off
echo ========================================
echo Installing Essential Python Packages
echo ========================================
echo.

REM Set the project directory
set PROJECT_DIR=%~dp0
set PYTHON_DIR=%PROJECT_DIR%python-model

echo ✅ Project Directory: %PROJECT_DIR%
echo ✅ Python Model Directory: %PYTHON_DIR%
echo.

REM Change to python-model directory
cd /d "%PYTHON_DIR%"

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo Creating virtual environment...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created
)

echo.
echo ========================================
echo Activating Virtual Environment
echo ========================================
echo.

REM Activate virtual environment
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ Failed to activate virtual environment
    pause
    exit /b 1
)

echo ✅ Virtual environment activated
echo.

REM Upgrade pip first
echo Upgrading pip...
python -m pip install --upgrade pip
echo.

echo ========================================
echo Installing Essential Packages Only
echo ========================================
echo.

echo Installing Flask and web framework...
pip install flask flask-cors python-dotenv
echo.

echo Installing MongoDB drivers...
pip install pymongo motor dnspython
echo.

echo Installing basic utilities...
pip install requests beautifulsoup4 python-dateutil
echo.

echo Installing document processing...
pip install PyPDF2 python-docx lxml
echo.

echo Installing basic data processing...
pip install --no-deps pandas
pip install --no-deps numpy
echo.

echo Installing computer vision (basic)...
pip install --no-deps opencv-python
pip install --no-deps pillow
echo.

echo ========================================
echo Testing Core Imports
echo ========================================
echo.

echo Testing essential imports...
python -c "import flask, pymongo, requests; print('✅ Core web packages imported successfully')"
if %errorlevel% neq 0 (
    echo ❌ Core package import failed
    pause
    exit /b 1
)

python -c "import PyPDF2, docx; print('✅ Document processing packages imported successfully')"
if %errorlevel% neq 0 (
    echo ⚠️  Some document processing packages may not be available
)

echo.
echo ========================================
echo ✅ Essential Packages Installation Complete!
echo ========================================
echo.
echo Ready to start the Python API!
echo.
pause
