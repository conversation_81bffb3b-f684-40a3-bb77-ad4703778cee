const axios = require('axios');

async function testLogin() {
  try {
    console.log('🧪 Testing login with your credentials...');
    
    const response = await axios.post('http://localhost:5017/api/auth/login', {
      email: '<EMAIL>',
      password: 'GopiTanneeru'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Login successful!');
    console.log('📊 Response:', response.data);
    
    if (response.data.token) {
      console.log('🔑 JWT Token received:', response.data.token.substring(0, 50) + '...');
    }
    
  } catch (error) {
    console.log('❌ Login failed');
    if (error.response) {
      console.log('📊 Status:', error.response.status);
      console.log('📊 Response:', error.response.data);
    } else {
      console.log('📊 Error:', error.message);
    }
  }
}

testLogin();
