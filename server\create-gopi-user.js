const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('./models/User');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/ims', {
  useNewUrlParser: true,
  useUnifiedTopology: true
}).then(() => {
  console.log('✅ Connected to MongoDB');
  createGopiUser();
}).catch(err => {
  console.error('❌ MongoDB connection error:', err);
  process.exit(1);
});

async function createGopiUser() {
  try {
    // Check if user already exists
    const existingUser = await User.findOne({ email: '<EMAIL>' });
    
    if (existingUser) {
      console.log('👤 User <EMAIL> already exists');
      console.log('📧 Email:', existingUser.email);
      console.log('👤 Name:', existingUser.name);
      console.log('🔑 Role:', existingUser.role);
      console.log('✅ Active:', existingUser.active);
      
      // Update password to ensure it matches
      const salt = await bcrypt.genSalt(12);
      const hashedPassword = await bcrypt.hash('GopiTanneeru', salt);
      
      existingUser.password = hashedPassword;
      existingUser.active = true;
      await existingUser.save();
      
      console.log('🔄 Password updated for existing user');
    } else {
      // Create new user
      const salt = await bcrypt.genSalt(12);
      const hashedPassword = await bcrypt.hash('GopiTanneeru', salt);
      
      const newUser = new User({
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Gopi Tanneeru',
        role: 'admin',
        active: true
      });
      
      await newUser.save();
      console.log('✅ Created new user: <EMAIL>');
      console.log('📧 Email: <EMAIL>');
      console.log('👤 Name: Gopi Tanneeru');
      console.log('🔑 Role: admin');
      console.log('🔒 Password: GopiTanneeru');
    }
    
    // Test login credentials
    console.log('\n🧪 Testing login credentials...');
    const testUser = await User.findOne({ email: '<EMAIL>' }).select('+password');
    
    if (testUser) {
      const isMatch = await bcrypt.compare('GopiTanneeru', testUser.password);
      if (isMatch) {
        console.log('✅ Password verification successful!');
        console.log('🎉 User is ready for login');
      } else {
        console.log('❌ Password verification failed');
      }
    }
    
    console.log('\n📊 User Summary:');
    console.log('Email: <EMAIL>');
    console.log('Password: GopiTanneeru');
    console.log('Role: admin');
    console.log('Status: Ready for login');
    
  } catch (error) {
    console.error('❌ Error creating user:', error);
  } finally {
    mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
}
