@echo off
echo 🚀 Starting TechMan Application...
echo.

echo 📊 Starting Node.js Backend (Port 5017)...
start "TechMan Backend" cmd /k "cd server && npm start"
timeout /t 3

echo 🐍 Starting Python ML Service (Port 5001)...
start "TechMan Python" cmd /k "cd python-model && python single_api.py"
timeout /t 3

echo 🌐 Starting Frontend Development Server (Port 5174)...
start "TechMan Frontend" cmd /k "cd client && npm run dev"
timeout /t 3

echo.
echo ✅ All TechMan services are starting...
echo.
echo 🌍 Access URLs:
echo   - Main App: http://localhost:5174/
echo   - Network:  http://********:5174/
echo   - Public:   http://**************:5174/
echo.
echo 📋 Login Credentials:
echo   - Email: <EMAIL> (or any other superadmin)
echo   - Password: password123
echo.
echo Press any key to exit...
pause > nul
