@echo off
echo ========================================
echo TechMan Production Startup with Nginx
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Not running as administrator. Nginx may not start properly.
    echo For best results, run as administrator.
    echo.
)

REM Set variables
set NGINX_DIR=C:\nginx
set PROJECT_DIR=%~dp0

echo ✅ Project Directory: %PROJECT_DIR%
echo ✅ Nginx Directory: %NGINX_DIR%
echo.

REM Check if MongoDB is running
echo Checking MongoDB service...
sc query MongoDB >nul 2>&1
if %errorlevel% neq 0 (
    echo Starting MongoDB service...
    net start MongoDB
    timeout /t 5 /nobreak >nul
) else (
    echo ✅ MongoDB is already running
)

echo.
echo ========================================
echo Starting TechMan Backend Services
echo ========================================
echo.

REM Stop any existing nginx processes
echo Stopping any existing nginx processes...
taskkill /f /im nginx.exe >nul 2>&1

REM Stop any existing node processes for our app
echo Stopping any existing TechMan processes...
for /f "tokens=2" %%i in ('tasklist /fi "windowtitle eq TechMan*" /fo csv ^| findstr /v "INFO:"') do (
    if not "%%i"=="PID" taskkill /f /pid %%i >nul 2>&1
)

REM Start Backend Server
echo Starting Backend Server on port 5017...
cd /d "%PROJECT_DIR%server"
start "TechMan Backend" cmd /k "npm run start"

REM Wait for backend to start
echo Waiting for backend to initialize...
timeout /t 5 /nobreak >nul

REM Start Python API (Single API)
echo Starting Python API on port 5001...
cd /d "%PROJECT_DIR%python-model"
call venv\Scripts\activate.bat
start "TechMan Python API" cmd /k "python single_api.py"

REM Wait for Python API to start
echo Waiting for Python API to initialize...
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo Starting Nginx Reverse Proxy
echo ========================================
echo.

REM Check if nginx exists
if not exist "%NGINX_DIR%\nginx.exe" (
    echo ERROR: Nginx not found at %NGINX_DIR%\nginx.exe
    echo Please run setup-nginx-windows.bat first to install nginx.
    echo.
    pause
    exit /b 1
)

REM Copy our nginx configuration
echo Copying TechMan nginx configuration...
copy "%PROJECT_DIR%nginx-techman.conf" "%NGINX_DIR%\conf\nginx.conf" /Y

REM Test nginx configuration
echo Testing nginx configuration...
cd /d "%NGINX_DIR%"
nginx.exe -t
if %errorlevel% neq 0 (
    echo ERROR: Nginx configuration test failed!
    echo Please check the configuration file.
    pause
    exit /b 1
)

echo ✅ Nginx configuration test passed

REM Start nginx
echo Starting nginx...
nginx.exe
if %errorlevel% neq 0 (
    echo ERROR: Failed to start nginx!
    pause
    exit /b 1
)

echo ✅ Nginx started successfully

echo.
echo ========================================
echo ✅ TechMan Production Environment Ready!
echo ========================================
echo.
echo 🌐 Access URLs:
echo.
echo Public Access:
echo   Main Application: http://**************
echo   Health Check:     http://**************/health
echo.
echo Local Access:
echo   Main Application: http://localhost
echo   Health Check:     http://localhost/health
echo.
echo Direct Service Access (for debugging):
echo   Backend API:      http://**************:5017/api/health
echo   Python API:       http://**************:5001/api/health
echo.
echo 📊 Service Status:
echo   ✅ MongoDB: Running
echo   ✅ Node.js Backend: Running on port 5017
echo   ✅ Python API: Running on port 5001
echo   ✅ Nginx Reverse Proxy: Running on port 80
echo   ✅ Frontend: Served via Nginx
echo.
echo 🔧 Management Commands:
echo   Stop nginx:    cd C:\nginx && nginx.exe -s stop
echo   Reload nginx:  cd C:\nginx && nginx.exe -s reload
echo   View logs:     cd C:\nginx\logs && type access.log
echo.
echo Press any key to continue...
pause >nul
