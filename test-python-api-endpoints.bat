@echo off
echo ========================================
echo Testing TechMan Python API Endpoints
echo ========================================
echo.

echo Testing Local Access...
echo.

echo 1. Health Check (Local)
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://localhost:5001/api/health' -Method Get; Write-Host '✅ Health Check: PASSED'; Write-Host 'Status:' $result.status; Write-Host 'Port:' $result.port } catch { Write-Host '❌ Health Check: FAILED -' $_.Exception.Message }"
echo.

echo 2. Vendors Endpoint (Local)
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://localhost:5001/api/procurement/vendors' -Method Get; Write-Host '✅ Vendors: PASSED'; Write-Host 'Vendors found:' $result.data.Count } catch { Write-Host '❌ Vendors: FAILED -' $_.Exception.Message }"
echo.

echo 3. Procurement Requests (Local)
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://localhost:5001/api/procurement/requests' -Method Get; Write-Host '✅ Requests: PASSED'; Write-Host 'Requests found:' $result.data.Count } catch { Write-Host '❌ Requests: FAILED -' $_.Exception.Message }"
echo.

echo Testing Internal Network Access...
echo.

echo 4. Health Check (Internal IP)
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://********:5001/api/health' -Method Get; Write-Host '✅ Internal Health Check: PASSED'; Write-Host 'Status:' $result.status } catch { Write-Host '❌ Internal Health Check: FAILED -' $_.Exception.Message }"
echo.

echo 5. Vendors (Internal IP)
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://********:5001/api/procurement/vendors' -Method Get; Write-Host '✅ Internal Vendors: PASSED'; Write-Host 'Vendors found:' $result.data.Count } catch { Write-Host '❌ Internal Vendors: FAILED -' $_.Exception.Message }"
echo.

echo Testing Public IP Access (if firewall configured)...
echo.

echo 6. Health Check (Public IP) - May require firewall configuration
powershell -Command "try { $result = Invoke-RestMethod -Uri 'http://**************:5001/api/health' -Method Get -TimeoutSec 10; Write-Host '✅ Public Health Check: PASSED'; Write-Host 'Status:' $result.status } catch { Write-Host '⚠️  Public Health Check: FAILED - May need firewall configuration' }"
echo.

echo Testing Chatbot Endpoint...
echo.

echo 7. Chatbot Test
powershell -Command "try { $headers = @{'Content-Type'='application/json'}; $body = '{\"message\":\"hello\"}'; $result = Invoke-RestMethod -Uri 'http://localhost:5001/api/chatbot/chat' -Method Post -Body $body -Headers $headers; Write-Host '✅ Chatbot: PASSED'; Write-Host 'Response:' $result.response.Substring(0, [Math]::Min(100, $result.response.Length)) } catch { Write-Host '❌ Chatbot: FAILED -' $_.Exception.Message }"
echo.

echo ========================================
echo API Test Summary
echo ========================================
echo.
echo ✅ If all tests passed, your Python API is working correctly!
echo.
echo 🌐 Your API is accessible at:
echo   Local:    http://localhost:5001
echo   Internal: http://********:5001
echo   Public:   http://**************:5001 (requires firewall config)
echo.
echo 📋 Available endpoints:
echo   /api/health - Health check
echo   /api/procurement/vendors - Vendor management
echo   /api/procurement/requests - Procurement requests
echo   /api/chatbot/chat - AI chatbot
echo.
echo 🔧 If public access fails, run as Administrator:
echo   .\configure-python-public-access.bat
echo.
pause
